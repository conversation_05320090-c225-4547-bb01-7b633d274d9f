<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />

    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>通知</title>
    <link rel="stylesheet" href="vue/element/<EMAIL>" />
    <link rel="stylesheet" href="css/css-comment.css" />
    <link
      rel="stylesheet"
      href="https://at.alicdn.com/t/font_1156348_lrijkzmtfh.css"
    />
    <link rel="stylesheet" href="css/tongzhi.css" />
    <link rel="stylesheet" href="component/css/component.css" />
    <link rel="stylesheet" href="css/print.css" />
  </head>

  <body>
    <div id="app" v-cloak>
      <!--顶部导航条-->
      <!--主体-->
      <div
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.7)"
      >
        <div class="main" v-if="datas.length>0">
          <div class="tz_left" style="padding-top: 20px">
            <div
              v-infinite-scroll="loadMore"
              infinite-scroll-disabled="isScroll"
              infinite-scroll-distance="10"
              infinite-scroll-immediate-check="isScroll"
            >
              <div
                class="tz_left1"
                v-for="(item,index) in datas"
                style="border: 1px solid white"
                @click="add_tz_yuyue_message1(item,index)"
                :class="listIndex==index?'listActive':''"
              >
                <div class="tz_left1_line1">
                  <div class="tz_left1_line1_font1" style="font-weight: bolder">
                    {{item.title}}
                  </div>
                </div>
                <div class="tz_left1_line2">
                  <div style="font-size: 14px; line-height: 16px">
                    {{item.content}}
                    <!-- <span v-if="noticeDetails.state == 1" class="tz_left1_line2_font4">预约人：{{item.listbooker.shoper}} 到店时间：{{item.listbooker.arrdate}}</span>
                                <span v-if="noticeDetails.state == 2" class="tz_left1_line2_font4">预约人：{{item.listbooker.vipName}} 到店时间：{{item.listbooker.order_time}}</span> -->
                  </div>
                </div>
                <div
                  class="tz_left1_line2_font3"
                  style="text-align: right; margin-top: 5px"
                >
                  {{item.Time}}
                </div>
              </div>
            </div>
            <div
              @click="loadMore"
              class="loadMoreBtn"
              v-show="allCount != datas.length"
            >
              加载更多
            </div>
            <div
              class="loadMoreBtn"
              v-if="allCount > 10 && allCount == datas.length"
            >
              我也是有底线的
            </div>
          </div>

          <div class="tz_right" v-if="datas.length>0">
            <div class="notice-body">
              <div class="tz_right_title">
                <div class="tz_right_title_font1">标题</div>
                <div class="tz_right_title_font2">{{noticeDetails.title}}</div>
              </div>
              <div class="tz_right_neirong">
                <div class="tz_right_neirong_font1">内容</div>
                <div class="tz_right_neirong_font22">
                  {{noticeDetails.content}}
                </div>
                <ul
                  class="tz_right_neirong_font2"
                  v-if="noticeDetails.state == 1"
                >
                  <li
                    class="tz_right_neirong_font22"
                    v-if="noticeDetails.listbooker"
                  >
                    预约人：{{noticeDetails.listbooker.shoper}}
                  </li>
                  <li
                    class="tz_right_neirong_font22"
                    v-if="noticeDetails.listbooker"
                  >
                    到店时间：{{noticeDetails.listbooker.arrdate}}
                  </li>
                  <li
                    class="tz_right_neirong_font22"
                    v-if="noticeDetails.listbooker"
                  >
                    服务项目：{{noticeDetails.listbooker.name}}
                  </li>
                  <li
                    class="tz_right_neirong_font22"
                    v-if="noticeDetails.listbooker"
                  >
                    服务
                    <technician-name></technician-name>
                    ：{{noticeDetails.listbooker.staffName}}
                  </li>
                </ul>
                <ul
                  class="tz_right_neirong_font2"
                  v-if="noticeDetails.state == 2"
                >
                  <li
                    class="tz_right_neirong_font22"
                    v-if="noticeDetails.listbooker"
                  >
                    下单人：{{noticeDetails.listbooker.vipName}}
                  </li>
                  <li
                    class="tz_right_neirong_font22"
                    v-if="noticeDetails.listbooker"
                  >
                    下单时间：{{noticeDetails.listbooker.order_time}}
                  </li>
                  <li
                    class="tz_right_neirong_font22"
                    v-if="noticeDetails.listbooker"
                  >
                    购买商品：
                    <div
                      v-for="(item,index) in noticeDetails.listbooker.goodsInfo"
                    >
                      <div>{{item.name}}</div>
                    </div>
                  </li>
                  <li
                    class="tz_right_neirong_font22"
                    v-if="noticeDetails.listbooker && noticeDetails.listbooker.dispatch_type==2"
                  >
                    收货人：{{addressInfo.name}}
                  </li>
                  <li
                    class="tz_right_neirong_font22"
                    v-if="noticeDetails.listbooker && noticeDetails.listbooker.dispatch_type==2"
                  >
                    收货地址：{{addressInfo.address}}
                  </li>
                </ul>
              </div>
            </div>
            <div class="Notice-details" @click="seeNoticeDetails">查看详情</div>
          </div>
        </div>
        <p v-else style="text-align: center; margin: 15px">暂无数据</p>
      </div>

      <!--预约/订单详情-->
      <el-dialog
        width="700px"
        title="预约/订单详情"
        top="7vh"
        :visible.sync="isNoticeDetails"
        :close-on-click-modal="false"
        :show-close="false"
        custom-class="notice-mask"
      >
        <div slot="title" title="勿动"></div>
        <app-notice
          :details="noticeDetailsData"
          :login-info="loginInfo"
          @modify="modifyOrder"
          @send-cancel="sendCancel"
          @send-close="bindSendClose"
          @send-change="bindchangeTechnician"
        ></app-notice>
      </el-dialog>

      <el-dialog
        title="取消预约"
        :visible.sync="isCancelReservation"
        class="cancelReservation-mask"
        width="400px"
      >
        <app-cancel
          :details="noticeDetailsData"
          @send-close="sendClose"
          @send-confirm="sendConfirm"
        ></app-cancel>
      </el-dialog>

      <el-dialog
        class="add-server-mask"
        :visible.sync="isTechnician"
        width="600px"
      >
        <template slot="title">
          选择
          <technician-name></technician-name>
        </template>
        <choose-technician
          :technician-list="technicianInfo"
          @send-change="bindModifyTechnician"
        ></choose-technician>
      </el-dialog>

      <el-dialog
        title="订单详情"
        :show-close="false"
        class="order-details"
        :visible.sync="isDetails"
        :fullscreen="true"
      >
        <div slot="title"></div>
        <order-details
          :details="orderDetails"
          @send-close-details="closeDetails"
          @send-print="bindPrint"
        ></order-details>
      </el-dialog>

      <!--默认打印-->
      <app-print
        style="display: none"
        :store="loginInfo"
        :order-info="orderDetails"
        :goods="orderDetails.orderInfo"
        :member="orderDetails.vip"
        :gift="orderDetails.presentData"
        :print-type="2"
      ></app-print>
      <!--打印样式-->
      <div
        class="printWrap"
        :style="{width:paperwidth +'px'}"
        ref="printorderstr"
      >
        <template v-for="(item,index) in printSet.set">
          <template v-if="item.name=='store'">
            <app-store :store-set="item.set" :store="loginInfo"></app-store>
          </template>
          <template v-if="item.name=='header'">
            <app-header
              :header-set="item.set"
              :order-header="orderDetails"
            ></app-header>
          </template>
          <template v-if="item.name=='goods'">
            <app-goods
              :goods-set="item.set"
              :goods="orderDetails.orderInfo"
            ></app-goods>
          </template>
          <template v-if="item.name=='goods'">
            <app-gift
              :goods-set="item.set"
              :gift="orderDetails.presentData"
            ></app-gift>
          </template>
          <template v-if="item.name=='vip'">
            <app-vip
              :vip-set="item.set"
              :member="orderDetails.vip"
              :print-type="2"
            ></app-vip>
          </template>
          <!-- <template v-if="item.name=='takegoods'">
            <app-address
              :address-set="item.set"
              :order-info="orderDetails"
            ></app-address>
          </template> -->
          <template v-if="item.name=='footer'">
            <app-footer
              :footer-set="item.set"
              :order-footer="orderDetails"
              print-type="2"
            ></app-footer>
          </template>
          <template v-if="item.name=='line'">
            <app-line :line-set="item.set"></app-line>
          </template>
          <template v-if="item.name=='info'">
            <app-info :info-set="item.set"></app-info>
          </template>
          <template v-if="item.name=='text'">
            <app-text :text-set="item.set"></app-text>
          </template>
        </template>
      </div>
    </div>
  </body>

  <script src="vue/vue2.5.16.js"></script>
  <script src="vue/element/<EMAIL>"></script>
  <script src="js/plugin/jquery-3.2.1.min.js"></script>
  <script src="js/plugin/<EMAIL>"></script>
  <script src="js/plugin/vue-infinite-scroll.js"></script>
  <script src="print/print.js"></script>
  <script src="component/components.js"></script>
  <script src="js/notice.js"></script>
  <script type="text/javascript" src="js/plugin/LodopFuncs.js"></script>
  <script src="js/plugin/Qrcode.js"></script>
  <script src="js/plugin/JsBarcode.js"></script>
</html>
