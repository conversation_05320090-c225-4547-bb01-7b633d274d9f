.shop-body {
  box-sizing: border-box;
  overflow-y: auto;
  /*60 + 2 + 15 + 50 */
}

.shop-body::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.shop-body::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.shop-body::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.card-shop {
  background: #fff;
}

.cardBg {
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  transition-property: background-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.cardBg:hover {
  background-color: #f5f7fa;
}
.cardBg:active {
  color: #fff;
  background-color: #3363FF;
}

.cardBg > img {
  width: 100%;
  height: 100%;
  display: inline-block;
}

.card-info {
  width: 100%;
  height: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 10px 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  cursor: pointer;
}

.card-name {
  width: fit-content;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.validity_time {
  font-size: 12px;
}

.cardActive {
  color: #fff;
  background-color: #3363FF;
}

.cardBg.cardActive:hover,
.cardActive .cardBg:hover{
  background-color: #3363FF;
}

.aging {
  font-size: 14px;
}

.giveAway-faceValue {
  display: flex;
  justify-content: space-between;
}

.faceValue>span{
  font-size: 18px;
}

.giveAway {
  display: inline-block;
  border: 1px solid #fff;
  box-sizing: border-box;
  padding: 5px 10px;
  font-size: 12px;
  -webkit-border-radius: 25px;
  -moz-border-radius: 25px;
  border-radius: 25px;
}
