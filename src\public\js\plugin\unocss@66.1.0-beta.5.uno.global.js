/**
 * Skipped minification because the original files appears to be already minified.
 * Original file: /npm/@unocss/runtime@66.1.0-beta.5/uno.global.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
"use strict";(()=>{var Uu=Object.defineProperty;var Bu=(e,t)=>{for(var r in t)Uu(e,r,{get:t[r],enumerable:!0})};var Ze="default",Dt="preflights",Du="shortcuts",Iu="imports",Po={[Iu]:-200,[Dt]:-100,[Du]:-10,[Ze]:0};var It=/[\\:]?[\s'"`;{}]+/g;function Nu(e){return e.split(It)}var Nt={name:"@unocss/core/extractor-split",order:0,extract({code:e}){return Nu(e)}};function C(e=[]){return Array.isArray(e)?e:[e]}function ne(e){return Array.from(new Set(e))}function bn(e,t){return e.reduce((r,n)=>(r.findIndex(i=>t(n,i))===-1&&r.push(n),r),[])}function M(e){return typeof e=="string"}var Je=class extends Set{_map;constructor(t){super(t),this._map??=new Map}add(t){return this._map??=new Map,this._map.set(t,(this._map.get(t)??0)+1),super.add(t)}delete(t){return this._map.delete(t),super.delete(t)}clear(){this._map.clear(),super.clear()}getCount(t){return this._map.get(t)??0}setCount(t,r){return this._map.set(t,r),super.add(t)}};function Kt(e){return e instanceof Je}function le(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Q(e){let t=e.length,r=-1,n,o="",i=e.charCodeAt(0);for(;++r<t;){if(n=e.charCodeAt(r),n===0){o+="\uFFFD";continue}if(n===37){o+="\\%";continue}if(n===44){o+="\\,";continue}if(n>=1&&n<=31||n===127||r===0&&n>=48&&n<=57||r===1&&n>=48&&n<=57&&i===45){o+=`\\${n.toString(16)} `;continue}if(r===0&&t===1&&n===45){o+=`\\${e.charAt(r)}`;continue}if(n>=128||n===45||n===95||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122){o+=e.charAt(r);continue}o+=`\\${e.charAt(r)}`}return o}var Gt=Q;function Vo(){return{events:{},emit(e,...t){(this.events[e]||[]).forEach(r=>r(...t))},on(e,t){return(this.events[e]=this.events[e]||[]).push(t),()=>this.events[e]=(this.events[e]||[]).filter(r=>r!==t)}}}var Ku=/[\w\u00A0-\uFFFF%-?]/;function Mo(e=""){return Ku.test(e)}function Fo(e){return typeof e=="function"?{match:e}:e}function xn(e){return e.length===3}function Ht(e){return e!=null}function _o(){}var qt=class{_map=new Map;get(t,r){let n=this._map.get(t);if(n)return n.get(r)}getFallback(t,r,n){let o=this._map.get(t);return o||(o=new Map,this._map.set(t,o)),o.has(r)||o.set(r,n),o.get(r)}set(t,r,n){let o=this._map.get(t);return o||(o=new Map,this._map.set(t,o)),o.set(r,n),this}has(t,r){return this._map.get(t)?.has(r)}delete(t,r){return this._map.get(t)?.delete(r)||!1}deleteTop(t){return this._map.delete(t)}map(t){return Array.from(this._map.entries()).flatMap(([r,n])=>Array.from(n.entries()).map(([o,i])=>t(i,r,o)))}},Yt=class extends Map{getFallback(t,r){let n=this.get(t);return n===void 0?(this.set(t,r),r):n}map(t){let r=[];return this.forEach((n,o)=>{r.push(t(n,o))}),r}flatMap(t){let r=[];return this.forEach((n,o)=>{r.push(...t(n,o))}),r}};function et(e){return M(e)?e:(Array.isArray(e)?e:Object.entries(e)).filter(t=>t[1]!=null)}function yn(e){return Array.isArray(e)?e.find(t=>!Array.isArray(t)||Array.isArray(t[0]))?e.map(t=>et(t)):[e]:[et(e)]}function Gu(e){return e.filter(([t,r],n)=>{if(t.startsWith("$$"))return!1;for(let o=n-1;o>=0;o--)if(e[o][0]===t&&e[o][1]===r)return!1;return!0})}function Le(e){return e==null?"":Gu(e).map(([t,r])=>r!=null&&typeof r!="function"?`${t}:${r};`:void 0).filter(Boolean).join("")}function Xt(e){return e&&typeof e=="object"&&!Array.isArray(e)}function $n(e,t,r=!1){let n=e,o=t;if(Array.isArray(o))return r&&Array.isArray(o)?[...n,...o]:[...o];let i={...n};return Xt(n)&&Xt(o)&&Object.keys(o).forEach(s=>{Xt(n[s])&&Xt(o[s])||Array.isArray(n[s])&&Array.isArray(o[s])?i[s]=$n(n[s],o[s],r):Object.assign(i,{[s]:o[s]})}),i}function Qe(e){let t,r,n;if(Array.isArray(e)){for(r=Array.from({length:t=e.length});t--;)r[t]=(n=e[t])&&typeof n=="object"?Qe(n):n;return r}if(Object.prototype.toString.call(e)==="[object Object]"){r={};for(t in e)t==="__proto__"?Object.defineProperty(r,t,{value:Qe(e[t]),configurable:!0,enumerable:!0,writable:!0}):r[t]=(n=e[t])&&typeof n=="object"?Qe(n):n;return r}return e}function Lo(e){return M(e[0])}function Wo(e){return M(e[0])}var Zt={};function Hu(e=["-",":"]){let t=e.join("|");return Zt[t]||(Zt[t]=new RegExp(`((?:[!@<~\\w+:_-]|\\[&?>?:?\\S*\\])+?)(${t})\\(((?:[~!<>\\w\\s:/\\\\,%#.$?-]|\\[[^\\]]*?\\])+?)\\)(?!\\s*?=>)`,"gm")),Zt[t].lastIndex=0,Zt[t]}function qu(e,t=["-",":"],r=5){let n=Hu(t),o,i=e.toString(),s=new Set,a=new Map;do o=!1,i=i.replace(n,(l,p,d,h,m)=>{if(!t.includes(d))return l;o=!0,s.add(p+d);let g=m+p.length+d.length+1,b={length:l.length,items:[]};a.set(m,b);for(let $ of[...h.matchAll(/\S+/g)]){let S=g+$.index,v=a.get(S)?.items;v?a.delete(S):v=[{offset:S,length:$[0].length,className:$[0]}];for(let E of v)E.className=E.className==="~"?p:E.className.replace(/^(!?)(.*)/,`$1${p}${d}$2`),b.items.push(E)}return"$".repeat(l.length)}),r-=1;while(o&&r);let c;if(typeof e=="string"){c="";let l=0;for(let[p,d]of a)c+=e.slice(l,p),c+=d.items.map(h=>h.className).join(" "),l=p+d.length;c+=e.slice(l)}else{c=e;for(let[l,p]of a)c.overwrite(l,l+p.length,p.items.map(d=>d.className).join(" "))}return{prefixes:Array.from(s),hasChanged:o,groupsByOffset:a,get expanded(){return c.toString()}}}function Uo(e,t=["-",":"],r=5){let n=qu(e,t,r);return typeof e=="string"?n.expanded:e}var Bo=new Set;function Do(e){Bo.has(e)||(console.warn("[unocss]",e),Bo.add(e))}function Ko(e){return C(e).flatMap(t=>Array.isArray(t)?[t]:Object.entries(t))}var Io="_uno_resolved";async function Yu(e){let t=typeof e=="function"?await e():await e;if(Io in t)return t;t={...t},Object.defineProperty(t,Io,{value:!0,enumerable:!1});let r=t.shortcuts?Ko(t.shortcuts):void 0;if(t.shortcuts=r,t.prefix||t.layer){let n=o=>{o[2]||(o[2]={});let i=o[2];i.prefix==null&&t.prefix&&(i.prefix=C(t.prefix)),i.layer==null&&t.layer&&(i.layer=t.layer)};r?.forEach(n),t.rules?.forEach(n)}return t}async function Go(e){let t=await Yu(e);if(!t.presets)return[t];let r=(await Promise.all((t.presets||[]).flatMap(C).flatMap(Go))).flat();return[t,...r]}function Xu(e){if(e.length===0)return{};let t=[],r=[],n=!1,o=[],i=[],s=[];for(let c of e){if(c.pipeline===!1){n=!0;break}else c.pipeline?.include&&t.push(c.pipeline.include),c.pipeline?.exclude&&r.push(c.pipeline.exclude);c.filesystem&&o.push(c.filesystem),c.inline&&i.push(c.inline),c.plain&&s.push(c.plain)}let a={pipeline:n?!1:{include:ne(No(...t)),exclude:ne(No(...r))}};return o.length&&(a.filesystem=ne(o.flat())),i.length&&(a.inline=ne(i.flat())),s.length&&(a.plain=ne(s.flat())),a}async function vn(e={},t={}){let r=Object.assign({},t,e),n=bn((await Promise.all((r.presets||[]).flatMap(C).flatMap(Go))).flat(),(y,x)=>y.name===x.name),o=[...n.filter(y=>y.enforce==="pre"),...n.filter(y=>!y.enforce),...n.filter(y=>y.enforce==="post")],i=[...o,r],s=[...i].reverse(),a=Object.assign({},Po,...i.map(y=>y.layers));function c(y){return ne(i.flatMap(x=>C(x[y]||[])))}let l=c("extractors"),p=s.find(y=>y.extractorDefault!==void 0)?.extractorDefault;p===void 0&&(p=Nt),p&&!l.includes(p)&&l.unshift(p),l.sort((y,x)=>(y.order||0)-(x.order||0));let d=c("rules"),h={},m=d.length,g=d.filter(y=>Lo(y)?(C(y[2]?.prefix||"").forEach(k=>{h[k+y[0]]=y}),!1):!0).reverse(),b=Zu(i.map(y=>y.theme)),$=c("extendTheme");for(let y of $)b=y(b)||b;let S={templates:ne(i.flatMap(y=>C(y.autocomplete?.templates))),extractors:i.flatMap(y=>C(y.autocomplete?.extractors)).sort((y,x)=>(y.order||0)-(x.order||0)),shorthands:Ju(i.map(y=>y.autocomplete?.shorthands||{}))},v=c("separators");v.length||(v=[":","-"]);let E=c("content"),H=Xu(E),V={mergeSelectors:!0,warn:!0,sortLayers:y=>y,...r,blocklist:c("blocklist"),presets:o,envMode:r.envMode||"build",shortcutsLayer:r.shortcutsLayer||"shortcuts",layers:a,theme:b,rules:d,rulesSize:m,rulesDynamic:g,rulesStaticMap:h,preprocess:c("preprocess"),postprocess:c("postprocess"),preflights:c("preflights"),autocomplete:S,variants:c("variants").map(Fo).sort((y,x)=>(y.order||0)-(x.order||0)),shortcuts:Ko(c("shortcuts")).reverse(),extractors:l,safelist:c("safelist"),separators:v,details:r.details??r.envMode==="dev",content:H,transformers:bn(c("transformers"),(y,x)=>y.name===x.name)};for(let y of i)y?.configResolved?.(V);return V}function Zu(e){return e.map(t=>t?Qe(t):{}).reduce((t,r)=>$n(t,r),{})}function Ju(e){return e.reduce((t,r)=>{let n={};for(let o in r){let i=r[o];Array.isArray(i)?n[o]=`(${i.join("|")})`:n[o]=i}return{...t,...n}},{})}function No(...e){return e.flatMap(Qu)}function Qu(e){return Array.isArray(e)?e:e?[e]:[]}var Ho="66.1.0-beta.5";var Ce={shortcutsNoMerge:"$$symbol-shortcut-no-merge",variants:"$$symbol-variants",parent:"$$symbol-parent",selector:"$$symbol-selector",layer:"$$symbol-layer",sort:"$$symbol-sort"},wn=class e{constructor(t={},r={}){this.userConfig=t;this.defaults=r}version=Ho;events=Vo();config=void 0;cache=new Map;blocked=new Set;parentOrders=new Map;activatedRules=new Set;static async create(t={},r={}){let n=new e(t,r);return n.config=await vn(n.userConfig,n.defaults),n.events.emit("config",n.config),n}async setConfig(t,r){t&&(r&&(this.defaults=r),this.userConfig=t,this.blocked.clear(),this.parentOrders.clear(),this.activatedRules.clear(),this.cache.clear(),this.config=await vn(t,this.defaults),this.events.emit("config",this.config))}async applyExtractors(t,r,n=new Set){let o={original:t,code:t,id:r,extracted:n,envMode:this.config.envMode};for(let i of this.config.extractors){let s=await i.extract?.(o);if(s)if(Kt(s)&&Kt(n))for(let a of s)n.setCount(a,n.getCount(a)+s.getCount(a));else for(let a of s)n.add(a)}return n}makeContext(t,r){let n={rawSelector:t,currentSelector:r[1],theme:this.config.theme,generator:this,symbols:Ce,variantHandlers:r[2],constructCSS:(...o)=>this.constructCustomCSS(n,...o),variantMatch:r};return n}async parseToken(t,r){if(this.blocked.has(t))return;let n=`${t}${r?` ${r}`:""}`;if(this.cache.has(n))return this.cache.get(n);let o=t;for(let c of this.config.preprocess)o=c(t);if(this.isBlocked(o)){this.blocked.add(t),this.cache.set(n,null);return}let i=await this.matchVariants(t,o);if(i.every(c=>!c||this.isBlocked(c[1]))){this.blocked.add(t),this.cache.set(n,null);return}let s=async c=>{let l=this.makeContext(t,[r||c[0],c[1],c[2],c[3]]);this.config.details&&(l.variants=[...c[3]]);let p=await this.expandShortcut(l.currentSelector,l);return p?await this.stringifyShortcuts(l.variantMatch,l,p[0],p[1]):(await this.parseUtil(l.variantMatch,l))?.map(h=>this.stringifyUtil(h,l)).filter(Ht)},a=(await Promise.all(i.map(c=>s(c)))).flat().filter(c=>!!c);if(a?.length)return this.cache.set(n,a),a;this.cache.set(n,null)}async generate(t,r={}){let{id:n,scope:o,preflights:i=!0,safelist:s=!0,minify:a=!1,extendedInfo:c=!1}=r,l=M(t)?await this.applyExtractors(t,n,c?new Je:new Set):Array.isArray(t)?new Set(t):t;if(s){let x={generator:this,theme:this.config.theme};this.config.safelist.flatMap(k=>typeof k=="function"?k(x):k).forEach(k=>{l.has(k)||l.add(k)})}let p=a?"":`
`,d=new Set([Ze]),h=c?new Map:new Set,m=new Map,g={},b=Array.from(l).map(async x=>{if(h.has(x))return;let k=await this.parseToken(x);if(k!=null){h instanceof Map?h.set(x,{data:k,count:Kt(l)?l.getCount(x):-1}):h.add(x);for(let T of k){let Z=T[3]||"",J=T[4]?.layer;m.has(Z)||m.set(Z,[]),m.get(Z).push(T),J&&d.add(J)}}});await Promise.all(b),await(async()=>{if(!i)return;let x={generator:this,theme:this.config.theme},k=new Set([]);this.config.preflights.forEach(({layer:T=Dt})=>{d.add(T),k.add(T)}),g=Object.fromEntries(await Promise.all(Array.from(k).map(async T=>{let J=(await Promise.all(this.config.preflights.filter(ce=>(ce.layer||Dt)===T).map(async ce=>await ce.getCSS(x)))).filter(Boolean).join(p);return[T,J]})))})();let $=this.config.sortLayers(Array.from(d).sort((x,k)=>(this.config.layers[x]??0)-(this.config.layers[k]??0)||x.localeCompare(k))),S={},v=this.config.outputToCssLayers,E=x=>{let k=x;return typeof v=="object"&&(k=v.cssLayerName?.(x)),k===null?null:k??x},H=(x=Ze)=>{if(S[x])return S[x];let k=Array.from(m).sort((J,ce)=>(this.parentOrders.get(J[0])??0)-(this.parentOrders.get(ce[0])??0)||J[0]?.localeCompare(ce[0]||"")||0).map(([J,ce])=>{let R=ce.length,O=ce.filter(z=>(z[4]?.layer||Ze)===x).sort((z,X)=>z[0]-X[0]||(z[4]?.sort||0)-(X[4]?.sort||0)||z[5]?.currentSelector?.localeCompare(X[5]?.currentSelector??"")||z[1]?.localeCompare(X[1]||"")||z[2]?.localeCompare(X[2]||"")||0).map(([,z,X,,Bt,,hn])=>[[[(z&&rf(z,o))??"",Bt?.sort??0]],X,!!(hn??Bt?.noMerge)]);if(!O.length)return;let W=O.reverse().map(([z,X,Bt],hn)=>{if(!Bt&&this.config.mergeSelectors)for(let Se=hn+1;Se<R;Se++){let de=O[Se];if(de&&!de[2]&&(z&&de[0]||z==null&&de[0]==null)&&de[1]===X)return z&&de[0]&&de[0].push(...z),null}let gn=z?ne(z.sort((Se,de)=>Se[1]-de[1]||Se[0]?.localeCompare(de[0]||"")||0).map(Se=>Se[0]).filter(Boolean)):[];return gn.length?`${gn.join(`,${p}`)}{${X}}`:X}).filter(Boolean).reverse().join(p);if(!J)return W;let D=J.split(" $$ ");return`${D.join("{")}{${p}${W}${p}${"}".repeat(D.length)}`}).filter(Boolean).join(p);i&&(k=[g[x],k].filter(Boolean).join(p));let T;v&&k&&(T=E(x),T!==null&&(k=`@layer ${T}{${p}${k}${p}}`));let Z=a?"":`/* layer: ${x}${T&&T!==x?`, alias: ${T}`:""} */${p}`;return S[x]=k?Z+k:""},V=(x=$,k)=>{let T=x.filter(Z=>!k?.includes(Z));return[v&&T.length>0?`@layer ${T.map(E).filter(Ht).join(", ")};`:void 0,...T.map(Z=>H(Z)||"")].filter(Boolean).join(p)};return{get css(){return V()},layers:$,matched:h,getLayers:V,getLayer:H,setLayer:async(x,k)=>{let T=await k(H(x));return S[x]=T,T}}}async matchVariants(t,r){let n={rawSelector:t,theme:this.config.theme,generator:this},o=async i=>{let s=!0,[,,a,c]=i;for(;s;){s=!1;let l=i[1];for(let p of this.config.variants){if(!p.multiPass&&c.has(p))continue;let d=await p.match(l,n);if(d){if(M(d)){if(d===l)continue;d={matcher:d}}if(Array.isArray(d)){if(!d.length)continue;if(d.length===1)d=d[0];else{if(p.multiPass)throw new Error("multiPass can not be used together with array return variants");let h=d.map(m=>{let g=m.matcher??l,b=[m,...a],$=new Set(c);return $.add(p),[i[0],g,b,$]});return(await Promise.all(h.map(m=>o(m)))).flat()}}i[1]=d.matcher??l,a.unshift(d),c.add(p),s=!0;break}}if(!s)break;if(a.length>500)throw new Error(`Too many variants applied to "${t}"`)}return[i]};return await o([t,r||t,[],new Set])}applyVariants(t,r=t[4],n=t[1]){let i=r.slice().sort((l,p)=>(l.order||0)-(p.order||0)).reduceRight((l,p)=>d=>{let h=p.body?.(d.entries)||d.entries,m=Array.isArray(p.parent)?p.parent:[p.parent,void 0];return(p.handle??of)({...d,entries:h,selector:p.selector?.(d.selector,h)||d.selector,parent:m[0]||d.parent,parentOrder:m[1]||d.parentOrder,layer:p.layer||d.layer,sort:p.sort||d.sort},l)},l=>l)({prefix:"",selector:nf(n),pseudo:"",entries:t[2]}),{parent:s,parentOrder:a}=i;s!=null&&a!=null&&this.parentOrders.set(s,a);let c={selector:[i.prefix,i.selector,i.pseudo].join(""),entries:i.entries,parent:s,layer:i.layer,sort:i.sort,noMerge:i.noMerge};for(let l of this.config.postprocess)l(c);return c}constructCustomCSS(t,r,n){let o=et(r);if(M(o))return o;let{selector:i,entries:s,parent:a}=this.applyVariants([0,n||t.rawSelector,o,void 0,t.variantHandlers]),c=`${i}{${Le(s)}}`;return a?`${a}{${c}}`:c}async parseUtil(t,r,n=!1,o){let i=M(t)?await this.matchVariants(t):[t],s=async([c,l,p])=>{this.config.details&&(r.rules=r.rules??[]);let d=this.config.rulesStaticMap[l];if(d&&d[1]&&(n||!d[2]?.internal)){r.generator.activatedRules.add(d),this.config.details&&r.rules.push(d);let m=this.config.rules.indexOf(d),g=yn(d[1]).filter($=>$.length),b=d[2];if(g.length)return g.map($=>M($)?[m,$,b]:[m,c,$,b,p])}r.variantHandlers=p;let{rulesDynamic:h}=this.config;for(let m of h){let[g,b,$]=m;if($?.internal&&!n)continue;let S=l;if($?.prefix){let V=C($.prefix);if(o){let y=C(o);if(!V.some(x=>y.includes(x)))continue}else{let y=V.find(x=>l.startsWith(x));if(y==null)continue;S=l.slice(y.length)}}let v=S.match(g);if(!v)continue;let E=await b(v,r);if(!E)continue;if(r.generator.activatedRules.add(m),this.config.details&&r.rules.push(m),typeof E!="string")if(Symbol.asyncIterator in E){let V=[];for await(let y of E)y&&V.push(y);E=V}else Symbol.iterator in E&&!Array.isArray(E)&&(E=Array.from(E).filter(Ht));let H=yn(E).filter(V=>V.length);if(H.length){let V=this.config.rules.indexOf(m);return H.map(y=>{if(M(y))return[V,y,$];let x=p,k=$;for(let T of y)T[0]===Ce.variants?x=[...C(T[1]),...x]:T[0]===Ce.parent?x=[{parent:T[1]},...x]:T[0]===Ce.selector?x=[{selector:T[1]},...x]:T[0]===Ce.layer?x=[{layer:T[1]},...x]:T[0]===Ce.sort&&(k={...k,sort:T[1]});return[V,c,y,k,x]})}}},a=(await Promise.all(i.map(c=>s(c)))).flat().filter(c=>!!c);if(a.length)return a}stringifyUtil(t,r){if(!t)return;if(xn(t))return[t[0],void 0,t[1],void 0,t[2],this.config.details?r:void 0,void 0];let{selector:n,entries:o,parent:i,layer:s,sort:a,noMerge:c}=this.applyVariants(t),l=Le(o);if(!l)return;let{layer:p,sort:d,...h}=t[3]??{},m={...h,layer:s??p,sort:a??d};return[t[0],n,l,i,m,this.config.details?r:void 0,c]}async expandShortcut(t,r,n=5){if(n===0)return;let o=this.config.details?l=>{r.shortcuts=r.shortcuts??[],r.shortcuts.push(l)}:_o,i,s,a,c;for(let l of this.config.shortcuts){let p=t;if(l[2]?.prefix){let h=C(l[2].prefix).find(m=>t.startsWith(m));if(h==null)continue;p=t.slice(h.length)}if(Wo(l)){if(l[0]===p){i=i||l[2],s=l[1],o(l);break}}else{let d=p.match(l[0]);if(d&&(s=l[1](d,r)),s){i=i||l[2],o(l);break}}}if(s&&(a=ne(C(s).filter(M).map(l=>Uo(l.trim()).split(/\s+/g)).flat()),c=C(s).filter(l=>!M(l)).map(l=>({handles:[],value:l}))),!s){let l=M(t)?await this.matchVariants(t):[t];for(let p of l){let[d,h,m]=p;if(d!==h){let g=await this.expandShortcut(h,r,n-1);g&&(a=g[0].filter(M).map(b=>d.replace(h,b)),c=g[0].filter(b=>!M(b)).map(b=>({handles:[...b.handles,...m],value:b.value})))}}}if(!(!a?.length&&!c?.length))return[[await Promise.all(C(a).map(async l=>(await this.expandShortcut(l,r,n-1))?.[0]||[l])),c].flat(2).filter(l=>!!l),i]}async stringifyShortcuts(t,r,n,o={layer:this.config.shortcutsLayer}){let i=new Yt,s=(await Promise.all(ne(n).map(async p=>{let d=M(p)?await this.parseUtil(p,r,!0,o.prefix):[[Number.POSITIVE_INFINITY,"{inline}",et(p.value),void 0,p.handles]];return!d&&this.config.warn&&Do(`unmatched utility "${p}" in shortcut "${t[1]}"`),d||[]}))).flat(1).filter(Boolean).sort((p,d)=>p[0]-d[0]),[a,,c]=t,l=[];for(let p of s){if(xn(p)){l.push([p[0],void 0,p[1],void 0,p[2],r,void 0]);continue}let{selector:d,entries:h,parent:m,sort:g,noMerge:b,layer:$}=this.applyVariants(p,[...p[4],...c],a);i.getFallback($??o.layer,new qt).getFallback(d,m,[[],p[0]])[0].push([h,!!(b??p[3]?.noMerge),g??0])}return l.concat(i.flatMap((p,d)=>p.map(([h,m],g,b)=>{let $=(v,E,H)=>{let V=Math.max(...H.map(x=>x[1])),y=H.map(x=>x[0]);return(v?[y.flat(1)]:y).map(x=>{let k=Le(x);if(k)return[m,g,k,b,{...o,noMerge:E,sort:V,layer:d},r,void 0]})};return[[h.filter(([,v])=>v).map(([v,,E])=>[v,E]),!0],[h.filter(([,v])=>!v).map(([v,,E])=>[v,E]),!1]].map(([v,E])=>[...$(!1,E,v.filter(([H])=>H.some(V=>V[0]===Ce.shortcutsNoMerge))),...$(!0,E,v.filter(([H])=>H.every(V=>V[0]!==Ce.shortcutsNoMerge)))])}).flat(2).filter(Boolean)))}isBlocked(t){return!t||this.config.blocklist.map(r=>Array.isArray(r)?r[0]:r).some(r=>typeof r=="function"?r(t):M(r)?r===t:r.test(t))}getBlocked(t){let r=this.config.blocklist.find(n=>{let o=Array.isArray(n)?n[0]:n;return typeof o=="function"?o(t):M(o)?o===t:o.test(t)});return r?Array.isArray(r)?r:[r,void 0]:void 0}};async function Yo(e,t){return await wn.create(e,t)}var Xo=/\s\$\$\s+/g;function tf(e){return Xo.test(e)}function rf(e,t){return tf(e)?e.replace(Xo,t?` ${t} `:" "):t?`${t} ${e}`:e}var qo=/^\[(.+?)(~?=)"(.*)"\]$/;function nf(e){return qo.test(e)?e.replace(qo,(t,r,n,o)=>`[${Gt(r)}${n}"${Gt(o)}"]`):`.${Gt(e)}`}function of(e,t){return t(e)}function sf(e){let t,r,n=2166136261;for(t=0,r=e.length;t<r;t++)n^=e.charCodeAt(t),n+=(n<<1)+(n<<4)+(n<<7)+(n<<8)+(n<<24);return`00000${(n>>>0).toString(36)}`.slice(-6)}function Zo(e,t,r,n){for(let o of Array.from(e.matchAll(r)))if(o!=null){let i=o[0],s=`${n}${sf(i)}`;t.set(s,i),e=e.replace(i,s)}return e}function Jo(e,t){for(let[r,n]of t.entries())e=e.replaceAll(r,n);return e}var af=/\/\/#\s*sourceMappingURL=.*\n?/g;function Qo(e){return e.includes("sourceMappingURL=")?e.replace(af,""):e}var cf=/(?:[\w&:[\]-]|\[\S{1,64}=\S{1,64}\]){1,64}\[\\?['"]?\S{1,64}?['"]\]\]?[\w:-]{0,64}/g,lf=/\[(\\\W|[\w-]){1,64}:[^\s:]{0,64}?("\S{1,64}?"|'\S{1,64}?'|`\S{1,64}?`|[^\s:]{1,64}?)[^\s:]{0,64}?\)?\]/g,uf=/^\[(?:\\\W|[\w-]){1,64}:['"]?\S{1,64}?['"]?\]$/;function ff(e){let t=[];for(let o of e.matchAll(lf))o.index!==0&&!/^[\s'"`]/.test(e[o.index-1]??"")||t.push(o[0]);for(let o of e.matchAll(cf))t.push(o[0]);let r=new Map,n="@unocss-skip-arbitrary-brackets";return e=Zo(e,r,/-\[(?!&.+?;)[^\]]*\]/g,n),e&&e.split(It).forEach(o=>{o.includes(n)&&(o=Jo(o,r)),Mo(o)&&!uf.test(o)&&t.push(o)}),t}function ei(){return{name:"@unocss/extractor-arbitrary-variants",order:0,extract({code:e}){return ff(Qo(e))}}}function ti(e){if(e.preflight)return[{layer:"preflights",getCSS({theme:t,generator:r}){if(t.preflightBase){let n=Object.entries(t.preflightBase);if(e.preflight==="on-demand"){let o=new Set(Array.from(r.activatedRules).map(i=>i[2]?.custom?.preflightKeys).filter(Boolean).flat());n=n.filter(([i])=>o.has(i))}if(n.length>0){let o=Le(n);return e.variablePrefix!=="un-"&&(o=o.replace(/--un-/g,`--${e.variablePrefix}`)),C(t.preflightRoot??["*,::before,::after","::backdrop"]).map(s=>`${s}{${o}}`).join("")}}}}]}function ye(e,t,r){if(e==="")return;let n=e.length,o=0,i=!1,s=0;for(let a=0;a<n;a++)switch(e[a]){case t:i||(i=!0,s=a),o++;break;case r:if(--o,o<0)return;if(o===0)return[e.slice(s,a+1),e.slice(a+1),e.slice(0,s)];break}}function ue(e,t,r,n){if(e===""||(M(n)&&(n=[n]),n.length===0))return;let o=e.length,i=0;for(let s=0;s<o;s++)switch(e[s]){case t:i++;break;case r:if(--i<0)return;break;default:for(let a of n){let c=a.length;if(c&&a===e.slice(s,s+c)&&i===0)return s===0||s===o-c?void 0:[e.slice(0,s),e.slice(s+c)]}}return[e,""]}function Re(e,t,r){r=r??10;let n=[],o=0;for(;e!=="";){if(++o>r)return;let i=ue(e,"(",")",t);if(!i)return;let[s,a]=i;n.push(s),e=a}if(n.length>0)return n}var kn=["hsl","hsla","hwb","lab","lch","oklab","oklch","rgb","rgba"],ri=["%alpha","<alpha-value>"],pf=new RegExp(ri.map(e=>le(e)).join("|"),"g");function q(e=""){let t=df(e);if(t==null||t===!1)return;let{type:r,components:n,alpha:o}=t,i=r.toLowerCase();if(n.length!==0&&!(kn.includes(i)&&![1,3].includes(n.length)))return{type:i,components:n.map(s=>typeof s=="string"?s.trim():s),alpha:typeof o=="string"?o.trim():o}}function oe(e){let t=e.alpha??1;return typeof t=="string"&&ri.includes(t)?1:t}function A(e,t){if(typeof e=="string")return e.replace(pf,`${t??1}`);let{components:r}=e,{alpha:n,type:o}=e;return n=t??n,o=o.toLowerCase(),["hsla","rgba"].includes(o)?`${o}(${r.join(", ")}${n==null?"":`, ${n}`})`:(n=n==null?"":` / ${n}`,kn.includes(o)?`${o}(${r.join(" ")}${n})`:`color(${o} ${r.join(" ")}${n})`)}function df(e){if(!e)return;let t=mf(e);if(t!=null||(t=hf(e),t!=null)||(t=gf(e),t!=null)||(t=xf(e),t!=null)||(t=yf(e),t!=null))return t}function mf(e){let[,t]=e.match(/^#([\da-f]+)$/i)||[];if(t)switch(t.length){case 3:case 4:let r=Array.from(t,o=>Number.parseInt(o,16)).map(o=>o<<4|o);return{type:"rgb",components:r.slice(0,3),alpha:t.length===3?void 0:Math.round(r[3]/255*100)/100};case 6:case 8:let n=Number.parseInt(t,16);return{type:"rgb",components:t.length===6?[n>>16&255,n>>8&255,n&255]:[n>>24&255,n>>16&255,n>>8&255],alpha:t.length===6?void 0:Math.round((n&255)/255*100)/100}}}function hf(e){let t={rebeccapurple:[102,51,153,1]}[e];if(t!=null)return{type:"rgb",components:t.slice(0,3),alpha:t[3]}}function gf(e){let t=e.match(/^(rgb|rgba|hsl|hsla)\((.+)\)$/i);if(!t)return;let[,r,n]=t,o=Re(n,",",5);if(o){if([3,4].includes(o.length))return{type:r,components:o.slice(0,3),alpha:o[3]};if(o.length!==1)return!1}}var bf=new RegExp(`^(${kn.join("|")})\\((.+)\\)$`,"i");function xf(e){let t=e.match(bf);if(!t)return;let[,r,n]=t,o=ni(`${r} ${n}`);if(o){let{alpha:i,components:[s,...a]}=o;return{type:s,components:a,alpha:i}}}function yf(e){let t=e.match(/^color\((.+)\)$/);if(!t)return;let r=ni(t[1]);if(r){let{alpha:n,components:[o,...i]}=r;return{type:o,components:i,alpha:n}}}function ni(e){let t=Re(e," ");if(!t)return;let r=t.length;if(t[r-2]==="/")return{components:t.slice(0,r-2),alpha:t[r-1]};if(t[r-2]!=null&&(t[r-2].endsWith("/")||t[r-1].startsWith("/"))){let i=t.splice(r-2);t.push(i.join(" ")),--r}let n=Re(t[r-1],"/",2);if(!n)return;if(n.length===1||n[n.length-1]==="")return{components:t};let o=n.pop();return t[r-1]=n.join("/"),{components:t,alpha:o}}function Jt(e){let t=function(n){let o=this.__options?.sequence||[];this.__options.sequence=[];for(let i of o){let s=e[i](n);if(s!=null)return s}};function r(n,o){return n.__options||(n.__options={sequence:[]}),n.__options.sequence.push(o),n}for(let n of Object.keys(e))Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get(){return r(this,n)}});return t}var oi="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ai=new Uint8Array(64),$f=new Uint8Array(128);for(let e=0;e<oi.length;e++){let t=oi.charCodeAt(e);ai[e]=t,$f[t]=e}function tt(e,t,r){let n=t-r;n=n<0?-n<<1|1:n<<1;do{let o=n&31;n>>>=5,n>0&&(o|=32),e.write(ai[o])}while(n>0);return t}var ii=1024*16,si=typeof TextDecoder<"u"?new TextDecoder:typeof Buffer<"u"?{decode(e){return Buffer.from(e.buffer,e.byteOffset,e.byteLength).toString()}}:{decode(e){let t="";for(let r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t}},Sn=class{constructor(){this.pos=0,this.out="",this.buffer=new Uint8Array(ii)}write(t){let{buffer:r}=this;r[this.pos++]=t,this.pos===ii&&(this.out+=si.decode(r),this.pos=0)}flush(){let{buffer:t,out:r,pos:n}=this;return n>0?r+si.decode(t.subarray(0,n)):r}};function ci(e){let t=new Sn,r=0,n=0,o=0,i=0;for(let s=0;s<e.length;s++){let a=e[s];if(s>0&&t.write(59),a.length===0)continue;let c=0;for(let l=0;l<a.length;l++){let p=a[l];l>0&&t.write(44),c=tt(t,p[0],c),p.length!==1&&(r=tt(t,p[1],r),n=tt(t,p[2],n),o=tt(t,p[3],o),p.length!==4&&(i=tt(t,p[4],i)))}}return t.flush()}var Qt=class e{constructor(t){this.bits=t instanceof e?t.bits.slice():[]}add(t){this.bits[t>>5]|=1<<(t&31)}has(t){return!!(this.bits[t>>5]&1<<(t&31))}},er=class e{constructor(t,r,n){this.start=t,this.end=r,this.original=n,this.intro="",this.outro="",this.content=n,this.storeName=!1,this.edited=!1,this.previous=null,this.next=null}appendLeft(t){this.outro+=t}appendRight(t){this.intro=this.intro+t}clone(){let t=new e(this.start,this.end,this.original);return t.intro=this.intro,t.outro=this.outro,t.content=this.content,t.storeName=this.storeName,t.edited=this.edited,t}contains(t){return this.start<t&&t<this.end}eachNext(t){let r=this;for(;r;)t(r),r=r.next}eachPrevious(t){let r=this;for(;r;)t(r),r=r.previous}edit(t,r,n){return this.content=t,n||(this.intro="",this.outro=""),this.storeName=r,this.edited=!0,this}prependLeft(t){this.outro=t+this.outro}prependRight(t){this.intro=t+this.intro}reset(){this.intro="",this.outro="",this.edited&&(this.content=this.original,this.storeName=!1,this.edited=!1)}split(t){let r=t-this.start,n=this.original.slice(0,r),o=this.original.slice(r);this.original=n;let i=new e(t,this.end,o);return i.outro=this.outro,this.outro="",this.end=t,this.edited?(i.edit("",!1),this.content=""):this.content=n,i.next=this.next,i.next&&(i.next.previous=i),i.previous=this,this.next=i,i}toString(){return this.intro+this.content+this.outro}trimEnd(t){if(this.outro=this.outro.replace(t,""),this.outro.length)return!0;let r=this.content.replace(t,"");if(r.length)return r!==this.content&&(this.split(this.start+r.length).edit("",void 0,!0),this.edited&&this.edit(r,this.storeName,!0)),!0;if(this.edit("",void 0,!0),this.intro=this.intro.replace(t,""),this.intro.length)return!0}trimStart(t){if(this.intro=this.intro.replace(t,""),this.intro.length)return!0;let r=this.content.replace(t,"");if(r.length){if(r!==this.content){let n=this.split(this.end-r.length);this.edited&&n.edit(r,this.storeName,!0),this.edit("",void 0,!0)}return!0}else if(this.edit("",void 0,!0),this.outro=this.outro.replace(t,""),this.outro.length)return!0}};function vf(){return typeof globalThis<"u"&&typeof globalThis.btoa=="function"?e=>globalThis.btoa(unescape(encodeURIComponent(e))):typeof Buffer=="function"?e=>Buffer.from(e,"utf-8").toString("base64"):()=>{throw new Error("Unsupported environment: `window.btoa` or `Buffer` should be supported.")}}var wf=vf(),Cn=class{constructor(t){this.version=3,this.file=t.file,this.sources=t.sources,this.sourcesContent=t.sourcesContent,this.names=t.names,this.mappings=ci(t.mappings),typeof t.x_google_ignoreList<"u"&&(this.x_google_ignoreList=t.x_google_ignoreList),typeof t.debugId<"u"&&(this.debugId=t.debugId)}toString(){return JSON.stringify(this)}toUrl(){return"data:application/json;charset=utf-8;base64,"+wf(this.toString())}};function kf(e){let t=e.split(`
`),r=t.filter(i=>/^\t+/.test(i)),n=t.filter(i=>/^ {2,}/.test(i));if(r.length===0&&n.length===0)return null;if(r.length>=n.length)return"	";let o=n.reduce((i,s)=>{let a=/^ +/.exec(s)[0].length;return Math.min(a,i)},1/0);return new Array(o+1).join(" ")}function Sf(e,t){let r=e.split(/[/\\]/),n=t.split(/[/\\]/);for(r.pop();r[0]===n[0];)r.shift(),n.shift();if(r.length){let o=r.length;for(;o--;)r[o]=".."}return r.concat(n).join("/")}var Cf=Object.prototype.toString;function Rf(e){return Cf.call(e)==="[object Object]"}function li(e){let t=e.split(`
`),r=[];for(let n=0,o=0;n<t.length;n++)r.push(o),o+=t[n].length+1;return function(o){let i=0,s=r.length;for(;i<s;){let l=i+s>>1;o<r[l]?s=l:i=l+1}let a=i-1,c=o-r[a];return{line:a,column:c}}}var Ef=/\w/,Rn=class{constructor(t){this.hires=t,this.generatedCodeLine=0,this.generatedCodeColumn=0,this.raw=[],this.rawSegments=this.raw[this.generatedCodeLine]=[],this.pending=null}addEdit(t,r,n,o){if(r.length){let i=r.length-1,s=r.indexOf(`
`,0),a=-1;for(;s>=0&&i>s;){let l=[this.generatedCodeColumn,t,n.line,n.column];o>=0&&l.push(o),this.rawSegments.push(l),this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,a=s,s=r.indexOf(`
`,s+1)}let c=[this.generatedCodeColumn,t,n.line,n.column];o>=0&&c.push(o),this.rawSegments.push(c),this.advance(r.slice(a+1))}else this.pending&&(this.rawSegments.push(this.pending),this.advance(r));this.pending=null}addUneditedChunk(t,r,n,o,i){let s=r.start,a=!0,c=!1;for(;s<r.end;){if(n[s]===`
`)o.line+=1,o.column=0,this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,a=!0,c=!1;else{if(this.hires||a||i.has(s)){let l=[this.generatedCodeColumn,t,o.line,o.column];this.hires==="boundary"?Ef.test(n[s])?c||(this.rawSegments.push(l),c=!0):(this.rawSegments.push(l),c=!1):this.rawSegments.push(l)}o.column+=1,this.generatedCodeColumn+=1,a=!1}s+=1}this.pending=null}advance(t){if(!t)return;let r=t.split(`
`);if(r.length>1){for(let n=0;n<r.length-1;n++)this.generatedCodeLine++,this.raw[this.generatedCodeLine]=this.rawSegments=[];this.generatedCodeColumn=0}this.generatedCodeColumn+=r[r.length-1].length}},rt=`
`,We={insertLeft:!1,insertRight:!1,storeName:!1},tr=class e{constructor(t,r={}){let n=new er(0,t.length,t);Object.defineProperties(this,{original:{writable:!0,value:t},outro:{writable:!0,value:""},intro:{writable:!0,value:""},firstChunk:{writable:!0,value:n},lastChunk:{writable:!0,value:n},lastSearchedChunk:{writable:!0,value:n},byStart:{writable:!0,value:{}},byEnd:{writable:!0,value:{}},filename:{writable:!0,value:r.filename},indentExclusionRanges:{writable:!0,value:r.indentExclusionRanges},sourcemapLocations:{writable:!0,value:new Qt},storedNames:{writable:!0,value:{}},indentStr:{writable:!0,value:void 0},ignoreList:{writable:!0,value:r.ignoreList},offset:{writable:!0,value:r.offset||0}}),this.byStart[0]=n,this.byEnd[t.length]=n}addSourcemapLocation(t){this.sourcemapLocations.add(t)}append(t){if(typeof t!="string")throw new TypeError("outro content must be a string");return this.outro+=t,this}appendLeft(t,r){if(t=t+this.offset,typeof r!="string")throw new TypeError("inserted content must be a string");this._split(t);let n=this.byEnd[t];return n?n.appendLeft(r):this.intro+=r,this}appendRight(t,r){if(t=t+this.offset,typeof r!="string")throw new TypeError("inserted content must be a string");this._split(t);let n=this.byStart[t];return n?n.appendRight(r):this.outro+=r,this}clone(){let t=new e(this.original,{filename:this.filename,offset:this.offset}),r=this.firstChunk,n=t.firstChunk=t.lastSearchedChunk=r.clone();for(;r;){t.byStart[n.start]=n,t.byEnd[n.end]=n;let o=r.next,i=o&&o.clone();i&&(n.next=i,i.previous=n,n=i),r=o}return t.lastChunk=n,this.indentExclusionRanges&&(t.indentExclusionRanges=this.indentExclusionRanges.slice()),t.sourcemapLocations=new Qt(this.sourcemapLocations),t.intro=this.intro,t.outro=this.outro,t}generateDecodedMap(t){t=t||{};let r=0,n=Object.keys(this.storedNames),o=new Rn(t.hires),i=li(this.original);return this.intro&&o.advance(this.intro),this.firstChunk.eachNext(s=>{let a=i(s.start);s.intro.length&&o.advance(s.intro),s.edited?o.addEdit(r,s.content,a,s.storeName?n.indexOf(s.original):-1):o.addUneditedChunk(r,s,this.original,a,this.sourcemapLocations),s.outro.length&&o.advance(s.outro)}),{file:t.file?t.file.split(/[/\\]/).pop():void 0,sources:[t.source?Sf(t.file||"",t.source):t.file||""],sourcesContent:t.includeContent?[this.original]:void 0,names:n,mappings:o.raw,x_google_ignoreList:this.ignoreList?[r]:void 0}}generateMap(t){return new Cn(this.generateDecodedMap(t))}_ensureindentStr(){this.indentStr===void 0&&(this.indentStr=kf(this.original))}_getRawIndentString(){return this._ensureindentStr(),this.indentStr}getIndentString(){return this._ensureindentStr(),this.indentStr===null?"	":this.indentStr}indent(t,r){let n=/^[^\r\n]/gm;if(Rf(t)&&(r=t,t=void 0),t===void 0&&(this._ensureindentStr(),t=this.indentStr||"	"),t==="")return this;r=r||{};let o={};r.exclude&&(typeof r.exclude[0]=="number"?[r.exclude]:r.exclude).forEach(p=>{for(let d=p[0];d<p[1];d+=1)o[d]=!0});let i=r.indentStart!==!1,s=l=>i?`${t}${l}`:(i=!0,l);this.intro=this.intro.replace(n,s);let a=0,c=this.firstChunk;for(;c;){let l=c.end;if(c.edited)o[a]||(c.content=c.content.replace(n,s),c.content.length&&(i=c.content[c.content.length-1]===`
`));else for(a=c.start;a<l;){if(!o[a]){let p=this.original[a];p===`
`?i=!0:p!=="\r"&&i&&(i=!1,a===c.start||(this._splitChunk(c,a),c=c.next),c.prependRight(t))}a+=1}a=c.end,c=c.next}return this.outro=this.outro.replace(n,s),this}insert(){throw new Error("magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)")}insertLeft(t,r){return We.insertLeft||(console.warn("magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead"),We.insertLeft=!0),this.appendLeft(t,r)}insertRight(t,r){return We.insertRight||(console.warn("magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead"),We.insertRight=!0),this.prependRight(t,r)}move(t,r,n){if(t=t+this.offset,r=r+this.offset,n=n+this.offset,n>=t&&n<=r)throw new Error("Cannot move a selection inside itself");this._split(t),this._split(r),this._split(n);let o=this.byStart[t],i=this.byEnd[r],s=o.previous,a=i.next,c=this.byStart[n];if(!c&&i===this.lastChunk)return this;let l=c?c.previous:this.lastChunk;return s&&(s.next=a),a&&(a.previous=s),l&&(l.next=o),c&&(c.previous=i),o.previous||(this.firstChunk=i.next),i.next||(this.lastChunk=o.previous,this.lastChunk.next=null),o.previous=l,i.next=c||null,l||(this.firstChunk=o),c||(this.lastChunk=i),this}overwrite(t,r,n,o){return o=o||{},this.update(t,r,n,{...o,overwrite:!o.contentOnly})}update(t,r,n,o){if(t=t+this.offset,r=r+this.offset,typeof n!="string")throw new TypeError("replacement content must be a string");if(this.original.length!==0){for(;t<0;)t+=this.original.length;for(;r<0;)r+=this.original.length}if(r>this.original.length)throw new Error("end is out of bounds");if(t===r)throw new Error("Cannot overwrite a zero-length range \u2013 use appendLeft or prependRight instead");this._split(t),this._split(r),o===!0&&(We.storeName||(console.warn("The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string"),We.storeName=!0),o={storeName:!0});let i=o!==void 0?o.storeName:!1,s=o!==void 0?o.overwrite:!1;if(i){let l=this.original.slice(t,r);Object.defineProperty(this.storedNames,l,{writable:!0,value:!0,enumerable:!0})}let a=this.byStart[t],c=this.byEnd[r];if(a){let l=a;for(;l!==c;){if(l.next!==this.byStart[l.end])throw new Error("Cannot overwrite across a split point");l=l.next,l.edit("",!1)}a.edit(n,i,!s)}else{let l=new er(t,r,"").edit(n,i);c.next=l,l.previous=c}return this}prepend(t){if(typeof t!="string")throw new TypeError("outro content must be a string");return this.intro=t+this.intro,this}prependLeft(t,r){if(t=t+this.offset,typeof r!="string")throw new TypeError("inserted content must be a string");this._split(t);let n=this.byEnd[t];return n?n.prependLeft(r):this.intro=r+this.intro,this}prependRight(t,r){if(t=t+this.offset,typeof r!="string")throw new TypeError("inserted content must be a string");this._split(t);let n=this.byStart[t];return n?n.prependRight(r):this.outro=r+this.outro,this}remove(t,r){if(t=t+this.offset,r=r+this.offset,this.original.length!==0){for(;t<0;)t+=this.original.length;for(;r<0;)r+=this.original.length}if(t===r)return this;if(t<0||r>this.original.length)throw new Error("Character is out of bounds");if(t>r)throw new Error("end must be greater than start");this._split(t),this._split(r);let n=this.byStart[t];for(;n;)n.intro="",n.outro="",n.edit(""),n=r>n.end?this.byStart[n.end]:null;return this}reset(t,r){if(t=t+this.offset,r=r+this.offset,this.original.length!==0){for(;t<0;)t+=this.original.length;for(;r<0;)r+=this.original.length}if(t===r)return this;if(t<0||r>this.original.length)throw new Error("Character is out of bounds");if(t>r)throw new Error("end must be greater than start");this._split(t),this._split(r);let n=this.byStart[t];for(;n;)n.reset(),n=r>n.end?this.byStart[n.end]:null;return this}lastChar(){if(this.outro.length)return this.outro[this.outro.length-1];let t=this.lastChunk;do{if(t.outro.length)return t.outro[t.outro.length-1];if(t.content.length)return t.content[t.content.length-1];if(t.intro.length)return t.intro[t.intro.length-1]}while(t=t.previous);return this.intro.length?this.intro[this.intro.length-1]:""}lastLine(){let t=this.outro.lastIndexOf(rt);if(t!==-1)return this.outro.substr(t+1);let r=this.outro,n=this.lastChunk;do{if(n.outro.length>0){if(t=n.outro.lastIndexOf(rt),t!==-1)return n.outro.substr(t+1)+r;r=n.outro+r}if(n.content.length>0){if(t=n.content.lastIndexOf(rt),t!==-1)return n.content.substr(t+1)+r;r=n.content+r}if(n.intro.length>0){if(t=n.intro.lastIndexOf(rt),t!==-1)return n.intro.substr(t+1)+r;r=n.intro+r}}while(n=n.previous);return t=this.intro.lastIndexOf(rt),t!==-1?this.intro.substr(t+1)+r:this.intro+r}slice(t=0,r=this.original.length-this.offset){if(t=t+this.offset,r=r+this.offset,this.original.length!==0){for(;t<0;)t+=this.original.length;for(;r<0;)r+=this.original.length}let n="",o=this.firstChunk;for(;o&&(o.start>t||o.end<=t);){if(o.start<r&&o.end>=r)return n;o=o.next}if(o&&o.edited&&o.start!==t)throw new Error(`Cannot use replaced character ${t} as slice start anchor.`);let i=o;for(;o;){o.intro&&(i!==o||o.start===t)&&(n+=o.intro);let s=o.start<r&&o.end>=r;if(s&&o.edited&&o.end!==r)throw new Error(`Cannot use replaced character ${r} as slice end anchor.`);let a=i===o?t-o.start:0,c=s?o.content.length+r-o.end:o.content.length;if(n+=o.content.slice(a,c),o.outro&&(!s||o.end===r)&&(n+=o.outro),s)break;o=o.next}return n}snip(t,r){let n=this.clone();return n.remove(0,t),n.remove(r,n.original.length),n}_split(t){if(this.byStart[t]||this.byEnd[t])return;let r=this.lastSearchedChunk,n=t>r.end;for(;r;){if(r.contains(t))return this._splitChunk(r,t);r=n?this.byStart[r.end]:this.byEnd[r.start]}}_splitChunk(t,r){if(t.edited&&t.content.length){let o=li(this.original)(r);throw new Error(`Cannot split a chunk that has already been edited (${o.line}:${o.column} \u2013 "${t.original}")`)}let n=t.split(r);return this.byEnd[r]=t,this.byStart[r]=n,this.byEnd[n.end]=n,t===this.lastChunk&&(this.lastChunk=n),this.lastSearchedChunk=t,!0}toString(){let t=this.intro,r=this.firstChunk;for(;r;)t+=r.toString(),r=r.next;return t+this.outro}isEmpty(){let t=this.firstChunk;do if(t.intro.length&&t.intro.trim()||t.content.length&&t.content.trim()||t.outro.length&&t.outro.trim())return!1;while(t=t.next);return!0}length(){let t=this.firstChunk,r=0;do r+=t.intro.length+t.content.length+t.outro.length;while(t=t.next);return r}trimLines(){return this.trim("[\\r\\n]")}trim(t){return this.trimStart(t).trimEnd(t)}trimEndAborted(t){let r=new RegExp((t||"\\s")+"+$");if(this.outro=this.outro.replace(r,""),this.outro.length)return!0;let n=this.lastChunk;do{let o=n.end,i=n.trimEnd(r);if(n.end!==o&&(this.lastChunk===n&&(this.lastChunk=n.next),this.byEnd[n.end]=n,this.byStart[n.next.start]=n.next,this.byEnd[n.next.end]=n.next),i)return!0;n=n.previous}while(n);return!1}trimEnd(t){return this.trimEndAborted(t),this}trimStartAborted(t){let r=new RegExp("^"+(t||"\\s")+"+");if(this.intro=this.intro.replace(r,""),this.intro.length)return!0;let n=this.firstChunk;do{let o=n.end,i=n.trimStart(r);if(n.end!==o&&(n===this.lastChunk&&(this.lastChunk=n.next),this.byEnd[n.end]=n,this.byStart[n.next.start]=n.next,this.byEnd[n.next.end]=n.next),i)return!0;n=n.next}while(n);return!1}trimStart(t){return this.trimStartAborted(t),this}hasChanged(){return this.original!==this.toString()}_replaceRegexp(t,r){function n(i,s){return typeof r=="string"?r.replace(/\$(\$|&|\d+)/g,(a,c)=>c==="$"?"$":c==="&"?i[0]:+c<i.length?i[+c]:`$${c}`):r(...i,i.index,s,i.groups)}function o(i,s){let a,c=[];for(;a=i.exec(s);)c.push(a);return c}if(t.global)o(t,this.original).forEach(s=>{if(s.index!=null){let a=n(s,this.original);a!==s[0]&&this.overwrite(s.index,s.index+s[0].length,a)}});else{let i=this.original.match(t);if(i&&i.index!=null){let s=n(i,this.original);s!==i[0]&&this.overwrite(i.index,i.index+i[0].length,s)}}return this}_replaceString(t,r){let{original:n}=this,o=n.indexOf(t);return o!==-1&&this.overwrite(o,o+t.length,r),this}replace(t,r){return typeof t=="string"?this._replaceString(t,r):this._replaceRegexp(t,r)}_replaceAllString(t,r){let{original:n}=this,o=t.length;for(let i=n.indexOf(t);i!==-1;i=n.indexOf(t,i+o))n.slice(i,i+o)!==r&&this.overwrite(i,i+o,r);return this}replaceAll(t,r){if(typeof t=="string")return this._replaceAllString(t,r);if(!t.global)throw new TypeError("MagicString.prototype.replaceAll called with a non-global RegExp argument");return this._replaceRegexp(t,r)}};var Tf=/theme\(\s*(['"])?(.*?)\1?\s*\)/g;function rr(e){return e.includes("theme(")&&e.includes(")")}function nr(e,t,r=!0){let n=Array.from(e.toString().matchAll(Tf));if(!n.length)return e;let o=new tr(e);for(let i of n){let s=i[2];if(!s)throw new Error("theme() expect exact one argument, but got 0");let a=jf(s,t,r);a&&o.overwrite(i.index,i.index+i[0].length,a)}return o.toString()}function jf(e,t,r=!0){let[n,o]=e.split("/"),s=n.trim().split(".").reduce((a,c)=>a?.[c],t);if(typeof s=="object"&&(s=s.DEFAULT),typeof s=="string"){if(o){let a=q(s);a&&(s=A(a,o))}return s}else if(r)throw new Error(`theme of "${e}" did not found`)}function I(e,t){let r;return{name:e,match(n,o){r||(r=new RegExp(`^${le(e)}(?:${o.generator.config.separators.join("|")})`));let i=n.match(r);if(i){let s=n.slice(i[0].length),a=C(t).map(c=>({matcher:s,handle:(l,p)=>p({...l,...c(l)})}));return a.length===1?a[0]:a}},autocomplete:`${e}:`}}function N(e,t){let r;return{name:e,match(n,o){r||(r=new RegExp(`^${le(e)}(?:${o.generator.config.separators.join("|")})`));let i=n.match(r);if(i)return{matcher:n.slice(i[0].length),handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}${t}`})}},autocomplete:`${e}:`}}function ee(e,t,r){if(t.startsWith(`${e}[`)){let[n,o]=ye(t.slice(e.length),"[","]")??[];if(n&&o){for(let i of r)if(o.startsWith(i))return[n,o.slice(i.length),i];return[n,o,""]}}}function F(e,t,r){if(t.startsWith(e)){let n=ee(e,t,r);if(n){let[o="",i=n[1]]=F("/",n[1],r)??[];return[n[0],i,o]}for(let o of r.filter(i=>i!=="/")){let i=t.indexOf(o,e.length);if(i!==-1){let s=t.indexOf("/",e.length),a=s===-1||i<=s;return[t.slice(e.length,a?i:s),t.slice(i+o.length),a?"":t.slice(s+1,i)]}}}}var On={};Bu(On,{auto:()=>Of,bracket:()=>Lf,bracketOfColor:()=>Wf,bracketOfLength:()=>Uf,bracketOfPosition:()=>Bf,cssvar:()=>Df,degree:()=>Nf,fraction:()=>_f,global:()=>Kf,number:()=>Mf,numberWithUnit:()=>Af,percent:()=>Ff,position:()=>Hf,properties:()=>Gf,px:()=>Vf,rem:()=>Pf,time:()=>If});var te={l:["-left"],r:["-right"],t:["-top"],b:["-bottom"],s:["-inline-start"],e:["-inline-end"],x:["-left","-right"],y:["-top","-bottom"],"":[""],bs:["-block-start"],be:["-block-end"],is:["-inline-start"],ie:["-inline-end"],block:["-block-start","-block-end"],inline:["-inline-start","-inline-end"]},En={...te,s:["-inset-inline-start"],start:["-inset-inline-start"],e:["-inset-inline-end"],end:["-inset-inline-end"],bs:["-inset-block-start"],be:["-inset-block-end"],is:["-inset-inline-start"],ie:["-inset-inline-end"],block:["-inset-block-start","-inset-block-end"],inline:["-inset-inline-start","-inset-inline-end"]},Tn={l:["-top-left","-bottom-left"],r:["-top-right","-bottom-right"],t:["-top-left","-top-right"],b:["-bottom-left","-bottom-right"],tl:["-top-left"],lt:["-top-left"],tr:["-top-right"],rt:["-top-right"],bl:["-bottom-left"],lb:["-bottom-left"],br:["-bottom-right"],rb:["-bottom-right"],"":[""],bs:["-start-start","-start-end"],be:["-end-start","-end-end"],s:["-end-start","-start-start"],is:["-end-start","-start-start"],e:["-start-end","-end-end"],ie:["-start-end","-end-end"],ss:["-start-start"],"bs-is":["-start-start"],"is-bs":["-start-start"],se:["-start-end"],"bs-ie":["-start-end"],"ie-bs":["-start-end"],es:["-end-start"],"be-is":["-end-start"],"is-be":["-end-start"],ee:["-end-end"],"be-ie":["-end-end"],"ie-be":["-end-end"]},fi={x:["-x"],y:["-y"],z:["-z"],"":["-x","-y"]},pi=["x","y","z"],ui=["top","top center","top left","top right","bottom","bottom center","bottom left","bottom right","left","left center","left top","left bottom","right","right center","right top","right bottom","center","center top","center bottom","center left","center right","center center"],nt=Object.assign({},...ui.map(e=>({[e.replace(/ /,"-")]:e})),...ui.map(e=>({[e.replace(/\b(\w)\w+/g,"$1").replace(/ /,"")]:e}))),_=["inherit","initial","revert","revert-layer","unset"],ot=/^(calc|clamp|min|max)\s*\((.+)\)(.*)/,or=/^(var)\s*\((.+)\)(.*)/;var Ue=/^(-?\d*(?:\.\d+)?)(px|pt|pc|%|r?(?:em|ex|lh|cap|ch|ic)|(?:[sld]?v|cq)(?:[whib]|min|max)|in|cm|mm|rpx)?$/i,jn=/^(-?\d*(?:\.\d+)?)$/,zn=/^(px|[sld]?v[wh])$/i,An={px:1,vw:100,vh:100,svw:100,svh:100,dvw:100,dvh:100,lvh:100,lvw:100},ir=/^\[(color|image|length|size|position|quoted|string):/i,di=/,(?![^()]*\))/g;var zf=["color","border-color","background-color","outline-color","text-decoration-color","flex-grow","flex","flex-shrink","caret-color","font","gap","opacity","visibility","z-index","font-weight","zoom","text-shadow","transform","box-shadow","border","background-position","left","right","top","bottom","object-position","max-height","min-height","max-width","min-width","height","width","border-width","margin","padding","outline-width","outline-offset","font-size","line-height","text-indent","vertical-align","border-spacing","letter-spacing","word-spacing","stroke","filter","backdrop-filter","fill","mask","mask-size","mask-border","clip-path","clip","border-radius"];function ie(e){return+e.toFixed(10)}function Af(e){let t=e.match(Ue);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(n&&!Number.isNaN(o))return`${ie(o)}${n}`}function Of(e){if(e==="auto"||e==="a")return"auto"}function Pf(e){if(!e)return;if(zn.test(e))return`${An[e]}${e}`;let t=e.match(Ue);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0?"0":n?`${ie(o)}${n}`:`${ie(o/4)}rem`}function Vf(e){if(zn.test(e))return`${An[e]}${e}`;let t=e.match(Ue);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return n?`${ie(o)}${n}`:`${ie(o)}px`}function Mf(e){if(!jn.test(e))return;let t=Number.parseFloat(e);if(!Number.isNaN(t))return ie(t)}function Ff(e){if(e.endsWith("%")&&(e=e.slice(0,-1)),!jn.test(e))return;let t=Number.parseFloat(e);if(!Number.isNaN(t))return`${ie(t/100)}`}function _f(e){if(!e)return;if(e==="full")return"100%";let[t,r]=e.split("/"),n=Number.parseFloat(t)/Number.parseFloat(r);if(!Number.isNaN(n))return n===0?"0":`${ie(n*100)}%`}function sr(e,t){if(e&&e.startsWith("[")&&e.endsWith("]")){let r,n,o=e.match(ir);if(o?(t||(n=o[1]),r=e.slice(o[0].length,-1)):r=e.slice(1,-1),!r||r==='=""')return;r.startsWith("--")&&(r=`var(${r})`);let i=0;for(let s of r)if(s==="[")i+=1;else if(s==="]"&&(i-=1,i<0))return;if(i)return;switch(n){case"string":return r.replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_");case"quoted":return r.replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_").replace(/(["\\])/g,"\\$1").replace(/^(.+)$/,'"$1"')}return r.replace(/(url\(.*?\))/g,s=>s.replace(/_/g,"\\_")).replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_").replace(/(?:calc|clamp|max|min)\((.*)/g,s=>{let a=[];return s.replace(/var\((--.+?)[,)]/g,(c,l)=>(a.push(l),c.replace(l,"--un-calc"))).replace(/(-?\d*\.?\d(?!-\d.+[,)](?![^+\-/*])\D)(?:%|[a-z]+)?|\))([+\-/*])/g,"$1 $2 ").replace(/--un-calc/g,()=>a.shift())})}}function Lf(e){return sr(e)}function Wf(e){return sr(e,"color")}function Uf(e){return sr(e,"length")}function Bf(e){return sr(e,"position")}function Df(e){if(/^\$[^\s'"`;{}]/.test(e)){let[t,r]=e.slice(1).split(",");return`var(--${Q(t)}${r?`, ${r}`:""})`}}function If(e){let t=e.match(/^(-?[0-9.]+)(s|ms)?$/i);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0&&!n?"0s":n?`${ie(o)}${n}`:`${ie(o)}ms`}function Nf(e){let t=e.match(/^(-?[0-9.]+)(deg|rad|grad|turn)?$/i);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0?"0":n?`${ie(o)}${n}`:`${ie(o)}deg`}function Kf(e){if(_.includes(e))return e}function Gf(e){if(e.split(",").every(t=>zf.includes(t)))return e}function Hf(e){if(["top","left","right","bottom","center"].includes(e))return e}var qf=Jt(On),f=qf;var mi={mid:"middle",base:"baseline",btm:"bottom",baseline:"baseline",top:"top",start:"top",middle:"middle",bottom:"bottom",end:"bottom","text-top":"text-top","text-bottom":"text-bottom",sub:"sub",super:"super",...Object.fromEntries(_.map(e=>[e,e]))},gi=[[/^(?:vertical|align|v)-([-\w]+%?)$/,([,e])=>({"vertical-align":mi[e]??f.numberWithUnit(e)}),{autocomplete:[`(vertical|align|v)-(${Object.keys(mi).join("|")})`,"(vertical|align|v)-<percentage>"]}]],hi=["center","left","right","justify","start","end"],bi=[...hi.map(e=>[`text-${e}`,{"text-align":e}]),...[..._,...hi].map(e=>[`text-align-${e}`,{"text-align":e}])];var vi="$$mini-no-negative";function re(e){return([t,r,n],{theme:o})=>{let i=o.spacing?.[n||"DEFAULT"]??f.bracket.cssvar.global.auto.fraction.rem(n);if(i!=null)return te[r].map(s=>[`${e}${s}`,i]);if(n?.startsWith("-")){let s=o.spacing?.[n.slice(1)];if(s!=null)return te[r].map(a=>[`${e}${a}`,`calc(${s} * -1)`])}}}function xi(e,t,r="colors"){let n=e[r],o=-1;for(let i of t){if(o+=1,n&&typeof n!="string"){let s=t.slice(o).join("-").replace(/(-[a-z])/g,a=>a.slice(1).toUpperCase());if(n[s])return n[s];if(n[i]){n=n[i];continue}}return}return n}function yi(e,t,r){return xi(e,t,r)||xi(e,t,"colors")}function Vn(e,t){let[r,n]=ue(e,"[","]",["/",":"])??[];if(r!=null){let o=(r.match(ir)??[])[1];if(o==null||o===t)return[r,n]}}function ar(e,t,r){let n=Vn(e,"color");if(!n)return;let[o,i]=n,s=o.replace(/([a-z])(\d)/g,"$1-$2").split(/-/g),[a]=s;if(!a)return;let c,l=f.bracketOfColor(o),p=l||o;if(f.numberWithUnit(p))return;if(/^#[\da-f]+$/i.test(p)?c=p:/^hex-[\da-fA-F]+$/.test(p)?c=`#${p.slice(4)}`:o.startsWith("$")&&(c=f.cssvar(o)),c=c||l,!c){let h=yi(t,[o],r);typeof h=="string"&&(c=h)}let d="DEFAULT";if(!c){let h=s,m,[g]=s.slice(-1);/^\d+$/.test(g)&&(d=m=g,h=s.slice(0,-1));let b=yi(t,h,r);typeof b=="object"?c=b[m??d]:typeof b=="string"&&!m&&(c=b)}return{opacity:i,name:a,no:d,color:c,cssColor:q(c),alpha:f.bracket.cssvar.percent(i??"")}}function K(e,t,r,n){return([,o],{theme:i,generator:s})=>{let a=ar(o,i,r);if(!a)return;let{alpha:c,color:l,cssColor:p}=a,h=s.config.envMode==="dev"&&l?` /* ${l} */`:"",m={};if(p)if(c!=null)m[e]=A(p,c)+h;else{let g=`--un-${t}-opacity`,b=A(p,`var(${g})`);b.includes(g)&&(m[g]=oe(p)),m[e]=b+h}else if(l)if(c!=null)m[e]=A(l,c)+h;else{let g=`--un-${t}-opacity`,b=A(l,`var(${g})`);b.includes(g)&&(m[g]=1),m[e]=b+h}if(n?.(m)!==!1)return m}}function cr(e,t){let r=[];e=C(e);for(let n=0;n<e.length;n++){let o=Re(e[n]," ",6);if(!o||o.length<3)return e;let i=!1,s=o.indexOf("inset");s!==-1&&(o.splice(s,1),i=!0);let a="",c=o.at(-1);if(q(o.at(0))){let l=q(o.shift());l&&(a=`, ${A(l)}`)}else if(q(c)){let l=q(o.pop());l&&(a=`, ${A(l)}`)}else c&&or.test(c)&&(a=`, ${o.pop()}`);r.push(`${i?"inset ":""}${o.join(" ")} var(${t}${a})`)}return r}function lr(e,t,r){return e!=null&&!!ar(e,t,r)?.color}var $i=/[a-z]+/gi,Pn=new WeakMap;function ur({theme:e,generator:t},r="breakpoints"){let n=t?.userConfig?.theme?.[r]||e[r];if(!n)return;if(Pn.has(e))return Pn.get(e);let o=Object.entries(n).sort((i,s)=>Number.parseInt(i[1].replace($i,""))-Number.parseInt(s[1].replace($i,""))).map(([i,s])=>({point:i,size:s}));return Pn.set(e,o),o}function U(e,t){return _.map(r=>[`${e}-${r}`,{[t??e]:r}])}function se(e){return e!=null&&ot.test(e)}function wi(e){return e[0]==="["&&e.slice(-1)==="]"&&(e=e.slice(1,-1)),ot.test(e)||Ue.test(e)}function fr(e,t,r){let n=t.split(di);return e||!e&&n.length===1?fi[e].map(o=>[`--un-${r}${o}`,t]):n.map((o,i)=>[`--un-${r}-${pi[i]}`,o])}var ki=[[/^outline-(?:width-|size-)?(.+)$/,Si,{autocomplete:"outline-(width|size)-<num>"}],[/^outline-(?:color-)?(.+)$/,Yf,{autocomplete:"outline-$colors"}],[/^outline-offset-(.+)$/,([,e],{theme:t})=>({"outline-offset":t.lineWidth?.[e]??f.bracket.cssvar.global.px(e)}),{autocomplete:"outline-(offset)-<num>"}],["outline",{"outline-style":"solid"}],...["auto","dashed","dotted","double","hidden","solid","groove","ridge","inset","outset",..._].map(e=>[`outline-${e}`,{"outline-style":e}]),["outline-none",{outline:"2px solid transparent","outline-offset":"2px"}]];function Si([,e],{theme:t}){return{"outline-width":t.lineWidth?.[e]??f.bracket.cssvar.global.px(e)}}function Yf(e,t){return se(f.bracket(e[1]))?Si(e,t):K("outline-color","outline-color","borderColor")(e,t)}var Ci=[["appearance-auto",{"-webkit-appearance":"auto",appearance:"auto"}],["appearance-none",{"-webkit-appearance":"none",appearance:"none"}]];function Xf(e){return f.properties.auto.global(e)??{contents:"contents",scroll:"scroll-position"}[e]}var Ri=[[/^will-change-(.+)/,([,e])=>({"will-change":Xf(e)})]];var lt=["solid","dashed","dotted","double","hidden","none","groove","ridge","inset","outset",..._],Ti=[[/^(?:border|b)()(?:-(.+))?$/,fe,{autocomplete:"(border|b)-<directions>"}],[/^(?:border|b)-([xy])(?:-(.+))?$/,fe],[/^(?:border|b)-([rltbse])(?:-(.+))?$/,fe],[/^(?:border|b)-(block|inline)(?:-(.+))?$/,fe],[/^(?:border|b)-([bi][se])(?:-(.+))?$/,fe],[/^(?:border|b)-()(?:width|size)-(.+)$/,fe,{autocomplete:["(border|b)-<num>","(border|b)-<directions>-<num>"]}],[/^(?:border|b)-([xy])-(?:width|size)-(.+)$/,fe],[/^(?:border|b)-([rltbse])-(?:width|size)-(.+)$/,fe],[/^(?:border|b)-(block|inline)-(?:width|size)-(.+)$/,fe],[/^(?:border|b)-([bi][se])-(?:width|size)-(.+)$/,fe],[/^(?:border|b)-()(?:color-)?(.+)$/,it,{autocomplete:["(border|b)-$colors","(border|b)-<directions>-$colors"]}],[/^(?:border|b)-([xy])-(?:color-)?(.+)$/,it],[/^(?:border|b)-([rltbse])-(?:color-)?(.+)$/,it],[/^(?:border|b)-(block|inline)-(?:color-)?(.+)$/,it],[/^(?:border|b)-([bi][se])-(?:color-)?(.+)$/,it],[/^(?:border|b)-()op(?:acity)?-?(.+)$/,st,{autocomplete:"(border|b)-(op|opacity)-<percent>"}],[/^(?:border|b)-([xy])-op(?:acity)?-?(.+)$/,st],[/^(?:border|b)-([rltbse])-op(?:acity)?-?(.+)$/,st],[/^(?:border|b)-(block|inline)-op(?:acity)?-?(.+)$/,st],[/^(?:border|b)-([bi][se])-op(?:acity)?-?(.+)$/,st],[/^(?:border-|b-)?(?:rounded|rd)()(?:-(.+))?$/,at,{autocomplete:["(border|b)-(rounded|rd)","(border|b)-(rounded|rd)-$borderRadius","(rounded|rd)","(rounded|rd)-$borderRadius"]}],[/^(?:border-|b-)?(?:rounded|rd)-([rltbse])(?:-(.+))?$/,at],[/^(?:border-|b-)?(?:rounded|rd)-([rltb]{2})(?:-(.+))?$/,at],[/^(?:border-|b-)?(?:rounded|rd)-([bise][se])(?:-(.+))?$/,at],[/^(?:border-|b-)?(?:rounded|rd)-([bi][se]-[bi][se])(?:-(.+))?$/,at],[/^(?:border|b)-(?:style-)?()(.+)$/,ct,{autocomplete:["(border|b)-style",`(border|b)-(${lt.join("|")})`,"(border|b)-<directions>-style",`(border|b)-<directions>-(${lt.join("|")})`,`(border|b)-<directions>-style-(${lt.join("|")})`,`(border|b)-style-(${lt.join("|")})`]}],[/^(?:border|b)-([xy])-(?:style-)?(.+)$/,ct],[/^(?:border|b)-([rltbse])-(?:style-)?(.+)$/,ct],[/^(?:border|b)-(block|inline)-(?:style-)?(.+)$/,ct],[/^(?:border|b)-([bi][se])-(?:style-)?(.+)$/,ct]];function Ei(e,t,r){if(t!=null)return{[`border${r}-color`]:A(e,t)};if(r===""){let n={},o="--un-border-opacity",i=A(e,`var(${o})`);return i.includes(o)&&(n[o]=typeof e=="string"?1:oe(e)),n["border-color"]=i,n}else{let n={},o="--un-border-opacity",i=`--un-border${r}-opacity`,s=A(e,`var(${i})`);return s.includes(i)&&(n[o]=typeof e=="string"?1:oe(e),n[i]=`var(${o})`),n[`border${r}-color`]=s,n}}function Zf(e){return([,t],r)=>{let n=ar(t,r,"borderColor");if(!n)return;let{alpha:o,color:i,cssColor:s}=n;if(s)return Ei(s,o,e);if(i)return Ei(i,o,e)}}function fe([,e="",t],{theme:r}){let n=r.lineWidth?.[t||"DEFAULT"]??f.bracket.cssvar.global.px(t||"1");if(e in te&&n!=null)return te[e].map(o=>[`border${o}-width`,n])}function it([,e="",t],r){if(e in te){if(se(f.bracket(t)))return fe(["",e,t],r);if(lr(t,r.theme,"borderColor"))return Object.assign({},...te[e].map(n=>Zf(n)(["",t],r.theme)))}}function st([,e="",t]){let r=f.bracket.percent.cssvar(t);if(e in te&&r!=null)return te[e].map(n=>[`--un-border${n}-opacity`,r])}function at([,e="",t],{theme:r}){let n=r.borderRadius?.[t||"DEFAULT"]||f.bracket.cssvar.global.fraction.rem(t||"1");if(e in Tn&&n!=null)return Tn[e].map(o=>[`border${o}-radius`,n])}function ct([,e="",t]){if(lt.includes(t)&&e in te)return te[e].map(r=>[`border${r}-style`,t])}var ji=[[/^op(?:acity)?-?(.+)$/,([,e])=>({opacity:f.bracket.percent.cssvar(e)})]],Jf=/^\[url\(.+\)\]$/,Qf=/^\[(?:length|size):.+\]$/,ep=/^\[position:.+\]$/,tp=/^\[(?:linear|conic|radial)-gradient\(.+\)\]$/,rp=/^\[image:.+\]$/,zi=[[/^bg-(.+)$/,(...e)=>{let t=e[0][1];if(Jf.test(t))return{"--un-url":f.bracket(t),"background-image":"var(--un-url)"};if(Qf.test(t)&&f.bracketOfLength(t)!=null)return{"background-size":f.bracketOfLength(t).split(" ").map(r=>f.fraction.auto.px.cssvar(r)??r).join(" ")};if((wi(t)||ep.test(t))&&f.bracketOfPosition(t)!=null)return{"background-position":f.bracketOfPosition(t).split(" ").map(r=>f.position.fraction.auto.px.cssvar(r)??r).join(" ")};if(tp.test(t)||rp.test(t)){let r=f.bracket(t);if(r)return{"background-image":(r.startsWith("http")?`url(${r})`:f.cssvar(r))??r}}return K("background-color","bg","backgroundColor")(...e)},{autocomplete:"bg-$colors"}],[/^bg-op(?:acity)?-?(.+)$/,([,e])=>({"--un-bg-opacity":f.bracket.percent.cssvar(e)}),{autocomplete:"bg-(op|opacity)-<percent>"}]],Ai=[[/^color-scheme-(\w+)$/,([,e])=>({"color-scheme":e})]];var Oi=[[/^@container(?:\/(\w+))?(?:-(normal|inline-size|size))?$/,([,e,t])=>({"container-type":t??"inline-size","container-name":e})]];var Pi=["solid","double","dotted","dashed","wavy",..._],Vi=[[/^(?:decoration-)?(underline|overline|line-through)$/,([,e])=>({"text-decoration-line":e}),{autocomplete:"decoration-(underline|overline|line-through)"}],[/^(?:underline|decoration)-(?:size-)?(.+)$/,Mi,{autocomplete:"(underline|decoration)-<num>"}],[/^(?:underline|decoration)-(auto|from-font)$/,([,e])=>({"text-decoration-thickness":e}),{autocomplete:"(underline|decoration)-(auto|from-font)"}],[/^(?:underline|decoration)-(.+)$/,np,{autocomplete:"(underline|decoration)-$colors"}],[/^(?:underline|decoration)-op(?:acity)?-?(.+)$/,([,e])=>({"--un-line-opacity":f.bracket.percent.cssvar(e)}),{autocomplete:"(underline|decoration)-(op|opacity)-<percent>"}],[/^(?:underline|decoration)-offset-(.+)$/,([,e],{theme:t})=>({"text-underline-offset":t.lineWidth?.[e]??f.auto.bracket.cssvar.global.px(e)}),{autocomplete:"(underline|decoration)-(offset)-<num>"}],...Pi.map(e=>[`underline-${e}`,{"text-decoration-style":e}]),...Pi.map(e=>[`decoration-${e}`,{"text-decoration-style":e}]),["no-underline",{"text-decoration":"none"}],["decoration-none",{"text-decoration":"none"}]];function Mi([,e],{theme:t}){return{"text-decoration-thickness":t.lineWidth?.[e]??f.bracket.cssvar.global.px(e)}}function np(e,t){if(se(f.bracket(e[1])))return Mi(e,t);let r=K("text-decoration-color","line","borderColor")(e,t);if(r)return{"-webkit-text-decoration-color":r["text-decoration-color"],...r}}var Fi=[["flex",{display:"flex"}],["inline-flex",{display:"inline-flex"}],["flex-inline",{display:"inline-flex"}],[/^flex-(.*)$/,([,e])=>({flex:f.bracket(e)!=null?f.bracket(e).split(" ").map(t=>f.cssvar.fraction(t)??t).join(" "):f.cssvar.fraction(e)})],["flex-1",{flex:"1 1 0%"}],["flex-auto",{flex:"1 1 auto"}],["flex-initial",{flex:"0 1 auto"}],["flex-none",{flex:"none"}],[/^(?:flex-)?shrink(?:-(.*))?$/,([,e=""])=>({"flex-shrink":f.bracket.cssvar.number(e)??1}),{autocomplete:["flex-shrink-<num>","shrink-<num>"]}],[/^(?:flex-)?grow(?:-(.*))?$/,([,e=""])=>({"flex-grow":f.bracket.cssvar.number(e)??1}),{autocomplete:["flex-grow-<num>","grow-<num>"]}],[/^(?:flex-)?basis-(.+)$/,([,e],{theme:t})=>({"flex-basis":t.spacing?.[e]??f.bracket.cssvar.auto.fraction.rem(e)}),{autocomplete:["flex-basis-$spacing","basis-$spacing"]}],["flex-row",{"flex-direction":"row"}],["flex-row-reverse",{"flex-direction":"row-reverse"}],["flex-col",{"flex-direction":"column"}],["flex-col-reverse",{"flex-direction":"column-reverse"}],["flex-wrap",{"flex-wrap":"wrap"}],["flex-wrap-reverse",{"flex-wrap":"wrap-reverse"}],["flex-nowrap",{"flex-wrap":"nowrap"}]];var op={"":"",x:"column-",y:"row-",col:"column-",row:"row-"};function Mn([,e="",t],{theme:r}){let n=r.spacing?.[t]??f.bracket.cssvar.global.rem(t);if(n!=null)return{[`${op[e]}gap`]:n}}var _i=[[/^(?:flex-|grid-)?gap-?()(.+)$/,Mn,{autocomplete:["gap-$spacing","gap-<num>"]}],[/^(?:flex-|grid-)?gap-([xy])-?(.+)$/,Mn,{autocomplete:["gap-(x|y)-$spacing","gap-(x|y)-<num>"]}],[/^(?:flex-|grid-)?gap-(col|row)-?(.+)$/,Mn,{autocomplete:["gap-(col|row)-$spacing","gap-(col|row)-<num>"]}]];function me(e){return e.replace("col","column")}function Fn(e){return e[0]==="r"?"Row":"Column"}function ip(e,t,r){let n=t[`gridAuto${Fn(e)}`]?.[r];if(n!=null)return n;switch(r){case"min":return"min-content";case"max":return"max-content";case"fr":return"minmax(0,1fr)"}return f.bracket.cssvar.auto.rem(r)}var Li=[["grid",{display:"grid"}],["inline-grid",{display:"inline-grid"}],[/^(?:grid-)?(row|col)-(.+)$/,([,e,t],{theme:r})=>({[`grid-${me(e)}`]:r[`grid${Fn(e)}`]?.[t]??f.bracket.cssvar.auto(t)})],[/^(?:grid-)?(row|col)-span-(.+)$/,([,e,t])=>{if(t==="full")return{[`grid-${me(e)}`]:"1/-1"};let r=f.bracket.number(t);if(r!=null)return{[`grid-${me(e)}`]:`span ${r}/span ${r}`}},{autocomplete:"(grid-row|grid-col|row|col)-span-<num>"}],[/^(?:grid-)?(row|col)-start-(.+)$/,([,e,t])=>({[`grid-${me(e)}-start`]:f.bracket.cssvar(t)??t})],[/^(?:grid-)?(row|col)-end-(.+)$/,([,e,t])=>({[`grid-${me(e)}-end`]:f.bracket.cssvar(t)??t}),{autocomplete:"(grid-row|grid-col|row|col)-(start|end)-<num>"}],[/^(?:grid-)?auto-(rows|cols)-(.+)$/,([,e,t],{theme:r})=>({[`grid-auto-${me(e)}`]:ip(e,r,t)}),{autocomplete:"(grid-auto|auto)-(rows|cols)-<num>"}],[/^(?:grid-auto-flow|auto-flow|grid-flow)-(.+)$/,([,e])=>({"grid-auto-flow":f.bracket.cssvar(e)})],[/^(?:grid-auto-flow|auto-flow|grid-flow)-(row|col|dense|row-dense|col-dense)$/,([,e])=>({"grid-auto-flow":me(e).replace("-"," ")}),{autocomplete:["(grid-auto-flow|auto-flow|grid-flow)-(row|col|dense|row-dense|col-dense)"]}],[/^(?:grid-)?(rows|cols)-(.+)$/,([,e,t],{theme:r})=>({[`grid-template-${me(e)}`]:r[`gridTemplate${Fn(e)}`]?.[t]??f.bracket.cssvar(t)})],[/^(?:grid-)?(rows|cols)-minmax-([\w.-]+)$/,([,e,t])=>({[`grid-template-${me(e)}`]:`repeat(auto-fill,minmax(${t},1fr))`})],[/^(?:grid-)?(rows|cols)-(\d+)$/,([,e,t])=>({[`grid-template-${me(e)}`]:`repeat(${t},minmax(0,1fr))`}),{autocomplete:"(grid-rows|grid-cols|rows|cols)-<num>"}],[/^grid-area(s)?-(.+)$/,([,e,t])=>e!=null?{"grid-template-areas":f.cssvar(t)??t.split("-").map(r=>`"${f.bracket(r)}"`).join(" ")}:{"grid-area":f.bracket.cssvar(t)}],["grid-rows-none",{"grid-template-rows":"none"}],["grid-cols-none",{"grid-template-columns":"none"}],["grid-rows-subgrid",{"grid-template-rows":"subgrid"}],["grid-cols-subgrid",{"grid-template-columns":"subgrid"}]];var pr=["auto","hidden","clip","visible","scroll","overlay",..._],Wi=[[/^(?:overflow|of)-(.+)$/,([,e])=>pr.includes(e)?{overflow:e}:void 0,{autocomplete:[`(overflow|of)-(${pr.join("|")})`,`(overflow|of)-(x|y)-(${pr.join("|")})`]}],[/^(?:overflow|of)-([xy])-(.+)$/,([,e,t])=>pr.includes(t)?{[`overflow-${e}`]:t}:void 0]];var Ui=[[/^(?:position-|pos-)?(relative|absolute|fixed|sticky)$/,([,e])=>({position:e}),{autocomplete:["(position|pos)-<position>","(position|pos)-<globalKeyword>","<position>"]}],[/^(?:position-|pos-)([-\w]+)$/,([,e])=>_.includes(e)?{position:e}:void 0],[/^(?:position-|pos-)?(static)$/,([,e])=>({position:e})]],Ln=[["justify-start",{"justify-content":"flex-start"}],["justify-end",{"justify-content":"flex-end"}],["justify-center",{"justify-content":"center"}],["justify-between",{"justify-content":"space-between"}],["justify-around",{"justify-content":"space-around"}],["justify-evenly",{"justify-content":"space-evenly"}],["justify-stretch",{"justify-content":"stretch"}],["justify-left",{"justify-content":"left"}],["justify-right",{"justify-content":"right"}],...U("justify","justify-content"),["justify-items-start",{"justify-items":"start"}],["justify-items-end",{"justify-items":"end"}],["justify-items-center",{"justify-items":"center"}],["justify-items-stretch",{"justify-items":"stretch"}],...U("justify-items"),["justify-self-auto",{"justify-self":"auto"}],["justify-self-start",{"justify-self":"start"}],["justify-self-end",{"justify-self":"end"}],["justify-self-center",{"justify-self":"center"}],["justify-self-stretch",{"justify-self":"stretch"}],...U("justify-self")],Bi=[[/^order-(.+)$/,([,e])=>({order:f.bracket.cssvar.number(e)})],["order-first",{order:"-9999"}],["order-last",{order:"9999"}],["order-none",{order:"0"}]],Wn=[["content-center",{"align-content":"center"}],["content-start",{"align-content":"flex-start"}],["content-end",{"align-content":"flex-end"}],["content-between",{"align-content":"space-between"}],["content-around",{"align-content":"space-around"}],["content-evenly",{"align-content":"space-evenly"}],...U("content","align-content"),["items-start",{"align-items":"flex-start"}],["items-end",{"align-items":"flex-end"}],["items-center",{"align-items":"center"}],["items-baseline",{"align-items":"baseline"}],["items-stretch",{"align-items":"stretch"}],...U("items","align-items"),["self-auto",{"align-self":"auto"}],["self-start",{"align-self":"flex-start"}],["self-end",{"align-self":"flex-end"}],["self-center",{"align-self":"center"}],["self-stretch",{"align-self":"stretch"}],["self-baseline",{"align-self":"baseline"}],...U("self","align-self")],Un=[["place-content-center",{"place-content":"center"}],["place-content-start",{"place-content":"start"}],["place-content-end",{"place-content":"end"}],["place-content-between",{"place-content":"space-between"}],["place-content-around",{"place-content":"space-around"}],["place-content-evenly",{"place-content":"space-evenly"}],["place-content-stretch",{"place-content":"stretch"}],...U("place-content"),["place-items-start",{"place-items":"start"}],["place-items-end",{"place-items":"end"}],["place-items-center",{"place-items":"center"}],["place-items-stretch",{"place-items":"stretch"}],...U("place-items"),["place-self-auto",{"place-self":"auto"}],["place-self-start",{"place-self":"start"}],["place-self-end",{"place-self":"end"}],["place-self-center",{"place-self":"center"}],["place-self-stretch",{"place-self":"stretch"}],...U("place-self")],Di=[...Ln,...Wn,...Un].flatMap(([e,t])=>[[`flex-${e}`,t],[`grid-${e}`,t]]);function _n(e,{theme:t}){return t.spacing?.[e]??f.bracket.cssvar.global.auto.fraction.rem(e)}function ut([,e,t],r){let n=_n(t,r);if(n!=null&&e in En)return En[e].map(o=>[o.slice(1),n])}var Ii=[[/^(?:position-|pos-)?inset-(.+)$/,([,e],t)=>({inset:_n(e,t)}),{autocomplete:["(position|pos)-inset-<directions>-$spacing","(position|pos)-inset-(block|inline)-$spacing","(position|pos)-inset-(bs|be|is|ie)-$spacing","(position|pos)-(top|left|right|bottom)-$spacing"]}],[/^(?:position-|pos-)?(start|end)-(.+)$/,ut],[/^(?:position-|pos-)?inset-([xy])-(.+)$/,ut],[/^(?:position-|pos-)?inset-([rltbse])-(.+)$/,ut],[/^(?:position-|pos-)?inset-(block|inline)-(.+)$/,ut],[/^(?:position-|pos-)?inset-([bi][se])-(.+)$/,ut],[/^(?:position-|pos-)?(top|left|right|bottom)-(.+)$/,([,e,t],r)=>({[e]:_n(t,r)})]],Ni=[["float-left",{float:"left"}],["float-right",{float:"right"}],["float-start",{float:"inline-start"}],["float-end",{float:"inline-end"}],["float-none",{float:"none"}],...U("float"),["clear-left",{clear:"left"}],["clear-right",{clear:"right"}],["clear-both",{clear:"both"}],["clear-start",{clear:"inline-start"}],["clear-end",{clear:"inline-end"}],["clear-none",{clear:"none"}],...U("clear")],Ki=[[/^(?:position-|pos-)?z([\d.]+)$/,([,e])=>({"z-index":f.number(e)})],[/^(?:position-|pos-)?z-(.+)$/,([,e],{theme:t})=>({"z-index":t.zIndex?.[e]??f.bracket.cssvar.global.auto.number(e)}),{autocomplete:"z-<num>"}]],Gi=[["box-border",{"box-sizing":"border-box"}],["box-content",{"box-sizing":"content-box"}],...U("box","box-sizing")];var Hi=[[/^(where|\?)$/,(e,{constructCSS:t,generator:r})=>{if(r.userConfig.envMode==="dev")return`@keyframes __un_qm{0%{box-shadow:inset 4px 4px #ff1e90, inset -4px -4px #ff1e90}100%{box-shadow:inset 8px 8px #3399ff, inset -8px -8px #3399ff}} ${t({animation:"__un_qm 0.5s ease-in-out alternate infinite"})}`}]];var sp=["auto","default","none","context-menu","help","pointer","progress","wait","cell","crosshair","text","vertical-text","alias","copy","move","no-drop","not-allowed","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"],ap=["none","strict","content","size","inline-size","layout","style","paint"],dr=" ",qi=[["inline",{display:"inline"}],["block",{display:"block"}],["inline-block",{display:"inline-block"}],["contents",{display:"contents"}],["flow-root",{display:"flow-root"}],["list-item",{display:"list-item"}],["hidden",{display:"none"}],[/^display-(.+)$/,([,e])=>({display:f.bracket.cssvar.global(e)})]],Yi=[["visible",{visibility:"visible"}],["invisible",{visibility:"hidden"}],["backface-visible",{"backface-visibility":"visible"}],["backface-hidden",{"backface-visibility":"hidden"}],...U("backface","backface-visibility")],Xi=[[/^cursor-(.+)$/,([,e])=>({cursor:f.bracket.cssvar.global(e)})],...sp.map(e=>[`cursor-${e}`,{cursor:e}])],Zi=[[/^contain-(.*)$/,([,e])=>f.bracket(e)!=null?{contain:f.bracket(e).split(" ").map(t=>f.cssvar.fraction(t)??t).join(" ")}:ap.includes(e)?{contain:e}:void 0]],Ji=[["pointer-events-auto",{"pointer-events":"auto"}],["pointer-events-none",{"pointer-events":"none"}],...U("pointer-events")],Qi=[["resize-x",{resize:"horizontal"}],["resize-y",{resize:"vertical"}],["resize",{resize:"both"}],["resize-none",{resize:"none"}],...U("resize")],es=[["select-auto",{"-webkit-user-select":"auto","user-select":"auto"}],["select-all",{"-webkit-user-select":"all","user-select":"all"}],["select-text",{"-webkit-user-select":"text","user-select":"text"}],["select-none",{"-webkit-user-select":"none","user-select":"none"}],...U("select","user-select")],ts=[[/^(?:whitespace-|ws-)([-\w]+)$/,([,e])=>["normal","nowrap","pre","pre-line","pre-wrap","break-spaces",..._].includes(e)?{"white-space":e}:void 0,{autocomplete:"(whitespace|ws)-(normal|nowrap|pre|pre-line|pre-wrap|break-spaces)"}]],rs=[[/^intrinsic-size-(.+)$/,([,e])=>({"contain-intrinsic-size":f.bracket.cssvar.global.fraction.rem(e)}),{autocomplete:"intrinsic-size-<num>"}],["content-visibility-visible",{"content-visibility":"visible"}],["content-visibility-hidden",{"content-visibility":"hidden"}],["content-visibility-auto",{"content-visibility":"auto"}],...U("content-visibility")],ns=[[/^content-(.+)$/,([,e])=>({content:f.bracket.cssvar(e)})],["content-empty",{content:'""'}],["content-none",{content:"none"}]],os=[["break-normal",{"overflow-wrap":"normal","word-break":"normal"}],["break-words",{"overflow-wrap":"break-word"}],["break-all",{"word-break":"break-all"}],["break-keep",{"word-break":"keep-all"}],["break-anywhere",{"overflow-wrap":"anywhere"}]],is=[["text-wrap",{"text-wrap":"wrap"}],["text-nowrap",{"text-wrap":"nowrap"}],["text-balance",{"text-wrap":"balance"}],["text-pretty",{"text-wrap":"pretty"}]],ss=[["truncate",{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}],["text-truncate",{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}],["text-ellipsis",{"text-overflow":"ellipsis"}],["text-clip",{"text-overflow":"clip"}]],as=[["case-upper",{"text-transform":"uppercase"}],["case-lower",{"text-transform":"lowercase"}],["case-capital",{"text-transform":"capitalize"}],["case-normal",{"text-transform":"none"}],...U("case","text-transform")],cs=[["italic",{"font-style":"italic"}],["not-italic",{"font-style":"normal"}],["font-italic",{"font-style":"italic"}],["font-not-italic",{"font-style":"normal"}],["oblique",{"font-style":"oblique"}],["not-oblique",{"font-style":"normal"}],["font-oblique",{"font-style":"oblique"}],["font-not-oblique",{"font-style":"normal"}]],ls=[["antialiased",{"-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale"}],["subpixel-antialiased",{"-webkit-font-smoothing":"auto","-moz-osx-font-smoothing":"auto"}]];var Bn={"--un-ring-inset":dr,"--un-ring-offset-width":"0px","--un-ring-offset-color":"#fff","--un-ring-width":"0px","--un-ring-color":"rgb(147 197 253 / 0.5)","--un-shadow":"0 0 rgb(0 0 0 / 0)"},cp=Object.keys(Bn),us=[[/^ring(?:-(.+))?$/,([,e],{theme:t})=>{let r=t.ringWidth?.[e||"DEFAULT"]??f.px(e||"1");if(r)return{"--un-ring-width":r,"--un-ring-offset-shadow":"var(--un-ring-inset) 0 0 0 var(--un-ring-offset-width) var(--un-ring-offset-color)","--un-ring-shadow":"var(--un-ring-inset) 0 0 0 calc(var(--un-ring-width) + var(--un-ring-offset-width)) var(--un-ring-color)","box-shadow":"var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow)"}},{custom:{preflightKeys:cp},autocomplete:"ring-$ringWidth"}],[/^ring-(?:width-|size-)(.+)$/,fs,{autocomplete:"ring-(width|size)-$lineWidth"}],["ring-offset",{"--un-ring-offset-width":"1px"}],[/^ring-offset-(?:width-|size-)?(.+)$/,([,e],{theme:t})=>({"--un-ring-offset-width":t.lineWidth?.[e]??f.bracket.cssvar.px(e)}),{autocomplete:"ring-offset-(width|size)-$lineWidth"}],[/^ring-(.+)$/,lp,{autocomplete:"ring-$colors"}],[/^ring-op(?:acity)?-?(.+)$/,([,e])=>({"--un-ring-opacity":f.bracket.percent.cssvar(e)}),{autocomplete:"ring-(op|opacity)-<percent>"}],[/^ring-offset-(.+)$/,K("--un-ring-offset-color","ring-offset","borderColor"),{autocomplete:"ring-offset-$colors"}],[/^ring-offset-op(?:acity)?-?(.+)$/,([,e])=>({"--un-ring-offset-opacity":f.bracket.percent.cssvar(e)}),{autocomplete:"ring-offset-(op|opacity)-<percent>"}],["ring-inset",{"--un-ring-inset":"inset"}]];function fs([,e],{theme:t}){return{"--un-ring-width":t.ringWidth?.[e]??f.bracket.cssvar.px(e)}}function lp(e,t){return se(f.bracket(e[1]))?fs(e,t):K("--un-ring-color","ring","borderColor")(e,t)}var Dn={"--un-ring-offset-shadow":"0 0 rgb(0 0 0 / 0)","--un-ring-shadow":"0 0 rgb(0 0 0 / 0)","--un-shadow-inset":dr,"--un-shadow":"0 0 rgb(0 0 0 / 0)"},up=Object.keys(Dn),ps=[[/^shadow(?:-(.+))?$/,(e,t)=>{let[,r]=e,{theme:n}=t,o=n.boxShadow?.[r||"DEFAULT"],i=r?f.bracket.cssvar(r):void 0;return(o!=null||i!=null)&&!lr(i,n,"shadowColor")?{"--un-shadow":cr(o||i,"--un-shadow-color").join(","),"box-shadow":"var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow)"}:K("--un-shadow-color","shadow","shadowColor")(e,t)},{custom:{preflightKeys:up},autocomplete:["shadow-$colors","shadow-$boxShadow"]}],[/^shadow-op(?:acity)?-?(.+)$/,([,e])=>({"--un-shadow-opacity":f.bracket.percent.cssvar(e)}),{autocomplete:"shadow-(op|opacity)-<percent>"}],["shadow-inset",{"--un-shadow-inset":"inset"}]];var fp={h:"height",w:"width",inline:"inline-size",block:"block-size"};function Ae(e,t){return`${e||""}${fp[t]}`}function mr(e,t,r,n){let o=Ae(e,t).replace(/-(\w)/g,(s,a)=>a.toUpperCase()),i=r[o]?.[n];if(i!=null)return i;switch(n){case"fit":case"max":case"min":return`${n}-content`}return f.bracket.cssvar.global.auto.fraction.rem(n)}var ms=[[/^size-(min-|max-)?(.+)$/,([,e,t],{theme:r})=>({[Ae(e,"w")]:mr(e,"w",r,t),[Ae(e,"h")]:mr(e,"h",r,t)})],[/^(?:size-)?(min-|max-)?([wh])-?(.+)$/,([,e,t,r],{theme:n})=>({[Ae(e,t)]:mr(e,t,n,r)})],[/^(?:size-)?(min-|max-)?(block|inline)-(.+)$/,([,e,t,r],{theme:n})=>({[Ae(e,t)]:mr(e,t,n,r)}),{autocomplete:["(w|h)-$width|height|maxWidth|maxHeight|minWidth|minHeight|inlineSize|blockSize|maxInlineSize|maxBlockSize|minInlineSize|minBlockSize","(block|inline)-$width|height|maxWidth|maxHeight|minWidth|minHeight|inlineSize|blockSize|maxInlineSize|maxBlockSize|minInlineSize|minBlockSize","(max|min)-(w|h|block|inline)","(max|min)-(w|h|block|inline)-$width|height|maxWidth|maxHeight|minWidth|minHeight|inlineSize|blockSize|maxInlineSize|maxBlockSize|minInlineSize|minBlockSize","(w|h)-full","(max|min)-(w|h)-full"]}],[/^(?:size-)?(min-|max-)?(h)-screen-(.+)$/,([,e,t,r],n)=>({[Ae(e,t)]:ds(n,r,"verticalBreakpoints")})],[/^(?:size-)?(min-|max-)?(w)-screen-(.+)$/,([,e,t,r],n)=>({[Ae(e,t)]:ds(n,r)}),{autocomplete:["(w|h)-screen","(min|max)-(w|h)-screen","h-screen-$verticalBreakpoints","(min|max)-h-screen-$verticalBreakpoints","w-screen-$breakpoints","(min|max)-w-screen-$breakpoints"]}]];function ds(e,t,r="breakpoints"){let n=ur(e,r);if(n)return n.find(o=>o.point===t)?.size}function pp(e){if(/^\d+\/\d+$/.test(e))return e;switch(e){case"square":return"1/1";case"video":return"16/9"}return f.bracket.cssvar.global.auto.number(e)}var hs=[[/^(?:size-)?aspect-(?:ratio-)?(.+)$/,([,e])=>({"aspect-ratio":pp(e)}),{autocomplete:["aspect-(square|video|ratio)","aspect-ratio-(square|video)"]}]];var gs=[[/^pa?()-?(.+)$/,re("padding"),{autocomplete:["(m|p)<num>","(m|p)-<num>"]}],[/^p-?xy()()$/,re("padding"),{autocomplete:"(m|p)-(xy)"}],[/^p-?([xy])(?:-?(.+))?$/,re("padding")],[/^p-?([rltbse])(?:-?(.+))?$/,re("padding"),{autocomplete:"(m|p)<directions>-<num>"}],[/^p-(block|inline)(?:-(.+))?$/,re("padding"),{autocomplete:"(m|p)-(block|inline)-<num>"}],[/^p-?([bi][se])(?:-?(.+))?$/,re("padding"),{autocomplete:"(m|p)-(bs|be|is|ie)-<num>"}]],bs=[[/^ma?()-?(.+)$/,re("margin")],[/^m-?xy()()$/,re("margin")],[/^m-?([xy])(?:-?(.+))?$/,re("margin")],[/^m-?([rltbse])(?:-?(.+))?$/,re("margin")],[/^m-(block|inline)(?:-(.+))?$/,re("margin")],[/^m-?([bi][se])(?:-?(.+))?$/,re("margin")]];var xs=[[/^fill-(.+)$/,K("fill","fill","backgroundColor"),{autocomplete:"fill-$colors"}],[/^fill-op(?:acity)?-?(.+)$/,([,e])=>({"--un-fill-opacity":f.bracket.percent.cssvar(e)}),{autocomplete:"fill-(op|opacity)-<percent>"}],["fill-none",{fill:"none"}],[/^stroke-(?:width-|size-)?(.+)$/,ys,{autocomplete:["stroke-width-$lineWidth","stroke-size-$lineWidth"]}],[/^stroke-dash-(.+)$/,([,e])=>({"stroke-dasharray":f.bracket.cssvar.number(e)}),{autocomplete:"stroke-dash-<num>"}],[/^stroke-offset-(.+)$/,([,e],{theme:t})=>({"stroke-dashoffset":t.lineWidth?.[e]??f.bracket.cssvar.px.numberWithUnit(e)}),{autocomplete:"stroke-offset-$lineWidth"}],[/^stroke-(.+)$/,dp,{autocomplete:"stroke-$colors"}],[/^stroke-op(?:acity)?-?(.+)$/,([,e])=>({"--un-stroke-opacity":f.bracket.percent.cssvar(e)}),{autocomplete:"stroke-(op|opacity)-<percent>"}],["stroke-cap-square",{"stroke-linecap":"square"}],["stroke-cap-round",{"stroke-linecap":"round"}],["stroke-cap-auto",{"stroke-linecap":"butt"}],["stroke-join-arcs",{"stroke-linejoin":"arcs"}],["stroke-join-bevel",{"stroke-linejoin":"bevel"}],["stroke-join-clip",{"stroke-linejoin":"miter-clip"}],["stroke-join-round",{"stroke-linejoin":"round"}],["stroke-join-auto",{"stroke-linejoin":"miter"}],["stroke-none",{stroke:"none"}]];function ys([,e],{theme:t}){return{"stroke-width":t.lineWidth?.[e]??f.bracket.cssvar.fraction.px.number(e)}}function dp(e,t){return se(f.bracket(e[1]))?ys(e,t):K("stroke","stroke","borderColor")(e,t)}var hr=["translate","rotate","scale"],mp=["translateX(var(--un-translate-x))","translateY(var(--un-translate-y))","rotate(var(--un-rotate))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))"].join(" "),Be=["translateX(var(--un-translate-x))","translateY(var(--un-translate-y))","translateZ(var(--un-translate-z))","rotate(var(--un-rotate))","rotateX(var(--un-rotate-x))","rotateY(var(--un-rotate-y))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))","scaleZ(var(--un-scale-z))"].join(" "),hp=["translate3d(var(--un-translate-x), var(--un-translate-y), var(--un-translate-z))","rotate(var(--un-rotate))","rotateX(var(--un-rotate-x))","rotateY(var(--un-rotate-y))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))","scaleZ(var(--un-scale-z))"].join(" "),In={"--un-rotate":0,"--un-rotate-x":0,"--un-rotate-y":0,"--un-rotate-z":0,"--un-scale-x":1,"--un-scale-y":1,"--un-scale-z":1,"--un-skew-x":0,"--un-skew-y":0,"--un-translate-x":0,"--un-translate-y":0,"--un-translate-z":0},he=Object.keys(In),Ss=[[/^(?:transform-)?origin-(.+)$/,([,e])=>({"transform-origin":nt[e]??f.bracket.cssvar(e)}),{autocomplete:[`transform-origin-(${Object.keys(nt).join("|")})`,`origin-(${Object.keys(nt).join("|")})`]}],[/^(?:transform-)?perspect(?:ive)?-(.+)$/,([,e])=>{let t=f.bracket.cssvar.px.numberWithUnit(e);if(t!=null)return{"-webkit-perspective":t,perspective:t}}],[/^(?:transform-)?perspect(?:ive)?-origin-(.+)$/,([,e])=>{let t=f.bracket.cssvar(e)??(e.length>=3?nt[e]:void 0);if(t!=null)return{"-webkit-perspective-origin":t,"perspective-origin":t}}],[/^(?:transform-)?translate-()(.+)$/,$s,{custom:{preflightKeys:he}}],[/^(?:transform-)?translate-([xyz])-(.+)$/,$s,{custom:{preflightKeys:he}}],[/^(?:transform-)?rotate-()(.+)$/,ws,{custom:{preflightKeys:he}}],[/^(?:transform-)?rotate-([xyz])-(.+)$/,ws,{custom:{preflightKeys:he}}],[/^(?:transform-)?skew-()(.+)$/,ks,{custom:{preflightKeys:he}}],[/^(?:transform-)?skew-([xy])-(.+)$/,ks,{custom:{preflightKeys:he},autocomplete:["transform-skew-(x|y)-<percent>","skew-(x|y)-<percent>"]}],[/^(?:transform-)?scale-()(.+)$/,vs,{custom:{preflightKeys:he}}],[/^(?:transform-)?scale-([xyz])-(.+)$/,vs,{custom:{preflightKeys:he},autocomplete:[`transform-(${hr.join("|")})-<percent>`,`transform-(${hr.join("|")})-(x|y|z)-<percent>`,`(${hr.join("|")})-<percent>`,`(${hr.join("|")})-(x|y|z)-<percent>`]}],[/^(?:transform-)?preserve-3d$/,()=>({"transform-style":"preserve-3d"})],[/^(?:transform-)?preserve-flat$/,()=>({"transform-style":"flat"})],["transform",{transform:Be},{custom:{preflightKeys:he}}],["transform-cpu",{transform:mp},{custom:{preflightKeys:["--un-translate-x","--un-translate-y","--un-rotate","--un-rotate-z","--un-skew-x","--un-skew-y","--un-scale-x","--un-scale-y"]}}],["transform-gpu",{transform:hp},{custom:{preflightKeys:he}}],["transform-none",{transform:"none"}],...U("transform")];function $s([,e,t],{theme:r}){let n=r.spacing?.[t]??f.bracket.cssvar.fraction.rem(t);if(n!=null)return[...fr(e,n,"translate"),["transform",Be]]}function vs([,e,t]){let r=f.bracket.cssvar.fraction.percent(t);if(r!=null)return[...fr(e,r,"scale"),["transform",Be]]}function ws([,e="",t]){let r=f.bracket.cssvar.degree(t);if(r!=null)return e?{"--un-rotate":0,[`--un-rotate-${e}`]:r,transform:Be}:{"--un-rotate-x":0,"--un-rotate-y":0,"--un-rotate-z":0,"--un-rotate":r,transform:Be}}function ks([,e,t]){let r=f.bracket.cssvar.degree(t);if(r!=null)return[...fr(e,r,"skew"),["transform",Be]]}function Cs(e,t){let r;if(f.cssvar(e)!=null)r=f.cssvar(e);else{e.startsWith("[")&&e.endsWith("]")&&(e=e.slice(1,-1));let n=e.split(",").map(o=>t.transitionProperty?.[o]??f.properties(o));n.every(Boolean)&&(r=n.join(","))}return r}var Rs=[[/^transition(?:-(\D+?))?(?:-(\d+))?$/,([,e,t],{theme:r})=>{if(!e&&!t)return{"transition-property":r.transitionProperty?.DEFAULT,"transition-timing-function":r.easing?.DEFAULT,"transition-duration":r.duration?.DEFAULT??f.time("150")};if(e!=null){let n=Cs(e,r),o=r.duration?.[t||"DEFAULT"]??f.time(t||"150");if(n)return{"transition-property":n,"transition-timing-function":r.easing?.DEFAULT,"transition-duration":o}}else if(t!=null)return{"transition-property":r.transitionProperty?.DEFAULT,"transition-timing-function":r.easing?.DEFAULT,"transition-duration":r.duration?.[t]??f.time(t)}},{autocomplete:"transition-$transitionProperty-$duration"}],[/^(?:transition-)?duration-(.+)$/,([,e],{theme:t})=>({"transition-duration":t.duration?.[e||"DEFAULT"]??f.bracket.cssvar.time(e)}),{autocomplete:["transition-duration-$duration","duration-$duration"]}],[/^(?:transition-)?delay-(.+)$/,([,e],{theme:t})=>({"transition-delay":t.duration?.[e||"DEFAULT"]??f.bracket.cssvar.time(e)}),{autocomplete:["transition-delay-$duration","delay-$duration"]}],[/^(?:transition-)?ease(?:-(.+))?$/,([,e],{theme:t})=>({"transition-timing-function":t.easing?.[e||"DEFAULT"]??f.bracket.cssvar(e)}),{autocomplete:["transition-ease-(linear|in|out|in-out|DEFAULT)","ease-(linear|in|out|in-out|DEFAULT)"]}],[/^(?:transition-)?property-(.+)$/,([,e],{theme:t})=>{let r=f.global(e)||Cs(e,t);if(r)return{"transition-property":r}},{autocomplete:[`transition-property-(${[..._].join("|")})`,"transition-property-$transitionProperty","property-$transitionProperty"]}],["transition-none",{transition:"none"}],...U("transition"),["transition-discrete",{"transition-behavior":"allow-discrete"}],["transition-normal",{"transition-behavior":"normal"}]];var Es=[[/^text-(.+)$/,bp,{autocomplete:"text-$fontSize"}],[/^(?:text|font)-size-(.+)$/,Os,{autocomplete:"text-size-$fontSize"}],[/^text-(?:color-)?(.+)$/,gp,{autocomplete:"text-$colors"}],[/^(?:color|c)-(.+)$/,K("color","text","textColor"),{autocomplete:"(color|c)-$colors"}],[/^(?:text|color|c)-(.+)$/,([,e])=>_.includes(e)?{color:e}:void 0,{autocomplete:`(text|color|c)-(${_.join("|")})`}],[/^(?:text|color|c)-op(?:acity)?-?(.+)$/,([,e])=>({"--un-text-opacity":f.bracket.percent.cssvar(e)}),{autocomplete:"(text|color|c)-(op|opacity)-<percent>"}],[/^(?:font|fw)-?([^-]+)$/,([,e],{theme:t})=>({"font-weight":t.fontWeight?.[e]||f.bracket.global.number(e)}),{autocomplete:["(font|fw)-(100|200|300|400|500|600|700|800|900)","(font|fw)-$fontWeight"]}],[/^(?:font-)?(?:leading|lh|line-height)-(.+)$/,([,e],{theme:t})=>({"line-height":Nn(e,t,"lineHeight")}),{autocomplete:"(leading|lh|line-height)-$lineHeight"}],["font-synthesis-weight",{"font-synthesis":"weight"}],["font-synthesis-style",{"font-synthesis":"style"}],["font-synthesis-small-caps",{"font-synthesis":"small-caps"}],["font-synthesis-none",{"font-synthesis":"none"}],[/^font-synthesis-(.+)$/,([,e])=>({"font-synthesis":f.bracket.cssvar.global(e)})],[/^(?:font-)?tracking-(.+)$/,([,e],{theme:t})=>({"letter-spacing":t.letterSpacing?.[e]||f.bracket.cssvar.global.rem(e)}),{autocomplete:"tracking-$letterSpacing"}],[/^(?:font-)?word-spacing-(.+)$/,([,e],{theme:t})=>({"word-spacing":t.wordSpacing?.[e]||f.bracket.cssvar.global.rem(e)}),{autocomplete:"word-spacing-$wordSpacing"}],["font-stretch-normal",{"font-stretch":"normal"}],["font-stretch-ultra-condensed",{"font-stretch":"ultra-condensed"}],["font-stretch-extra-condensed",{"font-stretch":"extra-condensed"}],["font-stretch-condensed",{"font-stretch":"condensed"}],["font-stretch-semi-condensed",{"font-stretch":"semi-condensed"}],["font-stretch-semi-expanded",{"font-stretch":"semi-expanded"}],["font-stretch-expanded",{"font-stretch":"expanded"}],["font-stretch-extra-expanded",{"font-stretch":"extra-expanded"}],["font-stretch-ultra-expanded",{"font-stretch":"ultra-expanded"}],[/^font-stretch-(.+)$/,([,e])=>({"font-stretch":f.bracket.cssvar.fraction.global(e)}),{autocomplete:"font-stretch-<percentage>"}],[/^font-(.+)$/,([,e],{theme:t})=>({"font-family":t.fontFamily?.[e]||f.bracket.cssvar.global(e)}),{autocomplete:"font-$fontFamily"}]],Ts=[[/^tab(?:-(.+))?$/,([,e])=>{let t=f.bracket.cssvar.global.number(e||"4");if(t!=null)return{"-moz-tab-size":t,"-o-tab-size":t,"tab-size":t}}]],js=[[/^indent(?:-(.+))?$/,([,e],{theme:t})=>({"text-indent":t.textIndent?.[e||"DEFAULT"]||f.bracket.cssvar.global.fraction.rem(e)}),{autocomplete:"indent-$textIndent"}]],zs=[[/^text-stroke(?:-(.+))?$/,([,e],{theme:t})=>({"-webkit-text-stroke-width":t.textStrokeWidth?.[e||"DEFAULT"]||f.bracket.cssvar.px(e)}),{autocomplete:"text-stroke-$textStrokeWidth"}],[/^text-stroke-(.+)$/,K("-webkit-text-stroke-color","text-stroke","borderColor"),{autocomplete:"text-stroke-$colors"}],[/^text-stroke-op(?:acity)?-?(.+)$/,([,e])=>({"--un-text-stroke-opacity":f.bracket.percent.cssvar(e)}),{autocomplete:"text-stroke-(op|opacity)-<percent>"}]],As=[[/^text-shadow(?:-(.+))?$/,([,e],{theme:t})=>{let r=t.textShadow?.[e||"DEFAULT"];return r!=null?{"--un-text-shadow":cr(r,"--un-text-shadow-color").join(","),"text-shadow":"var(--un-text-shadow)"}:{"text-shadow":f.bracket.cssvar.global(e)}},{autocomplete:"text-shadow-$textShadow"}],[/^text-shadow-color-(.+)$/,K("--un-text-shadow-color","text-shadow","shadowColor"),{autocomplete:"text-shadow-color-$colors"}],[/^text-shadow-color-op(?:acity)?-?(.+)$/,([,e])=>({"--un-text-shadow-opacity":f.bracket.percent.cssvar(e)}),{autocomplete:"text-shadow-color-(op|opacity)-<percent>"}]];function Nn(e,t,r){return t[r]?.[e]||f.bracket.cssvar.global.rem(e)}function Os([,e],{theme:t}){let n=C(t.fontSize?.[e])?.[0]??f.bracket.cssvar.global.rem(e);if(n!=null)return{"font-size":n}}function gp(e,t){return se(f.bracket(e[1]))?Os(e,t):K("color","text","textColor")(e,t)}function bp([,e="base"],{theme:t}){let r=Vn(e,"length");if(!r)return;let[n,o]=r,i=C(t.fontSize?.[n]),s=o?Nn(o,t,"lineHeight"):void 0;if(i?.[0]){let[c,l,p]=i;return typeof l=="object"?{"font-size":c,...l}:{"font-size":c,"line-height":s??l??"1","letter-spacing":p?Nn(p,t,"letterSpacing"):void 0}}let a=f.bracketOfLength.rem(n);return s&&a?{"font-size":a,"line-height":s}:{"font-size":f.bracketOfLength.rem(e)}}var xp={backface:"backface-visibility",break:"word-break",case:"text-transform",content:"align-content",fw:"font-weight",items:"align-items",justify:"justify-content",select:"user-select",self:"align-self",vertical:"vertical-align",visible:"visibility",whitespace:"white-space",ws:"white-space"},Ps=[[/^(.+?)-(\$.+)$/,([,e,t])=>{let r=xp[e];if(r)return{[r]:f.cssvar(t)}}]],Vs=[[/^\[(.*)\]$/,([e,t])=>{if(!t.includes(":"))return;let[r,...n]=t.split(":"),o=n.join(":");if(!$p(t)&&/^[a-z-]+$/.test(r)&&yp(o)){let i=f.bracket(`[${o}]`);if(i)return{[r]:i}}}]];function yp(e){let t=0;function r(n){for(;t<e.length;)if(t+=1,e[t]===n)return!0;return!1}for(t=0;t<e.length;t++){let n=e[t];if("\"`'".includes(n)){if(!r(n))return!1}else if(n==="("){if(!r(")"))return!1}else if("[]{}:".includes(n))return!1}return!0}function $p(e){if(!e.includes("://"))return!1;try{return new URL(e).host!==""}catch{return!1}}var Ms=[Ps,Vs,Zi,Ji,Yi,Ui,Ii,Ki,Bi,Li,Ni,bs,Gi,qi,hs,ms,Fi,Ss,Xi,es,Qi,Ci,Un,Wn,Ln,_i,Di,Wi,ss,ts,os,Ti,zi,Ai,xs,gs,bi,js,is,gi,Es,as,cs,Vi,ls,Ts,zs,As,ji,ps,ki,us,Rs,Ri,rs,ns,Oi,Hi].flat(1);var Fs={position:["relative","absolute","fixed","sticky","static"],globalKeyword:_};var Kn={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"},light:{50:"#fdfdfd",100:"#fcfcfc",200:"#fafafa",300:"#f8f9fa",400:"#f6f6f6",500:"#f2f2f2",600:"#f1f3f5",700:"#e9ecef",800:"#dee2e6",900:"#dde1e3",950:"#d8dcdf"},dark:{50:"#4a4a4a",100:"#3c3c3c",200:"#323232",300:"#2d2d2d",400:"#222222",500:"#1f1f1f",600:"#1c1c1e",700:"#1b1b1b",800:"#181818",900:"#0f0f0f",950:"#080808"},get lightblue(){return this.sky},get lightBlue(){return this.sky},get warmgray(){return this.stone},get warmGray(){return this.stone},get truegray(){return this.neutral},get trueGray(){return this.neutral},get coolgray(){return this.gray},get coolGray(){return this.gray},get bluegray(){return this.slate},get blueGray(){return this.slate}};Object.values(Kn).forEach(e=>{typeof e!="string"&&e!==void 0&&(e.DEFAULT=e.DEFAULT||e[400],Object.keys(e).forEach(t=>{let r=+t/100;r===Math.round(r)&&(e[r]=e[t])}))});var _s={DEFAULT:"8px",0:"0",sm:"4px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},Ls={DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],sm:"0 1px 1px rgb(0 0 0 / 0.05)",md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 rgb(0 0 0 / 0)"};var Ws={sans:["ui-sans-serif","system-ui","-apple-system","BlinkMacSystemFont",'"Segoe UI"',"Roboto",'"Helvetica Neue"',"Arial",'"Noto Sans"',"sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'].join(","),serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"].join(","),mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"].join(",")},Us={xs:["0.75rem","1rem"],sm:["0.875rem","1.25rem"],base:["1rem","1.5rem"],lg:["1.125rem","1.75rem"],xl:["1.25rem","1.75rem"],"2xl":["1.5rem","2rem"],"3xl":["1.875rem","2.25rem"],"4xl":["2.25rem","2.5rem"],"5xl":["3rem","1"],"6xl":["3.75rem","1"],"7xl":["4.5rem","1"],"8xl":["6rem","1"],"9xl":["8rem","1"]},Bs={DEFAULT:"1.5rem",xs:"0.5rem",sm:"1rem",md:"1.5rem",lg:"2rem",xl:"2.5rem","2xl":"3rem","3xl":"4rem"},Ds={DEFAULT:"1.5rem",none:"0",sm:"thin",md:"medium",lg:"thick"},Is={DEFAULT:["0 0 1px rgb(0 0 0 / 0.2)","0 0 1px rgb(1 0 5 / 0.1)"],none:"0 0 rgb(0 0 0 / 0)",sm:"1px 1px 3px rgb(36 37 47 / 0.25)",md:["0 1px 2px rgb(30 29 39 / 0.19)","1px 2px 4px rgb(54 64 147 / 0.18)"],lg:["3px 3px 6px rgb(0 0 0 / 0.26)","0 0 5px rgb(15 3 86 / 0.22)"],xl:["1px 1px 3px rgb(0 0 0 / 0.29)","2px 4px 7px rgb(73 64 125 / 0.35)"]},Ns={none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2"},Gn={tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},Ks={thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},Gs=Gn;var Hn={sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},Hs={...Hn},qs={DEFAULT:"1px",none:"0"},Ys={DEFAULT:"1rem",none:"0",xs:"0.75rem",sm:"0.875rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem","7xl":"4.5rem","8xl":"6rem","9xl":"8rem"},Xs={DEFAULT:"150ms",none:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms"},Zs={DEFAULT:"0.25rem",none:"0",sm:"0.125rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},Js={DEFAULT:["var(--un-shadow-inset) 0 1px 3px 0 rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 1px 2px -1px rgb(0 0 0 / 0.1)"],none:"0 0 rgb(0 0 0 / 0)",sm:"var(--un-shadow-inset) 0 1px 2px 0 rgb(0 0 0 / 0.05)",md:["var(--un-shadow-inset) 0 4px 6px -1px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 2px 4px -2px rgb(0 0 0 / 0.1)"],lg:["var(--un-shadow-inset) 0 10px 15px -3px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 4px 6px -4px rgb(0 0 0 / 0.1)"],xl:["var(--un-shadow-inset) 0 20px 25px -5px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 8px 10px -6px rgb(0 0 0 / 0.1)"],"2xl":"var(--un-shadow-inset) 0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)"},Qs={DEFAULT:"3px",none:"0"},ea={auto:"auto"},ta={mouse:"(hover) and (pointer: fine)"};var ra={...In,...Dn,...Bn};var $e={xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",prose:"65ch"},na={auto:"auto",...$e,screen:"100vw"},qn={none:"none",...$e,screen:"100vw"},oa={auto:"auto",...$e,screen:"100vb"},ia={auto:"auto",...$e,screen:"100vi"},sa={auto:"auto",...$e,screen:"100vh"},Yn={none:"none",...$e,screen:"100vh"},Xn={none:"none",...$e,screen:"100vb"},Zn={none:"none",...$e,screen:"100vi"},aa={...$e};var ca={DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},la={none:"none",all:"all",colors:["color","background-color","border-color","text-decoration-color","fill","stroke"].join(","),opacity:"opacity",shadow:"box-shadow",transform:"transform",get DEFAULT(){return[this.colors,"opacity","box-shadow","transform","filter","backdrop-filter"].join(",")}};var ua={width:na,height:sa,maxWidth:qn,maxHeight:Yn,minWidth:qn,minHeight:Yn,inlineSize:ia,blockSize:oa,maxInlineSize:Zn,maxBlockSize:Xn,minInlineSize:Zn,minBlockSize:Xn,colors:Kn,fontFamily:Ws,fontSize:Us,fontWeight:Ks,breakpoints:Hn,verticalBreakpoints:Hs,borderRadius:Zs,lineHeight:Ns,letterSpacing:Gn,wordSpacing:Gs,boxShadow:Js,textIndent:Bs,textShadow:Is,textStrokeWidth:Ds,blur:_s,dropShadow:Ls,easing:ca,transitionProperty:la,lineWidth:qs,spacing:Ys,duration:Xs,ringWidth:Qs,preflightBase:ra,containers:aa,zIndex:ea,media:ta};var fa={name:"aria",match(e,t){let r=F("aria-",e,t.generator.config.separators);if(r){let[n,o]=r,i=f.bracket(n)??t.theme.aria?.[n]??"";if(i)return{matcher:o,selector:s=>`${s}[aria-${i}]`}}}};function gr(e){return{name:`${e}-aria`,match(t,r){let n=F(`${e}-aria-`,t,r.generator.config.separators);if(n){let[o,i]=n,s=f.bracket(o)??r.theme.aria?.[o]??"";if(s)return{matcher:`${e}-[[aria-${s}]]:${i}`}}}}}var pa=[gr("group"),gr("peer"),gr("parent"),gr("previous")];function da(e){let t=e.match(/^-?\d+\.?\d*/)?.[0]||"",r=e.slice(t.length);if(r==="px"){let n=Number.parseFloat(t)-.1;return Number.isNaN(n)?e:`${n}${r}`}return`calc(${e} - 0.1px)`}var ma=/(max|min)-\[([^\]]*)\]:/;function ha(){let e={};return{name:"breakpoints",match(t,r){if(ma.test(t)){let o=t.match(ma);return{matcher:t.replace(o[0],""),handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@media (${o[1]}-width: ${o[2]})`})}}let n=(ur(r)??[]).map(({point:o,size:i},s)=>[o,i,s]);for(let[o,i,s]of n){e[o]||(e[o]=new RegExp(`^((?:([al]t-|[<~]|max-))?${o}(?:${r.generator.config.separators.join("|")}))`));let a=t.match(e[o]);if(!a)continue;let[,c]=a,l=t.slice(c.length);if(l==="container")continue;let p=c.startsWith("lt-")||c.startsWith("<")||c.startsWith("max-"),d=c.startsWith("at-")||c.startsWith("~"),h=3e3;return p?(h-=s+1,{matcher:l,handle:(m,g)=>g({...m,parent:`${m.parent?`${m.parent} $$ `:""}@media (max-width: ${da(i)})`,parentOrder:h})}):(h+=s+1,d&&s<n.length-1?{matcher:l,handle:(m,g)=>g({...m,parent:`${m.parent?`${m.parent} $$ `:""}@media (min-width: ${i}) and (max-width: ${da(n[s+1][1])})`,parentOrder:h})}:{matcher:l,handle:(m,g)=>g({...m,parent:`${m.parent?`${m.parent} $$ `:""}@media (min-width: ${i})`,parentOrder:h})})}},multiPass:!0,autocomplete:"(at-|lt-|max-|)$breakpoints:"}}var ga=[I("*",e=>({selector:`${e.selector} > *`}))];function ft(e,t){return{name:`combinator:${e}`,match(r,n){if(!r.startsWith(e))return;let o=n.generator.config.separators,i=ee(`${e}-`,r,o);if(!i){for(let a of o)if(r.startsWith(`${e}${a}`)){i=["",r.slice(e.length+a.length)];break}if(!i)return}let s=f.bracket(i[0])??"";return s===""&&(s="*"),{matcher:i[1],selector:a=>`${a}${t}${s}`}},multiPass:!0}}var ba=[ft("all"," "),ft("children",">"),ft("next","+"),ft("sibling","+"),ft("siblings","~")];var xa={name:"@",match(e,t){if(e.startsWith("@container"))return;let r=F("@",e,t.generator.config.separators);if(r){let[n,o,i]=r,s=f.bracket(n),a;if(s?a=f.numberWithUnit(s):a=t.theme.containers?.[n]??"",a){let c=1e3+Object.keys(t.theme.containers??{}).indexOf(n);return i&&(c+=1e3),{matcher:o,handle:(l,p)=>p({...l,parent:`${l.parent?`${l.parent} $$ `:""}@container${i?` ${i} `:" "}(min-width: ${a})`,parentOrder:c})}}}},multiPass:!0};function ya(e={}){if(e?.dark==="class"||typeof e.dark=="object"){let{dark:t=".dark",light:r=".light"}=typeof e.dark=="string"?{}:e.dark;return[I("dark",C(t).map(n=>o=>({prefix:`${n} $$ ${o.prefix}`}))),I("light",C(r).map(n=>o=>({prefix:`${n} $$ ${o.prefix}`})))]}return[N("dark","@media (prefers-color-scheme: dark)"),N("light","@media (prefers-color-scheme: light)")]}var $a={name:"data",match(e,t){let r=F("data-",e,t.generator.config.separators);if(r){let[n,o]=r,i=f.bracket(n)??t.theme.data?.[n]??"";if(i)return{matcher:o,selector:s=>`${s}[data-${i}]`}}}};function br(e){return{name:`${e}-data`,match(t,r){let n=F(`${e}-data-`,t,r.generator.config.separators);if(n){let[o,i,s]=n,a=f.bracket(o)??r.theme.data?.[o]??"";if(a)return{matcher:`${e}-[[data-${a}]]${s?`/${s}`:""}:${i}`}}}}}var va=[br("group"),br("peer"),br("parent"),br("previous")];var wa=[I("rtl",e=>({prefix:`[dir="rtl"] $$ ${e.prefix}`})),I("ltr",e=>({prefix:`[dir="ltr"] $$ ${e.prefix}`}))];function ka(){let e;return{name:"important",match(t,r){e||(e=new RegExp(`^(important(?:${r.generator.config.separators.join("|")})|!)`));let n,o=t.match(e);if(o?n=t.slice(o[0].length):t.endsWith("!")&&(n=t.slice(0,-1)),n)return{matcher:n,body:i=>(i.forEach(s=>{s[1]!=null&&(s[1]+=" !important")}),i)}}}}var Sa=N("print","@media print"),Ca={name:"media",match(e,t){let r=F("media-",e,t.generator.config.separators);if(r){let[n,o]=r,i=f.bracket(n)??"";if(i===""&&(i=t.theme.media?.[n]??""),i)return{matcher:o,handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@media ${i}`})}}},multiPass:!0};var Ra={name:"selector",match(e,t){let r=ee("selector-",e,t.generator.config.separators);if(r){let[n,o]=r,i=f.bracket(n);if(i)return{matcher:o,selector:()=>i}}}},Ea={name:"layer",match(e,t){let r=F("layer-",e,t.generator.config.separators);if(r){let[n,o]=r,i=f.bracket(n)??n;if(i)return{matcher:o,handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@layer ${i}`})}}}},Ta={name:"uno-layer",match(e,t){let r=F("uno-layer-",e,t.generator.config.separators);if(r){let[n,o]=r,i=f.bracket(n)??n;if(i)return{matcher:o,layer:i}}}},ja={name:"scope",match(e,t){let r=ee("scope-",e,t.generator.config.separators);if(r){let[n,o]=r,i=f.bracket(n);if(i)return{matcher:o,selector:s=>`${i} $$ ${s}`}}}},za={name:"variables",match(e,t){if(!e.startsWith("["))return;let[r,n]=ye(e,"[","]")??[];if(!(r&&n))return;let o;for(let a of t.generator.config.separators)if(n.startsWith(a)){o=n.slice(a.length);break}if(o==null)return;let i=f.bracket(r)??"",s=i.startsWith("@");if(s||i.includes("&"))return{matcher:o,handle(a,c){let l=s?{parent:`${a.parent?`${a.parent} $$ `:""}${i}`}:{selector:i.replace(/&/g,a.selector)};return c({...a,...l})}}},multiPass:!0},Aa={name:"theme-variables",match(e,t){if(rr(e))return{matcher:e,handle(r,n){return n({...r,entries:JSON.parse(nr(JSON.stringify(r.entries),t.theme))})}}}};var Oa=/^-?[0-9.]+(?:[a-z]+|%)?$/,Pa=/-?[0-9.]+(?:[a-z]+|%)?/,vp=[/\b(opacity|color|flex|backdrop-filter|^filter|transform)\b/];function wp(e){let t=e.match(ot)||e.match(or);if(t){let[r,n]=ue(`(${t[2]})${t[3]}`,"(",")"," ")??[];if(r)return`calc(${t[1]}${r} * -1)${n?` ${n}`:""}`}}var kp=/\b(hue-rotate)\s*(\(.*)/;function Sp(e){let t=e.match(kp);if(t){let[r,n]=ue(t[2],"(",")"," ")??[];if(r){let o=Oa.test(r.slice(1,-1))?r.replace(Pa,i=>i.startsWith("-")?i.slice(1):`-${i}`):`(calc(${r} * -1))`;return`${t[1]}${o}${n?` ${n}`:""}`}}}var Va={name:"negative",match(e){if(e.startsWith("-"))return{matcher:e.slice(1),body:t=>{if(t.find(n=>n[0]===vi))return;let r=!1;return t.forEach(n=>{let o=n[1]?.toString();if(!o||o==="0"||vp.some(a=>a.test(n[0])))return;let i=wp(o);if(i){n[1]=i,r=!0;return}let s=Sp(o);if(s){n[1]=s,r=!0;return}Oa.test(o)&&(n[1]=o.replace(Pa,a=>a.startsWith("-")?a.slice(1):`-${a}`),r=!0)}),r?t:[]}}}};var De=Object.fromEntries([["first-letter","::first-letter"],["first-line","::first-line"],"any-link","link","visited","target",["open","[open]"],"default","checked","indeterminate","placeholder-shown","autofill","optional","required","valid","invalid","user-valid","user-invalid","in-range","out-of-range","read-only","read-write","empty","focus-within","hover","focus","focus-visible","active","enabled","disabled","popover-open","root","empty",["even-of-type",":nth-of-type(even)"],["even",":nth-child(even)"],["odd-of-type",":nth-of-type(odd)"],["odd",":nth-child(odd)"],"first-of-type",["first",":first-child"],"last-of-type",["last",":last-child"],"only-child","only-of-type",["backdrop-element","::backdrop"],["placeholder","::placeholder"],["before","::before"],["after","::after"],["file","::file-selector-button"]].map(e=>Array.isArray(e)?e:[e,`:${e}`])),La=Object.keys(De),Ie=Object.fromEntries([["backdrop","::backdrop"]].map(e=>Array.isArray(e)?e:[e,`:${e}`])),Wa=Object.keys(Ie),Cp=["not","is","where","has"],Ua=Object.fromEntries([["selection",["::selection"," *::selection"]],["marker",["::marker"," *::marker"]]]),Jn=Object.entries(De).filter(([,e])=>!e.startsWith("::")).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|"),Qn=Object.entries(Ie).filter(([,e])=>!e.startsWith("::")).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|"),Oe=Cp.join("|"),Ma=Object.keys(Ua).sort((e,t)=>t.length-e.length).join("|");function Rp(e,t,r){let n=new RegExp(`^(${le(t)}:)(\\S+)${le(r)}\\1`),o,i,s,a,c=d=>{let h=ee(`${e}-`,d,[]);if(!h)return;let[m,g]=h,b=f.bracket(m);if(b==null)return;let $=g.split(o,1)?.[0]??"",S=`${t}${Q($)}`;return[$,d.slice(d.length-(g.length-$.length-1)),b.includes("&")?b.replace(/&/g,S):`${S}${b}`]},l=d=>{let h=d.match(i)||d.match(s);if(!h)return;let[m,g,b]=h,$=h[3]??"",S=De[b]||Ie[b]||`:${b}`;return g&&(S=`:${g}(${S})`),[$,d.slice(m.length),`${t}${Q($)}${S}`,b]},p=d=>{let h=d.match(a);if(!h)return;let[m,g,b]=h,$=h[3]??"",S=`:${g}(${b})`;return[$,d.slice(m.length),`${t}${Q($)}${S}`]};return{name:`pseudo:${e}`,match(d,h){if(o&&i&&s||(o=new RegExp(`(?:${h.generator.config.separators.join("|")})`),i=new RegExp(`^${e}-(?:(?:(${Oe})-)?(${Jn}))(?:(/\\w+))?(?:${h.generator.config.separators.join("|")})`),s=new RegExp(`^${e}-(?:(?:(${Oe})-)?(${Qn}))(?:(/\\w+))?(?:${h.generator.config.separators.filter(v=>v!=="-").join("|")})`),a=new RegExp(`^${e}-(?:(${Oe})-)?\\[(.+)\\](?:(/\\w+))?(?:${h.generator.config.separators.filter(v=>v!=="-").join("|")})`)),!d.startsWith(e))return;let m=c(d)||l(d)||p(d);if(!m)return;let[g,b,$,S=""]=m;return{matcher:b,handle:(v,E)=>E({...v,prefix:`${$}${r}${v.prefix}`.replace(n,"$1$2:"),sort:La.indexOf(S)??Wa.indexOf(S)})}},multiPass:!0}}var Ep=["::-webkit-resizer","::-webkit-scrollbar","::-webkit-scrollbar-button","::-webkit-scrollbar-corner","::-webkit-scrollbar-thumb","::-webkit-scrollbar-track","::-webkit-scrollbar-track-piece","::file-selector-button"],Fa=Object.entries(De).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|"),_a=Object.entries(Ie).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|");function Ba(){let e,t,r;return[{name:"pseudo",match(n,o){e&&t||(e=new RegExp(`^(${Fa})(?:${o.generator.config.separators.join("|")})`),t=new RegExp(`^(${_a})(?:${o.generator.config.separators.filter(s=>s!=="-").join("|")})`));let i=n.match(e)||n.match(t);if(i){let s=De[i[1]]||Ie[i[1]]||`:${i[1]}`,a=La.indexOf(i[1]);return a===-1&&(a=Wa.indexOf(i[1])),a===-1&&(a=void 0),{matcher:n.slice(i[0].length),handle:(c,l)=>{let p=s.includes("::")&&!Ep.includes(s)?{pseudo:`${c.pseudo}${s}`}:{selector:`${c.selector}${s}`};return l({...c,...p,sort:a,noMerge:!0})}}}},multiPass:!0,autocomplete:`(${Fa}|${_a}):`},{name:"pseudo:multi",match(n,o){r||(r=new RegExp(`^(${Ma})(?:${o.generator.config.separators.join("|")})`));let i=n.match(r);if(i)return Ua[i[1]].map(a=>({matcher:n.slice(i[0].length),handle:(c,l)=>l({...c,pseudo:`${c.pseudo}${a}`})}))},multiPass:!1,autocomplete:`(${Ma}):`}]}function Da(){let e,t,r;return{match(n,o){e&&t||(e=new RegExp(`^(${Oe})-(${Jn})(?:${o.generator.config.separators.join("|")})`),t=new RegExp(`^(${Oe})-(${Qn})(?:${o.generator.config.separators.filter(s=>s!=="-").join("|")})`),r=new RegExp(`^(${Oe})-(\\[.+\\])(?:${o.generator.config.separators.filter(s=>s!=="-").join("|")})`));let i=n.match(e)||n.match(t)||n.match(r);if(i){let s=i[1],c=ye(i[2],"[","]")?f.bracket(i[2]):De[i[2]]||Ie[i[2]]||`:${i[2]}`;return{matcher:n.slice(i[0].length),selector:l=>`${l}:${s}(${c})`}}},multiPass:!0,autocomplete:`(${Oe})-(${Jn}|${Qn}):`}}function Ia(e={}){let t=!!e?.attributifyPseudo,r=e?.prefix??"";r=(Array.isArray(r)?r:[r]).filter(Boolean)[0]??"";let n=(o,i)=>Rp(o,t?`[${r}${o}=""]`:`.${r}${o}`,i);return[n("group"," "),n("peer","~"),n("parent",">"),n("previous","+"),n("group-aria"," "),n("peer-aria","~"),n("parent-aria",">"),n("previous-aria","+")]}var Tp=/(part-\[(.+)\]:)(.+)/,Na={match(e){let t=e.match(Tp);if(t){let r=`part(${t[2]})`;return{matcher:e.slice(t[1].length),selector:n=>`${n}::${r}`}}},multiPass:!0};var Ka={name:"starting",match(e){if(e.startsWith("starting:"))return{matcher:e.slice(9),handle:(t,r)=>r({...t,parent:"@starting-style"})}}};var Ga={name:"supports",match(e,t){let r=F("supports-",e,t.generator.config.separators);if(r){let[n,o]=r,i=f.bracket(n)??"";if(i===""&&(i=t.theme.supports?.[n]??""),i)return{matcher:o,handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@supports ${i}`})}}},multiPass:!0};function Ha(e){return[fa,$a,Ea,Ra,Ta,Va,Ka,ka(),Ga,Sa,Ca,ha(),...ba,...Ba(),Da(),...Ia(e),Na,...ya(e),...wa,ja,...ga,xa,za,...va,...pa,Aa]}var eo=(e={})=>(e.dark=e.dark??"class",e.attributifyPseudo=e.attributifyPseudo??!1,e.preflight=e.preflight??!0,e.variablePrefix=e.variablePrefix??"un-",{name:"@unocss/preset-mini",theme:ua,rules:Ms,variants:Ha(e),options:e,prefix:e.prefix,postprocess:jp(e.variablePrefix),preflights:ti(e),extractorDefault:e.arbitraryVariants===!1?void 0:ei(),autocomplete:{shorthands:Fs}}),qa=eo;function jp(e){if(e!=="un-")return t=>{t.entries.forEach(r=>{r[0]=r[0].replace(/^--un-/,`--${e}`),typeof r[1]=="string"&&(r[1]=r[1].replace(/var\(--un-/g,`var(--${e}`))})}}function Ya(e){if(e==null||e===!1)return[];let t=r=>r.startsWith(":is(")&&r.endsWith(")")?r:r.includes("::")?r.replace(/(.*?)((?:\s\*)?::.*)/,":is($1)$2"):`:is(${r})`;return[e===!0?r=>{r.entries.forEach(n=>{n[1]!=null&&!String(n[1]).endsWith("!important")&&(n[1]+=" !important")})}:r=>{r.selector.startsWith(e)||(r.selector=`${e} ${t(r.selector)}`)}]}function Xa(e){return[...C(qa(e).postprocess),...Ya(e.important)]}var Y={l:["-left"],r:["-right"],t:["-top"],b:["-bottom"],s:["-inline-start"],e:["-inline-end"],x:["-left","-right"],y:["-top","-bottom"],"":[""],bs:["-block-start"],be:["-block-end"],is:["-inline-start"],ie:["-inline-end"],block:["-block-start","-block-end"],inline:["-inline-start","-inline-end"]},xr={...Y,s:["-inset-inline-start"],start:["-inset-inline-start"],e:["-inset-inline-end"],end:["-inset-inline-end"],bs:["-inset-block-start"],be:["-inset-block-end"],is:["-inset-inline-start"],ie:["-inset-inline-end"],block:["-inset-block-start","-inset-block-end"],inline:["-inset-inline-start","-inset-inline-end"]},yr={l:["-top-left","-bottom-left"],r:["-top-right","-bottom-right"],t:["-top-left","-top-right"],b:["-bottom-left","-bottom-right"],tl:["-top-left"],lt:["-top-left"],tr:["-top-right"],rt:["-top-right"],bl:["-bottom-left"],lb:["-bottom-left"],br:["-bottom-right"],rb:["-bottom-right"],"":[""],bs:["-start-start","-start-end"],be:["-end-start","-end-end"],s:["-end-start","-start-start"],is:["-end-start","-start-start"],e:["-start-end","-end-end"],ie:["-start-end","-end-end"],ss:["-start-start"],"bs-is":["-start-start"],"is-bs":["-start-start"],se:["-start-end"],"bs-ie":["-start-end"],"ie-bs":["-start-end"],es:["-end-start"],"be-is":["-end-start"],"is-be":["-end-start"],ee:["-end-end"],"be-ie":["-end-end"],"ie-be":["-end-end"]},tc={x:["-x"],y:["-y"],z:["-z"],"":["-x","-y"]},rc=["x","y","z"],Za=["top","top center","top left","top right","bottom","bottom center","bottom left","bottom right","left","left center","left top","left bottom","right","right center","right top","right bottom","center","center top","center bottom","center left","center right","center center"],G=Object.assign({},...Za.map(e=>({[e.replace(/ /,"-")]:e})),...Za.map(e=>({[e.replace(/\b(\w)\w+/g,"$1").replace(/ /,"")]:e}))),P=["inherit","initial","revert","revert-layer","unset"],pt=/^(calc|clamp|min|max)\s*\((.+)\)(.*)/,$r=/^(var)\s*\((.+)\)(.*)/,vr=/^(-?\d*(?:\.\d+)?)(px|pt|pc|%|r?(?:em|ex|lh|cap|ch|ic)|(?:[sld]?v|cq)(?:[whib]|min|max)|in|cm|mm|rpx)?$/i,nc=/^(-?\d*(?:\.\d+)?)$/,oc=/^(px|[sld]?v[wh])$/i,ic={px:1,vw:100,vh:100,svw:100,svh:100,dvw:100,dvh:100,lvh:100,lvw:100},sc=/^\[(color|image|length|size|position|quoted|string):/i,zp=/,(?![^()]*\))/g,Ap=["color","border-color","background-color","outline-color","text-decoration-color","flex-grow","flex","flex-shrink","caret-color","font","gap","opacity","visibility","z-index","font-weight","zoom","text-shadow","transform","box-shadow","border","background-position","left","right","top","bottom","object-position","max-height","min-height","max-width","min-width","height","width","border-width","margin","padding","outline-width","outline-offset","font-size","line-height","text-indent","vertical-align","border-spacing","letter-spacing","word-spacing","stroke","filter","backdrop-filter","fill","mask","mask-size","mask-border","clip-path","clip","border-radius"];function ae(e){return+e.toFixed(10)}function Op(e){let t=e.match(vr);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(n&&!Number.isNaN(o))return`${ae(o)}${n}`}function Pp(e){if(e==="auto"||e==="a")return"auto"}function Vp(e){if(!e)return;if(oc.test(e))return`${ic[e]}${e}`;let t=e.match(vr);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0?"0":n?`${ae(o)}${n}`:`${ae(o/4)}rem`}function Mp(e){if(oc.test(e))return`${ic[e]}${e}`;let t=e.match(vr);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return n?`${ae(o)}${n}`:`${ae(o)}px`}function Fp(e){if(!nc.test(e))return;let t=Number.parseFloat(e);if(!Number.isNaN(t))return ae(t)}function _p(e){if(e.endsWith("%")&&(e=e.slice(0,-1)),!nc.test(e))return;let t=Number.parseFloat(e);if(!Number.isNaN(t))return`${ae(t/100)}`}function Lp(e){if(!e)return;if(e==="full")return"100%";let[t,r]=e.split("/"),n=Number.parseFloat(t)/Number.parseFloat(r);if(!Number.isNaN(n))return n===0?"0":`${ae(n*100)}%`}function wr(e,t){if(e&&e.startsWith("[")&&e.endsWith("]")){let r,n,o=e.match(sc);if(o?(t||(n=o[1]),r=e.slice(o[0].length,-1)):r=e.slice(1,-1),!r||r==='=""')return;r.startsWith("--")&&(r=`var(${r})`);let i=0;for(let s of r)if(s==="[")i+=1;else if(s==="]"&&(i-=1,i<0))return;if(i)return;switch(n){case"string":return r.replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_");case"quoted":return r.replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_").replace(/(["\\])/g,"\\$1").replace(/^(.+)$/,'"$1"')}return r.replace(/(url\(.*?\))/g,s=>s.replace(/_/g,"\\_")).replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_").replace(/(?:calc|clamp|max|min)\((.*)/g,s=>{let a=[];return s.replace(/var\((--.+?)[,)]/g,(c,l)=>(a.push(l),c.replace(l,"--un-calc"))).replace(/(-?\d*\.?\d(?!-\d.+[,)](?![^+\-/*])\D)(?:%|[a-z]+)?|\))([+\-/*])/g,"$1 $2 ").replace(/--un-calc/g,()=>a.shift())})}}function Wp(e){return wr(e)}function Up(e){return wr(e,"color")}function Bp(e){return wr(e,"length")}function Dp(e){return wr(e,"position")}function Ip(e){if(/^\$[^\s'"`;{}]/.test(e)){let[t,r]=e.slice(1).split(",");return`var(--${Q(t)}${r?`, ${r}`:""})`}}function Np(e){let t=e.match(/^(-?[0-9.]+)(s|ms)?$/i);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0&&!n?"0s":n?`${ae(o)}${n}`:`${ae(o)}ms`}function Kp(e){let t=e.match(/^(-?[0-9.]+)(deg|rad|grad|turn)?$/i);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0?"0":n?`${ae(o)}${n}`:`${ae(o)}deg`}function Gp(e){if(P.includes(e))return e}function Hp(e){if(e.split(",").every(t=>Ap.includes(t)))return e}function qp(e){if(["top","left","right","bottom","center"].includes(e))return e}var ac={__proto__:null,auto:Pp,bracket:Wp,bracketOfColor:Up,bracketOfLength:Bp,bracketOfPosition:Dp,cssvar:Ip,degree:Kp,fraction:Lp,global:Gp,number:Fp,numberWithUnit:Op,percent:_p,position:qp,properties:Hp,px:Mp,rem:Vp,time:Np},cc=Jt(ac),u=cc,ro="$$mini-no-negative";function L(e){return([t,r,n],{theme:o})=>{let i=o.spacing?.[n||"DEFAULT"]??u.bracket.cssvar.global.auto.fraction.rem(n);if(i!=null)return Y[r].map(s=>[`${e}${s}`,i]);if(n?.startsWith("-")){let s=o.spacing?.[n.slice(1)];if(s!=null)return Y[r].map(a=>[`${e}${a}`,`calc(${s} * -1)`])}}}function Ja(e,t,r="colors"){let n=e[r],o=-1;for(let i of t){if(o+=1,n&&typeof n!="string"){let s=t.slice(o).join("-").replace(/(-[a-z])/g,a=>a.slice(1).toUpperCase());if(n[s])return n[s];if(n[i]){n=n[i];continue}}return}return n}function Qa(e,t,r){return Ja(e,t,r)||Ja(e,t,"colors")}function kr(e,t){let[r,n]=ue(e,"[","]",["/",":"])??[];if(r!=null){let o=(r.match(sc)??[])[1];if(o==null||o===t)return[r,n]}}function Pe(e,t,r){let n=kr(e,"color");if(!n)return;let[o,i]=n,s=o.replace(/([a-z])(\d)/g,"$1-$2").split(/-/g),[a]=s;if(!a)return;let c,l=u.bracketOfColor(o),p=l||o;if(u.numberWithUnit(p))return;if(/^#[\da-f]+$/i.test(p)?c=p:/^hex-[\da-fA-F]+$/.test(p)?c=`#${p.slice(4)}`:o.startsWith("$")&&(c=u.cssvar(o)),c=c||l,!c){let h=Qa(t,[o],r);typeof h=="string"&&(c=h)}let d="DEFAULT";if(!c){let h=s,m,[g]=s.slice(-1);/^\d+$/.test(g)&&(d=m=g,h=s.slice(0,-1));let b=Qa(t,h,r);typeof b=="object"?c=b[m??d]:typeof b=="string"&&!m&&(c=b)}return{opacity:i,name:a,no:d,color:c,cssColor:q(c),alpha:u.bracket.cssvar.percent(i??"")}}function B(e,t,r,n){return([,o],{theme:i,generator:s})=>{let a=Pe(o,i,r);if(!a)return;let{alpha:c,color:l,cssColor:p}=a,h=s.config.envMode==="dev"&&l?` /* ${l} */`:"",m={};if(p)if(c!=null)m[e]=A(p,c)+h;else{let g=`--un-${t}-opacity`,b=A(p,`var(${g})`);b.includes(g)&&(m[g]=oe(p)),m[e]=b+h}else if(l)if(c!=null)m[e]=A(l,c)+h;else{let g=`--un-${t}-opacity`,b=A(l,`var(${g})`);b.includes(g)&&(m[g]=1),m[e]=b+h}if(n?.(m)!==!1)return m}}function Ve(e,t){let r=[];e=C(e);for(let n=0;n<e.length;n++){let o=Re(e[n]," ",6);if(!o||o.length<3)return e;let i=!1,s=o.indexOf("inset");s!==-1&&(o.splice(s,1),i=!0);let a="",c=o.at(-1);if(q(o.at(0))){let l=q(o.shift());l&&(a=`, ${A(l)}`)}else if(q(c)){let l=q(o.pop());l&&(a=`, ${A(l)}`)}else c&&$r.test(c)&&(a=`, ${o.pop()}`);r.push(`${i?"inset ":""}${o.join(" ")} var(${t}${a})`)}return r}function Me(e,t,r){return e!=null&&!!Pe(e,t,r)?.color}var ec=/[a-z]+/gi,to=new WeakMap;function Ee({theme:e,generator:t},r="breakpoints"){let n=t?.userConfig?.theme?.[r]||e[r];if(!n)return;if(to.has(e))return to.get(e);let o=Object.entries(n).sort((i,s)=>Number.parseInt(i[1].replace(ec,""))-Number.parseInt(s[1].replace(ec,""))).map(([i,s])=>({point:i,size:s}));return to.set(e,o),o}function w(e,t){return P.map(r=>[`${e}-${r}`,{[t??e]:r}])}function ve(e){return e!=null&&pt.test(e)}function no(e){return e[0]==="["&&e.slice(-1)==="]"&&(e=e.slice(1,-1)),pt.test(e)||vr.test(e)}function dt(e,t,r){let n=t.split(zp);return e||!e&&n.length===1?tc[e].map(o=>[`--un-${r}${o}`,t]):n.map((o,i)=>[`--un-${r}-${rc[i]}`,o])}var lc=[[/^(?:animate-)?keyframes-(.+)$/,([,e],{theme:t})=>{let r=t.animation?.keyframes?.[e];if(r)return[`@keyframes ${e}${r}`,{animation:e}]},{autocomplete:["animate-keyframes-$animation.keyframes","keyframes-$animation.keyframes"]}],[/^animate-(.+)$/,([,e],{theme:t})=>{let r=t.animation?.keyframes?.[e];if(r){let n=t.animation?.durations?.[e]??"1s",o=t.animation?.timingFns?.[e]??"linear",i=t.animation?.counts?.[e]??1,s=t.animation?.properties?.[e];return[`@keyframes ${e}${r}`,{animation:`${e} ${n} ${o} ${i}`,...s}]}return{animation:u.bracket.cssvar(e)}},{autocomplete:"animate-$animation.keyframes"}],[/^animate-name-(.+)/,([,e])=>({"animation-name":u.bracket.cssvar(e)??e})],[/^animate-duration-(.+)$/,([,e],{theme:t})=>({"animation-duration":t.duration?.[e||"DEFAULT"]??u.bracket.cssvar.time(e)}),{autocomplete:["animate-duration","animate-duration-$duration"]}],[/^animate-delay-(.+)$/,([,e],{theme:t})=>({"animation-delay":t.duration?.[e||"DEFAULT"]??u.bracket.cssvar.time(e)}),{autocomplete:["animate-delay","animate-delay-$duration"]}],[/^animate-ease(?:-(.+))?$/,([,e],{theme:t})=>({"animation-timing-function":t.easing?.[e||"DEFAULT"]??u.bracket.cssvar(e)}),{autocomplete:["animate-ease","animate-ease-$easing"]}],[/^animate-(fill-mode-|fill-|mode-)?(.+)$/,([,e,t])=>["none","forwards","backwards","both",e?P:[]].includes(t)?{"animation-fill-mode":t}:void 0,{autocomplete:["animate-(fill|mode|fill-mode)","animate-(fill|mode|fill-mode)-(none|forwards|backwards|both|inherit|initial|revert|revert-layer|unset)","animate-(none|forwards|backwards|both|inherit|initial|revert|revert-layer|unset)"]}],[/^animate-(direction-)?(.+)$/,([,e,t])=>["normal","reverse","alternate","alternate-reverse",e?P:[]].includes(t)?{"animation-direction":t}:void 0,{autocomplete:["animate-direction","animate-direction-(normal|reverse|alternate|alternate-reverse|inherit|initial|revert|revert-layer|unset)","animate-(normal|reverse|alternate|alternate-reverse|inherit|initial|revert|revert-layer|unset)"]}],[/^animate-(?:iteration-count-|iteration-|count-)(.+)$/,([,e])=>({"animation-iteration-count":u.bracket.cssvar(e)??e.replace(/-/g,",")}),{autocomplete:["animate-(iteration|count|iteration-count)","animate-(iteration|count|iteration-count)-<num>"]}],[/^animate-(play-state-|play-|state-)?(.+)$/,([,e,t])=>["paused","running",e?P:[]].includes(t)?{"animation-play-state":t}:void 0,{autocomplete:["animate-(play|state|play-state)","animate-(play|state|play-state)-(paused|running|inherit|initial|revert|revert-layer|unset)","animate-(paused|running|inherit|initial|revert|revert-layer|unset)"]}],["animate-none",{animation:"none"}],...w("animate","animation")];function uc(e){return e?A(e,0):"rgb(255 255 255 / 0)"}function Yp(e,t,r,n){return t?n!=null?A(t,n):A(t,`var(--un-${e}-opacity, ${oe(t)})`):A(r,n)}function oo(){return([,e,t],{theme:r})=>{let n=Pe(t,r,"backgroundColor");if(!n)return;let{alpha:o,color:i,cssColor:s}=n;if(!i)return;let a=Yp(e,s,i,o);switch(e){case"from":return{"--un-gradient-from-position":"0%","--un-gradient-from":`${a} var(--un-gradient-from-position)`,"--un-gradient-to-position":"100%","--un-gradient-to":`${uc(s)} var(--un-gradient-to-position)`,"--un-gradient-stops":"var(--un-gradient-from), var(--un-gradient-to)"};case"via":return{"--un-gradient-via-position":"50%","--un-gradient-to":uc(s),"--un-gradient-stops":`var(--un-gradient-from), ${a} var(--un-gradient-via-position), var(--un-gradient-to)`};case"to":return{"--un-gradient-to-position":"100%","--un-gradient-to":`${a} var(--un-gradient-to-position)`}}}}function Xp(){return([,e,t])=>({[`--un-gradient-${e}-position`]:`${Number(u.bracket.cssvar.percent(t))*100}%`})}var fc=[[/^bg-gradient-(.+)$/,([,e])=>({"--un-gradient":u.bracket(e)}),{autocomplete:["bg-gradient","bg-gradient-(from|to|via)","bg-gradient-(from|to|via)-$colors","bg-gradient-(from|to|via)-(op|opacity)","bg-gradient-(from|to|via)-(op|opacity)-<percent>"]}],[/^(?:bg-gradient-)?stops-(\[.+\])$/,([,e])=>({"--un-gradient-stops":u.bracket(e)})],[/^(?:bg-gradient-)?(from)-(.+)$/,oo()],[/^(?:bg-gradient-)?(via)-(.+)$/,oo()],[/^(?:bg-gradient-)?(to)-(.+)$/,oo()],[/^(?:bg-gradient-)?(from|via|to)-op(?:acity)?-?(.+)$/,([,e,t])=>({[`--un-${e}-opacity`]:u.bracket.percent(t)})],[/^(from|via|to)-([\d.]+)%$/,Xp()],[/^bg-gradient-((?:repeating-)?(?:linear|radial|conic))$/,([,e])=>({"background-image":`${e}-gradient(var(--un-gradient, var(--un-gradient-stops, rgb(255 255 255 / 0))))`}),{autocomplete:["bg-gradient-repeating","bg-gradient-(linear|radial|conic)","bg-gradient-repeating-(linear|radial|conic)"]}],[/^bg-gradient-to-([rltb]{1,2})$/,([,e])=>{if(e in G)return{"--un-gradient-shape":`to ${G[e]} in oklch`,"--un-gradient":"var(--un-gradient-shape), var(--un-gradient-stops)","background-image":"linear-gradient(var(--un-gradient))"}},{autocomplete:`bg-gradient-to-(${Object.keys(G).filter(e=>e.length<=2&&Array.from(e).every(t=>"rltb".includes(t))).join("|")})`}],[/^(?:bg-gradient-)?shape-(.+)$/,([,e])=>{let t=e in G?`to ${G[e]}`:u.bracket(e);if(t!=null)return{"--un-gradient-shape":`${t} in oklch`,"--un-gradient":"var(--un-gradient-shape), var(--un-gradient-stops)"}},{autocomplete:["bg-gradient-shape",`bg-gradient-shape-(${Object.keys(G).join("|")})`,`shape-(${Object.keys(G).join("|")})`]}],["bg-none",{"background-image":"none"}],["box-decoration-slice",{"box-decoration-break":"slice"}],["box-decoration-clone",{"box-decoration-break":"clone"}],...w("box-decoration","box-decoration-break"),["bg-auto",{"background-size":"auto"}],["bg-cover",{"background-size":"cover"}],["bg-contain",{"background-size":"contain"}],["bg-fixed",{"background-attachment":"fixed"}],["bg-local",{"background-attachment":"local"}],["bg-scroll",{"background-attachment":"scroll"}],["bg-clip-border",{"-webkit-background-clip":"border-box","background-clip":"border-box"}],["bg-clip-content",{"-webkit-background-clip":"content-box","background-clip":"content-box"}],["bg-clip-padding",{"-webkit-background-clip":"padding-box","background-clip":"padding-box"}],["bg-clip-text",{"-webkit-background-clip":"text","background-clip":"text"}],...P.map(e=>[`bg-clip-${e}`,{"-webkit-background-clip":e,"background-clip":e}]),[/^bg-([-\w]{3,})$/,([,e])=>({"background-position":G[e]})],["bg-repeat",{"background-repeat":"repeat"}],["bg-no-repeat",{"background-repeat":"no-repeat"}],["bg-repeat-x",{"background-repeat":"repeat-x"}],["bg-repeat-y",{"background-repeat":"repeat-y"}],["bg-repeat-round",{"background-repeat":"round"}],["bg-repeat-space",{"background-repeat":"space"}],...w("bg-repeat","background-repeat"),["bg-origin-border",{"background-origin":"border-box"}],["bg-origin-padding",{"background-origin":"padding-box"}],["bg-origin-content",{"background-origin":"content-box"}],...w("bg-origin","background-origin")];var io={disc:"disc",circle:"circle",square:"square",decimal:"decimal","zero-decimal":"decimal-leading-zero",greek:"lower-greek",roman:"lower-roman","upper-roman":"upper-roman",alpha:"lower-alpha","upper-alpha":"upper-alpha",latin:"lower-latin","upper-latin":"upper-latin"},pc=[[/^list-(.+?)(?:-(outside|inside))?$/,([,e,t])=>{let r=io[e];if(r)return t?{"list-style-position":t,"list-style-type":r}:{"list-style-type":r}},{autocomplete:[`list-(${Object.keys(io).join("|")})`,`list-(${Object.keys(io).join("|")})-(outside|inside)`]}],["list-outside",{"list-style-position":"outside"}],["list-inside",{"list-style-position":"inside"}],["list-none",{"list-style-type":"none"}],[/^list-image-(.+)$/,([,e])=>{if(/^\[url\(.+\)\]$/.test(e))return{"list-style-image":u.bracket(e)}}],["list-image-none",{"list-style-image":"none"}],...w("list","list-style-type")],dc=[[/^accent-(.+)$/,B("accent-color","accent","accentColor"),{autocomplete:"accent-$colors"}],[/^accent-op(?:acity)?-?(.+)$/,([,e])=>({"--un-accent-opacity":u.bracket.percent(e)}),{autocomplete:["accent-(op|opacity)","accent-(op|opacity)-<percent>"]}]],mc=[[/^caret-(.+)$/,B("caret-color","caret","textColor"),{autocomplete:"caret-$colors"}],[/^caret-op(?:acity)?-?(.+)$/,([,e])=>({"--un-caret-opacity":u.bracket.percent(e)}),{autocomplete:["caret-(op|opacity)","caret-(op|opacity)-<percent>"]}]],hc=[["image-render-auto",{"image-rendering":"auto"}],["image-render-edge",{"image-rendering":"crisp-edges"}],["image-render-pixel",[["-ms-interpolation-mode","nearest-neighbor"],["image-rendering","-webkit-optimize-contrast"],["image-rendering","-moz-crisp-edges"],["image-rendering","-o-pixelated"],["image-rendering","pixelated"]]]],gc=[["overscroll-auto",{"overscroll-behavior":"auto"}],["overscroll-contain",{"overscroll-behavior":"contain"}],["overscroll-none",{"overscroll-behavior":"none"}],...w("overscroll","overscroll-behavior"),["overscroll-x-auto",{"overscroll-behavior-x":"auto"}],["overscroll-x-contain",{"overscroll-behavior-x":"contain"}],["overscroll-x-none",{"overscroll-behavior-x":"none"}],...w("overscroll-x","overscroll-behavior-x"),["overscroll-y-auto",{"overscroll-behavior-y":"auto"}],["overscroll-y-contain",{"overscroll-behavior-y":"contain"}],["overscroll-y-none",{"overscroll-behavior-y":"none"}],...w("overscroll-y","overscroll-behavior-y")],bc=[["scroll-auto",{"scroll-behavior":"auto"}],["scroll-smooth",{"scroll-behavior":"smooth"}],...w("scroll","scroll-behavior")];var xc=[[/^columns-(.+)$/,([,e])=>({columns:u.bracket.global.number.auto.numberWithUnit(e)}),{autocomplete:"columns-<num>"}],["break-before-auto",{"break-before":"auto"}],["break-before-avoid",{"break-before":"avoid"}],["break-before-all",{"break-before":"all"}],["break-before-avoid-page",{"break-before":"avoid-page"}],["break-before-page",{"break-before":"page"}],["break-before-left",{"break-before":"left"}],["break-before-right",{"break-before":"right"}],["break-before-column",{"break-before":"column"}],...w("break-before"),["break-inside-auto",{"break-inside":"auto"}],["break-inside-avoid",{"break-inside":"avoid"}],["break-inside-avoid-page",{"break-inside":"avoid-page"}],["break-inside-avoid-column",{"break-inside":"avoid-column"}],...w("break-inside"),["break-after-auto",{"break-after":"auto"}],["break-after-avoid",{"break-after":"avoid"}],["break-after-all",{"break-after":"all"}],["break-after-avoid-page",{"break-after":"avoid-page"}],["break-after-page",{"break-after":"page"}],["break-after-left",{"break-after":"left"}],["break-after-right",{"break-after":"right"}],["break-after-column",{"break-after":"column"}],...w("break-after")];var Zp=/@media \(min-width: (.+)\)/,yc=[[/^__container$/,(e,t)=>{let{theme:r,variantHandlers:n}=t,o=r.container?.padding,i;M(o)?i=o:i=o?.DEFAULT;let s=r.container?.maxWidth,a;for(let l of n){let p=l.handle?.({},d=>d)?.parent;if(M(p)){let d=p.match(Zp)?.[1];if(d){let m=(Ee(t)??[]).find(g=>g.size===d)?.point;s?m&&(a=s?.[m]):a=d,m&&!M(o)&&(i=o?.[m]??i)}}}let c={"max-width":a};return n.length||(c.width="100%"),r.container?.center&&(c["margin-left"]="auto",c["margin-right"]="auto"),o&&(c["padding-left"]=i,c["padding-right"]=i),c},{internal:!0}]],$c=[[/^(?:(\w+)[:-])?container$/,([,e],t)=>{let r=(Ee(t)??[]).map(o=>o.point);if(e){if(!r.includes(e))return;r=r.slice(r.indexOf(e))}let n=r.map(o=>`${o}:__container`);return e||n.unshift("__container"),n}]];var Jp=["auto","default","none","context-menu","help","pointer","progress","wait","cell","crosshair","text","vertical-text","alias","copy","move","no-drop","not-allowed","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"],Qp=["none","strict","content","size","inline-size","layout","style","paint"],j=" ",mt=[["inline",{display:"inline"}],["block",{display:"block"}],["inline-block",{display:"inline-block"}],["contents",{display:"contents"}],["flow-root",{display:"flow-root"}],["list-item",{display:"list-item"}],["hidden",{display:"none"}],[/^display-(.+)$/,([,e])=>({display:u.bracket.cssvar.global(e)})]],ht=[["visible",{visibility:"visible"}],["invisible",{visibility:"hidden"}],["backface-visible",{"backface-visibility":"visible"}],["backface-hidden",{"backface-visibility":"hidden"}],...w("backface","backface-visibility")],gt=[[/^cursor-(.+)$/,([,e])=>({cursor:u.bracket.cssvar.global(e)})],...Jp.map(e=>[`cursor-${e}`,{cursor:e}])],bt=[[/^contain-(.*)$/,([,e])=>u.bracket(e)!=null?{contain:u.bracket(e).split(" ").map(t=>u.cssvar.fraction(t)??t).join(" ")}:Qp.includes(e)?{contain:e}:void 0]],xt=[["pointer-events-auto",{"pointer-events":"auto"}],["pointer-events-none",{"pointer-events":"none"}],...w("pointer-events")],yt=[["resize-x",{resize:"horizontal"}],["resize-y",{resize:"vertical"}],["resize",{resize:"both"}],["resize-none",{resize:"none"}],...w("resize")],$t=[["select-auto",{"-webkit-user-select":"auto","user-select":"auto"}],["select-all",{"-webkit-user-select":"all","user-select":"all"}],["select-text",{"-webkit-user-select":"text","user-select":"text"}],["select-none",{"-webkit-user-select":"none","user-select":"none"}],...w("select","user-select")],vt=[[/^(?:whitespace-|ws-)([-\w]+)$/,([,e])=>["normal","nowrap","pre","pre-line","pre-wrap","break-spaces",...P].includes(e)?{"white-space":e}:void 0,{autocomplete:"(whitespace|ws)-(normal|nowrap|pre|pre-line|pre-wrap|break-spaces)"}]],wt=[[/^intrinsic-size-(.+)$/,([,e])=>({"contain-intrinsic-size":u.bracket.cssvar.global.fraction.rem(e)}),{autocomplete:"intrinsic-size-<num>"}],["content-visibility-visible",{"content-visibility":"visible"}],["content-visibility-hidden",{"content-visibility":"hidden"}],["content-visibility-auto",{"content-visibility":"auto"}],...w("content-visibility")],kt=[[/^content-(.+)$/,([,e])=>({content:u.bracket.cssvar(e)})],["content-empty",{content:'""'}],["content-none",{content:"none"}]],St=[["break-normal",{"overflow-wrap":"normal","word-break":"normal"}],["break-words",{"overflow-wrap":"break-word"}],["break-all",{"word-break":"break-all"}],["break-keep",{"word-break":"keep-all"}],["break-anywhere",{"overflow-wrap":"anywhere"}]],Ct=[["text-wrap",{"text-wrap":"wrap"}],["text-nowrap",{"text-wrap":"nowrap"}],["text-balance",{"text-wrap":"balance"}],["text-pretty",{"text-wrap":"pretty"}]],Rt=[["truncate",{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}],["text-truncate",{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}],["text-ellipsis",{"text-overflow":"ellipsis"}],["text-clip",{"text-overflow":"clip"}]],Et=[["case-upper",{"text-transform":"uppercase"}],["case-lower",{"text-transform":"lowercase"}],["case-capital",{"text-transform":"capitalize"}],["case-normal",{"text-transform":"none"}],...w("case","text-transform")],Tt=[["italic",{"font-style":"italic"}],["not-italic",{"font-style":"normal"}],["font-italic",{"font-style":"italic"}],["font-not-italic",{"font-style":"normal"}],["oblique",{"font-style":"oblique"}],["not-oblique",{"font-style":"normal"}],["font-oblique",{"font-style":"oblique"}],["font-not-oblique",{"font-style":"normal"}]],jt=[["antialiased",{"-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale"}],["subpixel-antialiased",{"-webkit-font-smoothing":"auto","-moz-osx-font-smoothing":"auto"}]],Ke={"--un-ring-inset":j,"--un-ring-offset-width":"0px","--un-ring-offset-color":"#fff","--un-ring-width":"0px","--un-ring-color":"rgb(147 197 253 / 0.5)","--un-shadow":"0 0 rgb(0 0 0 / 0)"},ed=Object.keys(Ke),zt=[[/^ring(?:-(.+))?$/,([,e],{theme:t})=>{let r=t.ringWidth?.[e||"DEFAULT"]??u.px(e||"1");if(r)return{"--un-ring-width":r,"--un-ring-offset-shadow":"var(--un-ring-inset) 0 0 0 var(--un-ring-offset-width) var(--un-ring-offset-color)","--un-ring-shadow":"var(--un-ring-inset) 0 0 0 calc(var(--un-ring-width) + var(--un-ring-offset-width)) var(--un-ring-color)","box-shadow":"var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow)"}},{custom:{preflightKeys:ed},autocomplete:"ring-$ringWidth"}],[/^ring-(?:width-|size-)(.+)$/,Cc,{autocomplete:"ring-(width|size)-$lineWidth"}],["ring-offset",{"--un-ring-offset-width":"1px"}],[/^ring-offset-(?:width-|size-)?(.+)$/,([,e],{theme:t})=>({"--un-ring-offset-width":t.lineWidth?.[e]??u.bracket.cssvar.px(e)}),{autocomplete:"ring-offset-(width|size)-$lineWidth"}],[/^ring-(.+)$/,td,{autocomplete:"ring-$colors"}],[/^ring-op(?:acity)?-?(.+)$/,([,e])=>({"--un-ring-opacity":u.bracket.percent.cssvar(e)}),{autocomplete:"ring-(op|opacity)-<percent>"}],[/^ring-offset-(.+)$/,B("--un-ring-offset-color","ring-offset","borderColor"),{autocomplete:"ring-offset-$colors"}],[/^ring-offset-op(?:acity)?-?(.+)$/,([,e])=>({"--un-ring-offset-opacity":u.bracket.percent.cssvar(e)}),{autocomplete:"ring-offset-(op|opacity)-<percent>"}],["ring-inset",{"--un-ring-inset":"inset"}]];function Cc([,e],{theme:t}){return{"--un-ring-width":t.ringWidth?.[e]??u.bracket.cssvar.px(e)}}function td(e,t){return ve(u.bracket(e[1]))?Cc(e,t):B("--un-ring-color","ring","borderColor")(e,t)}var Ge={"--un-ring-offset-shadow":"0 0 rgb(0 0 0 / 0)","--un-ring-shadow":"0 0 rgb(0 0 0 / 0)","--un-shadow-inset":j,"--un-shadow":"0 0 rgb(0 0 0 / 0)"},rd=Object.keys(Ge),At=[[/^shadow(?:-(.+))?$/,(e,t)=>{let[,r]=e,{theme:n}=t,o=n.boxShadow?.[r||"DEFAULT"],i=r?u.bracket.cssvar(r):void 0;return(o!=null||i!=null)&&!Me(i,n,"shadowColor")?{"--un-shadow":Ve(o||i,"--un-shadow-color").join(","),"box-shadow":"var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow)"}:B("--un-shadow-color","shadow","shadowColor")(e,t)},{custom:{preflightKeys:rd},autocomplete:["shadow-$colors","shadow-$boxShadow"]}],[/^shadow-op(?:acity)?-?(.+)$/,([,e])=>({"--un-shadow-opacity":u.bracket.percent.cssvar(e)}),{autocomplete:"shadow-(op|opacity)-<percent>"}],["shadow-inset",{"--un-shadow-inset":"inset"}]],Sr=["translate","rotate","scale"],nd=["translateX(var(--un-translate-x))","translateY(var(--un-translate-y))","rotate(var(--un-rotate))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))"].join(" "),Ne=["translateX(var(--un-translate-x))","translateY(var(--un-translate-y))","translateZ(var(--un-translate-z))","rotate(var(--un-rotate))","rotateX(var(--un-rotate-x))","rotateY(var(--un-rotate-y))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))","scaleZ(var(--un-scale-z))"].join(" "),od=["translate3d(var(--un-translate-x), var(--un-translate-y), var(--un-translate-z))","rotate(var(--un-rotate))","rotateX(var(--un-rotate-x))","rotateY(var(--un-rotate-y))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))","scaleZ(var(--un-scale-z))"].join(" "),He={"--un-rotate":0,"--un-rotate-x":0,"--un-rotate-y":0,"--un-rotate-z":0,"--un-scale-x":1,"--un-scale-y":1,"--un-scale-z":1,"--un-skew-x":0,"--un-skew-y":0,"--un-translate-x":0,"--un-translate-y":0,"--un-translate-z":0},ge=Object.keys(He),Ot=[[/^(?:transform-)?origin-(.+)$/,([,e])=>({"transform-origin":G[e]??u.bracket.cssvar(e)}),{autocomplete:[`transform-origin-(${Object.keys(G).join("|")})`,`origin-(${Object.keys(G).join("|")})`]}],[/^(?:transform-)?perspect(?:ive)?-(.+)$/,([,e])=>{let t=u.bracket.cssvar.px.numberWithUnit(e);if(t!=null)return{"-webkit-perspective":t,perspective:t}}],[/^(?:transform-)?perspect(?:ive)?-origin-(.+)$/,([,e])=>{let t=u.bracket.cssvar(e)??(e.length>=3?G[e]:void 0);if(t!=null)return{"-webkit-perspective-origin":t,"perspective-origin":t}}],[/^(?:transform-)?translate-()(.+)$/,vc,{custom:{preflightKeys:ge}}],[/^(?:transform-)?translate-([xyz])-(.+)$/,vc,{custom:{preflightKeys:ge}}],[/^(?:transform-)?rotate-()(.+)$/,kc,{custom:{preflightKeys:ge}}],[/^(?:transform-)?rotate-([xyz])-(.+)$/,kc,{custom:{preflightKeys:ge}}],[/^(?:transform-)?skew-()(.+)$/,Sc,{custom:{preflightKeys:ge}}],[/^(?:transform-)?skew-([xy])-(.+)$/,Sc,{custom:{preflightKeys:ge},autocomplete:["transform-skew-(x|y)-<percent>","skew-(x|y)-<percent>"]}],[/^(?:transform-)?scale-()(.+)$/,wc,{custom:{preflightKeys:ge}}],[/^(?:transform-)?scale-([xyz])-(.+)$/,wc,{custom:{preflightKeys:ge},autocomplete:[`transform-(${Sr.join("|")})-<percent>`,`transform-(${Sr.join("|")})-(x|y|z)-<percent>`,`(${Sr.join("|")})-<percent>`,`(${Sr.join("|")})-(x|y|z)-<percent>`]}],[/^(?:transform-)?preserve-3d$/,()=>({"transform-style":"preserve-3d"})],[/^(?:transform-)?preserve-flat$/,()=>({"transform-style":"flat"})],["transform",{transform:Ne},{custom:{preflightKeys:ge}}],["transform-cpu",{transform:nd},{custom:{preflightKeys:["--un-translate-x","--un-translate-y","--un-rotate","--un-rotate-z","--un-skew-x","--un-skew-y","--un-scale-x","--un-scale-y"]}}],["transform-gpu",{transform:od},{custom:{preflightKeys:ge}}],["transform-none",{transform:"none"}],...w("transform")];function vc([,e,t],{theme:r}){let n=r.spacing?.[t]??u.bracket.cssvar.fraction.rem(t);if(n!=null)return[...dt(e,n,"translate"),["transform",Ne]]}function wc([,e,t]){let r=u.bracket.cssvar.fraction.percent(t);if(r!=null)return[...dt(e,r,"scale"),["transform",Ne]]}function kc([,e="",t]){let r=u.bracket.cssvar.degree(t);if(r!=null)return e?{"--un-rotate":0,[`--un-rotate-${e}`]:r,transform:Ne}:{"--un-rotate-x":0,"--un-rotate-y":0,"--un-rotate-z":0,"--un-rotate":r,transform:Ne}}function Sc([,e,t]){let r=u.bracket.cssvar.degree(t);if(r!=null)return[...dt(e,r,"skew"),["transform",Ne]]}var Rc={mid:"middle",base:"baseline",btm:"bottom",baseline:"baseline",top:"top",start:"top",middle:"middle",bottom:"bottom",end:"bottom","text-top":"text-top","text-bottom":"text-bottom",sub:"sub",super:"super",...Object.fromEntries(P.map(e=>[e,e]))},Er=[[/^(?:vertical|align|v)-([-\w]+%?)$/,([,e])=>({"vertical-align":Rc[e]??u.numberWithUnit(e)}),{autocomplete:[`(vertical|align|v)-(${Object.keys(Rc).join("|")})`,"(vertical|align|v)-<percentage>"]}]],Ec=["center","left","right","justify","start","end"],Tr=[...Ec.map(e=>[`text-${e}`,{"text-align":e}]),...[...P,...Ec].map(e=>[`text-align-${e}`,{"text-align":e}])],jr=[[/^outline-(?:width-|size-)?(.+)$/,Oc,{autocomplete:"outline-(width|size)-<num>"}],[/^outline-(?:color-)?(.+)$/,id,{autocomplete:"outline-$colors"}],[/^outline-offset-(.+)$/,([,e],{theme:t})=>({"outline-offset":t.lineWidth?.[e]??u.bracket.cssvar.global.px(e)}),{autocomplete:"outline-(offset)-<num>"}],["outline",{"outline-style":"solid"}],...["auto","dashed","dotted","double","hidden","solid","groove","ridge","inset","outset",...P].map(e=>[`outline-${e}`,{"outline-style":e}]),["outline-none",{outline:"2px solid transparent","outline-offset":"2px"}]];function Oc([,e],{theme:t}){return{"outline-width":t.lineWidth?.[e]??u.bracket.cssvar.global.px(e)}}function id(e,t){return ve(u.bracket(e[1]))?Oc(e,t):B("outline-color","outline-color","borderColor")(e,t)}var zr=[["appearance-auto",{"-webkit-appearance":"auto",appearance:"auto"}],["appearance-none",{"-webkit-appearance":"none",appearance:"none"}]];function sd(e){return u.properties.auto.global(e)??{contents:"contents",scroll:"scroll-position"}[e]}var Ar=[[/^will-change-(.+)/,([,e])=>({"will-change":sd(e)})]],Te=["solid","dashed","dotted","double","hidden","none","groove","ridge","inset","outset",...P],Or=[[/^(?:border|b)()(?:-(.+))?$/,pe,{autocomplete:"(border|b)-<directions>"}],[/^(?:border|b)-([xy])(?:-(.+))?$/,pe],[/^(?:border|b)-([rltbse])(?:-(.+))?$/,pe],[/^(?:border|b)-(block|inline)(?:-(.+))?$/,pe],[/^(?:border|b)-([bi][se])(?:-(.+))?$/,pe],[/^(?:border|b)-()(?:width|size)-(.+)$/,pe,{autocomplete:["(border|b)-<num>","(border|b)-<directions>-<num>"]}],[/^(?:border|b)-([xy])-(?:width|size)-(.+)$/,pe],[/^(?:border|b)-([rltbse])-(?:width|size)-(.+)$/,pe],[/^(?:border|b)-(block|inline)-(?:width|size)-(.+)$/,pe],[/^(?:border|b)-([bi][se])-(?:width|size)-(.+)$/,pe],[/^(?:border|b)-()(?:color-)?(.+)$/,Pt,{autocomplete:["(border|b)-$colors","(border|b)-<directions>-$colors"]}],[/^(?:border|b)-([xy])-(?:color-)?(.+)$/,Pt],[/^(?:border|b)-([rltbse])-(?:color-)?(.+)$/,Pt],[/^(?:border|b)-(block|inline)-(?:color-)?(.+)$/,Pt],[/^(?:border|b)-([bi][se])-(?:color-)?(.+)$/,Pt],[/^(?:border|b)-()op(?:acity)?-?(.+)$/,Vt,{autocomplete:"(border|b)-(op|opacity)-<percent>"}],[/^(?:border|b)-([xy])-op(?:acity)?-?(.+)$/,Vt],[/^(?:border|b)-([rltbse])-op(?:acity)?-?(.+)$/,Vt],[/^(?:border|b)-(block|inline)-op(?:acity)?-?(.+)$/,Vt],[/^(?:border|b)-([bi][se])-op(?:acity)?-?(.+)$/,Vt],[/^(?:border-|b-)?(?:rounded|rd)()(?:-(.+))?$/,Mt,{autocomplete:["(border|b)-(rounded|rd)","(border|b)-(rounded|rd)-$borderRadius","(rounded|rd)","(rounded|rd)-$borderRadius"]}],[/^(?:border-|b-)?(?:rounded|rd)-([rltbse])(?:-(.+))?$/,Mt],[/^(?:border-|b-)?(?:rounded|rd)-([rltb]{2})(?:-(.+))?$/,Mt],[/^(?:border-|b-)?(?:rounded|rd)-([bise][se])(?:-(.+))?$/,Mt],[/^(?:border-|b-)?(?:rounded|rd)-([bi][se]-[bi][se])(?:-(.+))?$/,Mt],[/^(?:border|b)-(?:style-)?()(.+)$/,qe,{autocomplete:["(border|b)-style",`(border|b)-(${Te.join("|")})`,"(border|b)-<directions>-style",`(border|b)-<directions>-(${Te.join("|")})`,`(border|b)-<directions>-style-(${Te.join("|")})`,`(border|b)-style-(${Te.join("|")})`]}],[/^(?:border|b)-([xy])-(?:style-)?(.+)$/,qe],[/^(?:border|b)-([rltbse])-(?:style-)?(.+)$/,qe],[/^(?:border|b)-(block|inline)-(?:style-)?(.+)$/,qe],[/^(?:border|b)-([bi][se])-(?:style-)?(.+)$/,qe]];function Tc(e,t,r){if(t!=null)return{[`border${r}-color`]:A(e,t)};if(r===""){let n={},o="--un-border-opacity",i=A(e,`var(${o})`);return i.includes(o)&&(n[o]=typeof e=="string"?1:oe(e)),n["border-color"]=i,n}else{let n={},o="--un-border-opacity",i=`--un-border${r}-opacity`,s=A(e,`var(${i})`);return s.includes(i)&&(n[o]=typeof e=="string"?1:oe(e),n[i]=`var(${o})`),n[`border${r}-color`]=s,n}}function ad(e){return([,t],r)=>{let n=Pe(t,r,"borderColor");if(!n)return;let{alpha:o,color:i,cssColor:s}=n;if(s)return Tc(s,o,e);if(i)return Tc(i,o,e)}}function pe([,e="",t],{theme:r}){let n=r.lineWidth?.[t||"DEFAULT"]??u.bracket.cssvar.global.px(t||"1");if(e in Y&&n!=null)return Y[e].map(o=>[`border${o}-width`,n])}function Pt([,e="",t],r){if(e in Y){if(ve(u.bracket(t)))return pe(["",e,t],r);if(Me(t,r.theme,"borderColor"))return Object.assign({},...Y[e].map(n=>ad(n)(["",t],r.theme)))}}function Vt([,e="",t]){let r=u.bracket.percent.cssvar(t);if(e in Y&&r!=null)return Y[e].map(n=>[`--un-border${n}-opacity`,r])}function Mt([,e="",t],{theme:r}){let n=r.borderRadius?.[t||"DEFAULT"]||u.bracket.cssvar.global.fraction.rem(t||"1");if(e in yr&&n!=null)return yr[e].map(o=>[`border${o}-radius`,n])}function qe([,e="",t]){if(Te.includes(t)&&e in Y)return Y[e].map(r=>[`border${r}-style`,t])}var Pr=[[/^op(?:acity)?-?(.+)$/,([,e])=>({opacity:u.bracket.percent.cssvar(e)})]],cd=/^\[url\(.+\)\]$/,ld=/^\[(?:length|size):.+\]$/,ud=/^\[position:.+\]$/,fd=/^\[(?:linear|conic|radial)-gradient\(.+\)\]$/,pd=/^\[image:.+\]$/,Vr=[[/^bg-(.+)$/,(...e)=>{let t=e[0][1];if(cd.test(t))return{"--un-url":u.bracket(t),"background-image":"var(--un-url)"};if(ld.test(t)&&u.bracketOfLength(t)!=null)return{"background-size":u.bracketOfLength(t).split(" ").map(r=>u.fraction.auto.px.cssvar(r)??r).join(" ")};if((no(t)||ud.test(t))&&u.bracketOfPosition(t)!=null)return{"background-position":u.bracketOfPosition(t).split(" ").map(r=>u.position.fraction.auto.px.cssvar(r)??r).join(" ")};if(fd.test(t)||pd.test(t)){let r=u.bracket(t);if(r)return{"background-image":(r.startsWith("http")?`url(${r})`:u.cssvar(r))??r}}return B("background-color","bg","backgroundColor")(...e)},{autocomplete:"bg-$colors"}],[/^bg-op(?:acity)?-?(.+)$/,([,e])=>({"--un-bg-opacity":u.bracket.percent.cssvar(e)}),{autocomplete:"bg-(op|opacity)-<percent>"}]],Mr=[[/^color-scheme-(\w+)$/,([,e])=>({"color-scheme":e})]],Fr=[[/^@container(?:\/(\w+))?(?:-(normal|inline-size|size))?$/,([,e,t])=>({"container-type":t??"inline-size","container-name":e})]],jc=["solid","double","dotted","dashed","wavy",...P],_r=[[/^(?:decoration-)?(underline|overline|line-through)$/,([,e])=>({"text-decoration-line":e}),{autocomplete:"decoration-(underline|overline|line-through)"}],[/^(?:underline|decoration)-(?:size-)?(.+)$/,Pc,{autocomplete:"(underline|decoration)-<num>"}],[/^(?:underline|decoration)-(auto|from-font)$/,([,e])=>({"text-decoration-thickness":e}),{autocomplete:"(underline|decoration)-(auto|from-font)"}],[/^(?:underline|decoration)-(.+)$/,dd,{autocomplete:"(underline|decoration)-$colors"}],[/^(?:underline|decoration)-op(?:acity)?-?(.+)$/,([,e])=>({"--un-line-opacity":u.bracket.percent.cssvar(e)}),{autocomplete:"(underline|decoration)-(op|opacity)-<percent>"}],[/^(?:underline|decoration)-offset-(.+)$/,([,e],{theme:t})=>({"text-underline-offset":t.lineWidth?.[e]??u.auto.bracket.cssvar.global.px(e)}),{autocomplete:"(underline|decoration)-(offset)-<num>"}],...jc.map(e=>[`underline-${e}`,{"text-decoration-style":e}]),...jc.map(e=>[`decoration-${e}`,{"text-decoration-style":e}]),["no-underline",{"text-decoration":"none"}],["decoration-none",{"text-decoration":"none"}]];function Pc([,e],{theme:t}){return{"text-decoration-thickness":t.lineWidth?.[e]??u.bracket.cssvar.global.px(e)}}function dd(e,t){if(ve(u.bracket(e[1])))return Pc(e,t);let r=B("text-decoration-color","line","borderColor")(e,t);if(r)return{"-webkit-text-decoration-color":r["text-decoration-color"],...r}}var Lr=[["flex",{display:"flex"}],["inline-flex",{display:"inline-flex"}],["flex-inline",{display:"inline-flex"}],[/^flex-(.*)$/,([,e])=>({flex:u.bracket(e)!=null?u.bracket(e).split(" ").map(t=>u.cssvar.fraction(t)??t).join(" "):u.cssvar.fraction(e)})],["flex-1",{flex:"1 1 0%"}],["flex-auto",{flex:"1 1 auto"}],["flex-initial",{flex:"0 1 auto"}],["flex-none",{flex:"none"}],[/^(?:flex-)?shrink(?:-(.*))?$/,([,e=""])=>({"flex-shrink":u.bracket.cssvar.number(e)??1}),{autocomplete:["flex-shrink-<num>","shrink-<num>"]}],[/^(?:flex-)?grow(?:-(.*))?$/,([,e=""])=>({"flex-grow":u.bracket.cssvar.number(e)??1}),{autocomplete:["flex-grow-<num>","grow-<num>"]}],[/^(?:flex-)?basis-(.+)$/,([,e],{theme:t})=>({"flex-basis":t.spacing?.[e]??u.bracket.cssvar.auto.fraction.rem(e)}),{autocomplete:["flex-basis-$spacing","basis-$spacing"]}],["flex-row",{"flex-direction":"row"}],["flex-row-reverse",{"flex-direction":"row-reverse"}],["flex-col",{"flex-direction":"column"}],["flex-col-reverse",{"flex-direction":"column-reverse"}],["flex-wrap",{"flex-wrap":"wrap"}],["flex-wrap-reverse",{"flex-wrap":"wrap-reverse"}],["flex-nowrap",{"flex-wrap":"nowrap"}]],md={"":"",x:"column-",y:"row-",col:"column-",row:"row-"};function so([,e="",t],{theme:r}){let n=r.spacing?.[t]??u.bracket.cssvar.global.rem(t);if(n!=null)return{[`${md[e]}gap`]:n}}var Wr=[[/^(?:flex-|grid-)?gap-?()(.+)$/,so,{autocomplete:["gap-$spacing","gap-<num>"]}],[/^(?:flex-|grid-)?gap-([xy])-?(.+)$/,so,{autocomplete:["gap-(x|y)-$spacing","gap-(x|y)-<num>"]}],[/^(?:flex-|grid-)?gap-(col|row)-?(.+)$/,so,{autocomplete:["gap-(col|row)-$spacing","gap-(col|row)-<num>"]}]];function be(e){return e.replace("col","column")}function ao(e){return e[0]==="r"?"Row":"Column"}function hd(e,t,r){let n=t[`gridAuto${ao(e)}`]?.[r];if(n!=null)return n;switch(r){case"min":return"min-content";case"max":return"max-content";case"fr":return"minmax(0,1fr)"}return u.bracket.cssvar.auto.rem(r)}var Ur=[["grid",{display:"grid"}],["inline-grid",{display:"inline-grid"}],[/^(?:grid-)?(row|col)-(.+)$/,([,e,t],{theme:r})=>({[`grid-${be(e)}`]:r[`grid${ao(e)}`]?.[t]??u.bracket.cssvar.auto(t)})],[/^(?:grid-)?(row|col)-span-(.+)$/,([,e,t])=>{if(t==="full")return{[`grid-${be(e)}`]:"1/-1"};let r=u.bracket.number(t);if(r!=null)return{[`grid-${be(e)}`]:`span ${r}/span ${r}`}},{autocomplete:"(grid-row|grid-col|row|col)-span-<num>"}],[/^(?:grid-)?(row|col)-start-(.+)$/,([,e,t])=>({[`grid-${be(e)}-start`]:u.bracket.cssvar(t)??t})],[/^(?:grid-)?(row|col)-end-(.+)$/,([,e,t])=>({[`grid-${be(e)}-end`]:u.bracket.cssvar(t)??t}),{autocomplete:"(grid-row|grid-col|row|col)-(start|end)-<num>"}],[/^(?:grid-)?auto-(rows|cols)-(.+)$/,([,e,t],{theme:r})=>({[`grid-auto-${be(e)}`]:hd(e,r,t)}),{autocomplete:"(grid-auto|auto)-(rows|cols)-<num>"}],[/^(?:grid-auto-flow|auto-flow|grid-flow)-(.+)$/,([,e])=>({"grid-auto-flow":u.bracket.cssvar(e)})],[/^(?:grid-auto-flow|auto-flow|grid-flow)-(row|col|dense|row-dense|col-dense)$/,([,e])=>({"grid-auto-flow":be(e).replace("-"," ")}),{autocomplete:["(grid-auto-flow|auto-flow|grid-flow)-(row|col|dense|row-dense|col-dense)"]}],[/^(?:grid-)?(rows|cols)-(.+)$/,([,e,t],{theme:r})=>({[`grid-template-${be(e)}`]:r[`gridTemplate${ao(e)}`]?.[t]??u.bracket.cssvar(t)})],[/^(?:grid-)?(rows|cols)-minmax-([\w.-]+)$/,([,e,t])=>({[`grid-template-${be(e)}`]:`repeat(auto-fill,minmax(${t},1fr))`})],[/^(?:grid-)?(rows|cols)-(\d+)$/,([,e,t])=>({[`grid-template-${be(e)}`]:`repeat(${t},minmax(0,1fr))`}),{autocomplete:"(grid-rows|grid-cols|rows|cols)-<num>"}],[/^grid-area(s)?-(.+)$/,([,e,t])=>e!=null?{"grid-template-areas":u.cssvar(t)??t.split("-").map(r=>`"${u.bracket(r)}"`).join(" ")}:{"grid-area":u.bracket.cssvar(t)}],["grid-rows-none",{"grid-template-rows":"none"}],["grid-cols-none",{"grid-template-columns":"none"}],["grid-rows-subgrid",{"grid-template-rows":"subgrid"}],["grid-cols-subgrid",{"grid-template-columns":"subgrid"}]],Cr=["auto","hidden","clip","visible","scroll","overlay",...P],Br=[[/^(?:overflow|of)-(.+)$/,([,e])=>Cr.includes(e)?{overflow:e}:void 0,{autocomplete:[`(overflow|of)-(${Cr.join("|")})`,`(overflow|of)-(x|y)-(${Cr.join("|")})`]}],[/^(?:overflow|of)-([xy])-(.+)$/,([,e,t])=>Cr.includes(t)?{[`overflow-${e}`]:t}:void 0]],Dr=[[/^(?:position-|pos-)?(relative|absolute|fixed|sticky)$/,([,e])=>({position:e}),{autocomplete:["(position|pos)-<position>","(position|pos)-<globalKeyword>","<position>"]}],[/^(?:position-|pos-)([-\w]+)$/,([,e])=>P.includes(e)?{position:e}:void 0],[/^(?:position-|pos-)?(static)$/,([,e])=>({position:e})]],_t=[["justify-start",{"justify-content":"flex-start"}],["justify-end",{"justify-content":"flex-end"}],["justify-center",{"justify-content":"center"}],["justify-between",{"justify-content":"space-between"}],["justify-around",{"justify-content":"space-around"}],["justify-evenly",{"justify-content":"space-evenly"}],["justify-stretch",{"justify-content":"stretch"}],["justify-left",{"justify-content":"left"}],["justify-right",{"justify-content":"right"}],...w("justify","justify-content"),["justify-items-start",{"justify-items":"start"}],["justify-items-end",{"justify-items":"end"}],["justify-items-center",{"justify-items":"center"}],["justify-items-stretch",{"justify-items":"stretch"}],...w("justify-items"),["justify-self-auto",{"justify-self":"auto"}],["justify-self-start",{"justify-self":"start"}],["justify-self-end",{"justify-self":"end"}],["justify-self-center",{"justify-self":"center"}],["justify-self-stretch",{"justify-self":"stretch"}],...w("justify-self")],Ir=[[/^order-(.+)$/,([,e])=>({order:u.bracket.cssvar.number(e)})],["order-first",{order:"-9999"}],["order-last",{order:"9999"}],["order-none",{order:"0"}]],Lt=[["content-center",{"align-content":"center"}],["content-start",{"align-content":"flex-start"}],["content-end",{"align-content":"flex-end"}],["content-between",{"align-content":"space-between"}],["content-around",{"align-content":"space-around"}],["content-evenly",{"align-content":"space-evenly"}],...w("content","align-content"),["items-start",{"align-items":"flex-start"}],["items-end",{"align-items":"flex-end"}],["items-center",{"align-items":"center"}],["items-baseline",{"align-items":"baseline"}],["items-stretch",{"align-items":"stretch"}],...w("items","align-items"),["self-auto",{"align-self":"auto"}],["self-start",{"align-self":"flex-start"}],["self-end",{"align-self":"flex-end"}],["self-center",{"align-self":"center"}],["self-stretch",{"align-self":"stretch"}],["self-baseline",{"align-self":"baseline"}],...w("self","align-self")],Wt=[["place-content-center",{"place-content":"center"}],["place-content-start",{"place-content":"start"}],["place-content-end",{"place-content":"end"}],["place-content-between",{"place-content":"space-between"}],["place-content-around",{"place-content":"space-around"}],["place-content-evenly",{"place-content":"space-evenly"}],["place-content-stretch",{"place-content":"stretch"}],...w("place-content"),["place-items-start",{"place-items":"start"}],["place-items-end",{"place-items":"end"}],["place-items-center",{"place-items":"center"}],["place-items-stretch",{"place-items":"stretch"}],...w("place-items"),["place-self-auto",{"place-self":"auto"}],["place-self-start",{"place-self":"start"}],["place-self-end",{"place-self":"end"}],["place-self-center",{"place-self":"center"}],["place-self-stretch",{"place-self":"stretch"}],...w("place-self")],Nr=[..._t,...Lt,...Wt].flatMap(([e,t])=>[[`flex-${e}`,t],[`grid-${e}`,t]]);function co(e,{theme:t}){return t.spacing?.[e]??u.bracket.cssvar.global.auto.fraction.rem(e)}function Ft([,e,t],r){let n=co(t,r);if(n!=null&&e in xr)return xr[e].map(o=>[o.slice(1),n])}var Kr=[[/^(?:position-|pos-)?inset-(.+)$/,([,e],t)=>({inset:co(e,t)}),{autocomplete:["(position|pos)-inset-<directions>-$spacing","(position|pos)-inset-(block|inline)-$spacing","(position|pos)-inset-(bs|be|is|ie)-$spacing","(position|pos)-(top|left|right|bottom)-$spacing"]}],[/^(?:position-|pos-)?(start|end)-(.+)$/,Ft],[/^(?:position-|pos-)?inset-([xy])-(.+)$/,Ft],[/^(?:position-|pos-)?inset-([rltbse])-(.+)$/,Ft],[/^(?:position-|pos-)?inset-(block|inline)-(.+)$/,Ft],[/^(?:position-|pos-)?inset-([bi][se])-(.+)$/,Ft],[/^(?:position-|pos-)?(top|left|right|bottom)-(.+)$/,([,e,t],r)=>({[e]:co(t,r)})]],Gr=[["float-left",{float:"left"}],["float-right",{float:"right"}],["float-start",{float:"inline-start"}],["float-end",{float:"inline-end"}],["float-none",{float:"none"}],...w("float"),["clear-left",{clear:"left"}],["clear-right",{clear:"right"}],["clear-both",{clear:"both"}],["clear-start",{clear:"inline-start"}],["clear-end",{clear:"inline-end"}],["clear-none",{clear:"none"}],...w("clear")],Hr=[[/^(?:position-|pos-)?z([\d.]+)$/,([,e])=>({"z-index":u.number(e)})],[/^(?:position-|pos-)?z-(.+)$/,([,e],{theme:t})=>({"z-index":t.zIndex?.[e]??u.bracket.cssvar.global.auto.number(e)}),{autocomplete:"z-<num>"}]],qr=[["box-border",{"box-sizing":"border-box"}],["box-content",{"box-sizing":"content-box"}],...w("box","box-sizing")],Yr=[[/^(where|\?)$/,(e,{constructCSS:t,generator:r})=>{if(r.userConfig.envMode==="dev")return`@keyframes __un_qm{0%{box-shadow:inset 4px 4px #ff1e90, inset -4px -4px #ff1e90}100%{box-shadow:inset 8px 8px #3399ff, inset -8px -8px #3399ff}} ${t({animation:"__un_qm 0.5s ease-in-out alternate infinite"})}`}]],gd={h:"height",w:"width",inline:"inline-size",block:"block-size"};function Fe(e,t){return`${e||""}${gd[t]}`}function Rr(e,t,r,n){let o=Fe(e,t).replace(/-(\w)/g,(s,a)=>a.toUpperCase()),i=r[o]?.[n];if(i!=null)return i;switch(n){case"fit":case"max":case"min":return`${n}-content`}return u.bracket.cssvar.global.auto.fraction.rem(n)}var Xr=[[/^size-(min-|max-)?(.+)$/,([,e,t],{theme:r})=>({[Fe(e,"w")]:Rr(e,"w",r,t),[Fe(e,"h")]:Rr(e,"h",r,t)})],[/^(?:size-)?(min-|max-)?([wh])-?(.+)$/,([,e,t,r],{theme:n})=>({[Fe(e,t)]:Rr(e,t,n,r)})],[/^(?:size-)?(min-|max-)?(block|inline)-(.+)$/,([,e,t,r],{theme:n})=>({[Fe(e,t)]:Rr(e,t,n,r)}),{autocomplete:["(w|h)-$width|height|maxWidth|maxHeight|minWidth|minHeight|inlineSize|blockSize|maxInlineSize|maxBlockSize|minInlineSize|minBlockSize","(block|inline)-$width|height|maxWidth|maxHeight|minWidth|minHeight|inlineSize|blockSize|maxInlineSize|maxBlockSize|minInlineSize|minBlockSize","(max|min)-(w|h|block|inline)","(max|min)-(w|h|block|inline)-$width|height|maxWidth|maxHeight|minWidth|minHeight|inlineSize|blockSize|maxInlineSize|maxBlockSize|minInlineSize|minBlockSize","(w|h)-full","(max|min)-(w|h)-full"]}],[/^(?:size-)?(min-|max-)?(h)-screen-(.+)$/,([,e,t,r],n)=>({[Fe(e,t)]:zc(n,r,"verticalBreakpoints")})],[/^(?:size-)?(min-|max-)?(w)-screen-(.+)$/,([,e,t,r],n)=>({[Fe(e,t)]:zc(n,r)}),{autocomplete:["(w|h)-screen","(min|max)-(w|h)-screen","h-screen-$verticalBreakpoints","(min|max)-h-screen-$verticalBreakpoints","w-screen-$breakpoints","(min|max)-w-screen-$breakpoints"]}]];function zc(e,t,r="breakpoints"){let n=Ee(e,r);if(n)return n.find(o=>o.point===t)?.size}function bd(e){if(/^\d+\/\d+$/.test(e))return e;switch(e){case"square":return"1/1";case"video":return"16/9"}return u.bracket.cssvar.global.auto.number(e)}var Zr=[[/^(?:size-)?aspect-(?:ratio-)?(.+)$/,([,e])=>({"aspect-ratio":bd(e)}),{autocomplete:["aspect-(square|video|ratio)","aspect-ratio-(square|video)"]}]],Jr=[[/^pa?()-?(.+)$/,L("padding"),{autocomplete:["(m|p)<num>","(m|p)-<num>"]}],[/^p-?xy()()$/,L("padding"),{autocomplete:"(m|p)-(xy)"}],[/^p-?([xy])(?:-?(.+))?$/,L("padding")],[/^p-?([rltbse])(?:-?(.+))?$/,L("padding"),{autocomplete:"(m|p)<directions>-<num>"}],[/^p-(block|inline)(?:-(.+))?$/,L("padding"),{autocomplete:"(m|p)-(block|inline)-<num>"}],[/^p-?([bi][se])(?:-?(.+))?$/,L("padding"),{autocomplete:"(m|p)-(bs|be|is|ie)-<num>"}]],Qr=[[/^ma?()-?(.+)$/,L("margin")],[/^m-?xy()()$/,L("margin")],[/^m-?([xy])(?:-?(.+))?$/,L("margin")],[/^m-?([rltbse])(?:-?(.+))?$/,L("margin")],[/^m-(block|inline)(?:-(.+))?$/,L("margin")],[/^m-?([bi][se])(?:-?(.+))?$/,L("margin")]],en=[[/^fill-(.+)$/,B("fill","fill","backgroundColor"),{autocomplete:"fill-$colors"}],[/^fill-op(?:acity)?-?(.+)$/,([,e])=>({"--un-fill-opacity":u.bracket.percent.cssvar(e)}),{autocomplete:"fill-(op|opacity)-<percent>"}],["fill-none",{fill:"none"}],[/^stroke-(?:width-|size-)?(.+)$/,Vc,{autocomplete:["stroke-width-$lineWidth","stroke-size-$lineWidth"]}],[/^stroke-dash-(.+)$/,([,e])=>({"stroke-dasharray":u.bracket.cssvar.number(e)}),{autocomplete:"stroke-dash-<num>"}],[/^stroke-offset-(.+)$/,([,e],{theme:t})=>({"stroke-dashoffset":t.lineWidth?.[e]??u.bracket.cssvar.px.numberWithUnit(e)}),{autocomplete:"stroke-offset-$lineWidth"}],[/^stroke-(.+)$/,xd,{autocomplete:"stroke-$colors"}],[/^stroke-op(?:acity)?-?(.+)$/,([,e])=>({"--un-stroke-opacity":u.bracket.percent.cssvar(e)}),{autocomplete:"stroke-(op|opacity)-<percent>"}],["stroke-cap-square",{"stroke-linecap":"square"}],["stroke-cap-round",{"stroke-linecap":"round"}],["stroke-cap-auto",{"stroke-linecap":"butt"}],["stroke-join-arcs",{"stroke-linejoin":"arcs"}],["stroke-join-bevel",{"stroke-linejoin":"bevel"}],["stroke-join-clip",{"stroke-linejoin":"miter-clip"}],["stroke-join-round",{"stroke-linejoin":"round"}],["stroke-join-auto",{"stroke-linejoin":"miter"}],["stroke-none",{stroke:"none"}]];function Vc([,e],{theme:t}){return{"stroke-width":t.lineWidth?.[e]??u.bracket.cssvar.fraction.px.number(e)}}function xd(e,t){return ve(u.bracket(e[1]))?Vc(e,t):B("stroke","stroke","borderColor")(e,t)}function Ac(e,t){let r;if(u.cssvar(e)!=null)r=u.cssvar(e);else{e.startsWith("[")&&e.endsWith("]")&&(e=e.slice(1,-1));let n=e.split(",").map(o=>t.transitionProperty?.[o]??u.properties(o));n.every(Boolean)&&(r=n.join(","))}return r}var tn=[[/^transition(?:-(\D+?))?(?:-(\d+))?$/,([,e,t],{theme:r})=>{if(!e&&!t)return{"transition-property":r.transitionProperty?.DEFAULT,"transition-timing-function":r.easing?.DEFAULT,"transition-duration":r.duration?.DEFAULT??u.time("150")};if(e!=null){let n=Ac(e,r),o=r.duration?.[t||"DEFAULT"]??u.time(t||"150");if(n)return{"transition-property":n,"transition-timing-function":r.easing?.DEFAULT,"transition-duration":o}}else if(t!=null)return{"transition-property":r.transitionProperty?.DEFAULT,"transition-timing-function":r.easing?.DEFAULT,"transition-duration":r.duration?.[t]??u.time(t)}},{autocomplete:"transition-$transitionProperty-$duration"}],[/^(?:transition-)?duration-(.+)$/,([,e],{theme:t})=>({"transition-duration":t.duration?.[e||"DEFAULT"]??u.bracket.cssvar.time(e)}),{autocomplete:["transition-duration-$duration","duration-$duration"]}],[/^(?:transition-)?delay-(.+)$/,([,e],{theme:t})=>({"transition-delay":t.duration?.[e||"DEFAULT"]??u.bracket.cssvar.time(e)}),{autocomplete:["transition-delay-$duration","delay-$duration"]}],[/^(?:transition-)?ease(?:-(.+))?$/,([,e],{theme:t})=>({"transition-timing-function":t.easing?.[e||"DEFAULT"]??u.bracket.cssvar(e)}),{autocomplete:["transition-ease-(linear|in|out|in-out|DEFAULT)","ease-(linear|in|out|in-out|DEFAULT)"]}],[/^(?:transition-)?property-(.+)$/,([,e],{theme:t})=>{let r=u.global(e)||Ac(e,t);if(r)return{"transition-property":r}},{autocomplete:[`transition-property-(${[...P].join("|")})`,"transition-property-$transitionProperty","property-$transitionProperty"]}],["transition-none",{transition:"none"}],...w("transition"),["transition-discrete",{"transition-behavior":"allow-discrete"}],["transition-normal",{"transition-behavior":"normal"}]],rn=[[/^text-(.+)$/,$d,{autocomplete:"text-$fontSize"}],[/^(?:text|font)-size-(.+)$/,Mc,{autocomplete:"text-size-$fontSize"}],[/^text-(?:color-)?(.+)$/,yd,{autocomplete:"text-$colors"}],[/^(?:color|c)-(.+)$/,B("color","text","textColor"),{autocomplete:"(color|c)-$colors"}],[/^(?:text|color|c)-(.+)$/,([,e])=>P.includes(e)?{color:e}:void 0,{autocomplete:`(text|color|c)-(${P.join("|")})`}],[/^(?:text|color|c)-op(?:acity)?-?(.+)$/,([,e])=>({"--un-text-opacity":u.bracket.percent.cssvar(e)}),{autocomplete:"(text|color|c)-(op|opacity)-<percent>"}],[/^(?:font|fw)-?([^-]+)$/,([,e],{theme:t})=>({"font-weight":t.fontWeight?.[e]||u.bracket.global.number(e)}),{autocomplete:["(font|fw)-(100|200|300|400|500|600|700|800|900)","(font|fw)-$fontWeight"]}],[/^(?:font-)?(?:leading|lh|line-height)-(.+)$/,([,e],{theme:t})=>({"line-height":lo(e,t,"lineHeight")}),{autocomplete:"(leading|lh|line-height)-$lineHeight"}],["font-synthesis-weight",{"font-synthesis":"weight"}],["font-synthesis-style",{"font-synthesis":"style"}],["font-synthesis-small-caps",{"font-synthesis":"small-caps"}],["font-synthesis-none",{"font-synthesis":"none"}],[/^font-synthesis-(.+)$/,([,e])=>({"font-synthesis":u.bracket.cssvar.global(e)})],[/^(?:font-)?tracking-(.+)$/,([,e],{theme:t})=>({"letter-spacing":t.letterSpacing?.[e]||u.bracket.cssvar.global.rem(e)}),{autocomplete:"tracking-$letterSpacing"}],[/^(?:font-)?word-spacing-(.+)$/,([,e],{theme:t})=>({"word-spacing":t.wordSpacing?.[e]||u.bracket.cssvar.global.rem(e)}),{autocomplete:"word-spacing-$wordSpacing"}],["font-stretch-normal",{"font-stretch":"normal"}],["font-stretch-ultra-condensed",{"font-stretch":"ultra-condensed"}],["font-stretch-extra-condensed",{"font-stretch":"extra-condensed"}],["font-stretch-condensed",{"font-stretch":"condensed"}],["font-stretch-semi-condensed",{"font-stretch":"semi-condensed"}],["font-stretch-semi-expanded",{"font-stretch":"semi-expanded"}],["font-stretch-expanded",{"font-stretch":"expanded"}],["font-stretch-extra-expanded",{"font-stretch":"extra-expanded"}],["font-stretch-ultra-expanded",{"font-stretch":"ultra-expanded"}],[/^font-stretch-(.+)$/,([,e])=>({"font-stretch":u.bracket.cssvar.fraction.global(e)}),{autocomplete:"font-stretch-<percentage>"}],[/^font-(.+)$/,([,e],{theme:t})=>({"font-family":t.fontFamily?.[e]||u.bracket.cssvar.global(e)}),{autocomplete:"font-$fontFamily"}]],nn=[[/^tab(?:-(.+))?$/,([,e])=>{let t=u.bracket.cssvar.global.number(e||"4");if(t!=null)return{"-moz-tab-size":t,"-o-tab-size":t,"tab-size":t}}]],on=[[/^indent(?:-(.+))?$/,([,e],{theme:t})=>({"text-indent":t.textIndent?.[e||"DEFAULT"]||u.bracket.cssvar.global.fraction.rem(e)}),{autocomplete:"indent-$textIndent"}]],sn=[[/^text-stroke(?:-(.+))?$/,([,e],{theme:t})=>({"-webkit-text-stroke-width":t.textStrokeWidth?.[e||"DEFAULT"]||u.bracket.cssvar.px(e)}),{autocomplete:"text-stroke-$textStrokeWidth"}],[/^text-stroke-(.+)$/,B("-webkit-text-stroke-color","text-stroke","borderColor"),{autocomplete:"text-stroke-$colors"}],[/^text-stroke-op(?:acity)?-?(.+)$/,([,e])=>({"--un-text-stroke-opacity":u.bracket.percent.cssvar(e)}),{autocomplete:"text-stroke-(op|opacity)-<percent>"}]],an=[[/^text-shadow(?:-(.+))?$/,([,e],{theme:t})=>{let r=t.textShadow?.[e||"DEFAULT"];return r!=null?{"--un-text-shadow":Ve(r,"--un-text-shadow-color").join(","),"text-shadow":"var(--un-text-shadow)"}:{"text-shadow":u.bracket.cssvar.global(e)}},{autocomplete:"text-shadow-$textShadow"}],[/^text-shadow-color-(.+)$/,B("--un-text-shadow-color","text-shadow","shadowColor"),{autocomplete:"text-shadow-color-$colors"}],[/^text-shadow-color-op(?:acity)?-?(.+)$/,([,e])=>({"--un-text-shadow-opacity":u.bracket.percent.cssvar(e)}),{autocomplete:"text-shadow-color-(op|opacity)-<percent>"}]];function lo(e,t,r){return t[r]?.[e]||u.bracket.cssvar.global.rem(e)}function Mc([,e],{theme:t}){let n=C(t.fontSize?.[e])?.[0]??u.bracket.cssvar.global.rem(e);if(n!=null)return{"font-size":n}}function yd(e,t){return ve(u.bracket(e[1]))?Mc(e,t):B("color","text","textColor")(e,t)}function $d([,e="base"],{theme:t}){let r=kr(e,"length");if(!r)return;let[n,o]=r,i=C(t.fontSize?.[n]),s=o?lo(o,t,"lineHeight"):void 0;if(i?.[0]){let[c,l,p]=i;return typeof l=="object"?{"font-size":c,...l}:{"font-size":c,"line-height":s??l??"1","letter-spacing":p?lo(p,t,"letterSpacing"):void 0}}let a=u.bracketOfLength.rem(n);return s&&a?{"font-size":a,"line-height":s}:{"font-size":u.bracketOfLength.rem(e)}}var vd={backface:"backface-visibility",break:"word-break",case:"text-transform",content:"align-content",fw:"font-weight",items:"align-items",justify:"justify-content",select:"user-select",self:"align-self",vertical:"vertical-align",visible:"visibility",whitespace:"white-space",ws:"white-space"},cn=[[/^(.+?)-(\$.+)$/,([,e,t])=>{let r=vd[e];if(r)return{[r]:u.cssvar(t)}}]],ln=[[/^\[(.*)\]$/,([e,t])=>{if(!t.includes(":"))return;let[r,...n]=t.split(":"),o=n.join(":");if(!kd(t)&&/^[a-z-]+$/.test(r)&&wd(o)){let i=u.bracket(`[${o}]`);if(i)return{[r]:i}}}]];function wd(e){let t=0;function r(n){for(;t<e.length;)if(t+=1,e[t]===n)return!0;return!1}for(t=0;t<e.length;t++){let n=e[t];if("\"`'".includes(n)){if(!r(n))return!1}else if(n==="("){if(!r(")"))return!1}else if("[]{}:".includes(n))return!1}return!0}function kd(e){if(!e.includes("://"))return!1;try{return new URL(e).host!==""}catch{return!1}}var Sd=[cn,ln,bt,xt,ht,Dr,Kr,Hr,Ir,Ur,Gr,Qr,qr,mt,Zr,Xr,Lr,Ot,gt,$t,yt,zr,Wt,Lt,_t,Wr,Nr,Br,Rt,vt,St,Or,Vr,Mr,en,Jr,Tr,on,Ct,Er,rn,Et,Tt,_r,jt,nn,sn,an,Pr,At,jr,zt,tn,Ar,wt,kt,Fr,Yr].flat(1);var Fc=[[/^divide-?([xy])$/,un,{autocomplete:["divide-(x|y|block|inline)","divide-(x|y|block|inline)-reverse","divide-(x|y|block|inline)-$lineWidth"]}],[/^divide-?([xy])-?(.+)$/,un],[/^divide-?([xy])-reverse$/,([,e])=>({[`--un-divide-${e}-reverse`]:1})],[/^divide-(block|inline)$/,un],[/^divide-(block|inline)-(.+)$/,un],[/^divide-(block|inline)-reverse$/,([,e])=>({[`--un-divide-${e}-reverse`]:1})],[/^divide-(.+)$/,B("border-color","divide","borderColor"),{autocomplete:"divide-$colors"}],[/^divide-op(?:acity)?-?(.+)$/,([,e])=>({"--un-divide-opacity":u.bracket.percent(e)}),{autocomplete:["divide-(op|opacity)","divide-(op|opacity)-<percent>"]}],...Te.map(e=>[`divide-${e}`,{"border-style":e}])];function un([,e,t],{theme:r}){let n=r.lineWidth?.[t||"DEFAULT"]??u.bracket.cssvar.px(t||"1");if(n!=null){n==="0"&&(n="0px");let o=Y[e].map(i=>{let s=`border${i}-width`,a=i.endsWith("right")||i.endsWith("bottom")?`calc(${n} * var(--un-divide-${e}-reverse))`:`calc(${n} * calc(1 - var(--un-divide-${e}-reverse)))`;return[s,a]});if(o)return[[`--un-divide-${e}-reverse`,0],...o]}}var fo={"--un-blur":j,"--un-brightness":j,"--un-contrast":j,"--un-drop-shadow":j,"--un-grayscale":j,"--un-hue-rotate":j,"--un-invert":j,"--un-saturate":j,"--un-sepia":j},Lc=Object.keys(fo),_c={preflightKeys:Lc},fn="var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia)",po={"--un-backdrop-blur":j,"--un-backdrop-brightness":j,"--un-backdrop-contrast":j,"--un-backdrop-grayscale":j,"--un-backdrop-hue-rotate":j,"--un-backdrop-invert":j,"--un-backdrop-opacity":j,"--un-backdrop-saturate":j,"--un-backdrop-sepia":j},Wc=Object.keys(po),Rd={preflightKeys:Wc},pn="var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia)",we={preflightKeys:[...Lc,...Wc]};function uo(e){let t=u.bracket.cssvar(e||"");if(t!=null||(t=e?u.percent(e):"1",t!=null&&Number.parseFloat(t)<=1))return t}function ke(e,t){return([,r,n],{theme:o})=>{let i=t(n,o)??(n==="none"?"0":"");if(i!=="")return r?{[`--un-${r}${e}`]:`${e}(${i})`,"-webkit-backdrop-filter":pn,"backdrop-filter":pn}:{[`--un-${e}`]:`${e}(${i})`,filter:fn}}}function Ed([,e],{theme:t}){let r=t.dropShadow?.[e||"DEFAULT"];if(r!=null)return{"--un-drop-shadow":`drop-shadow(${Ve(r,"--un-drop-shadow-color").join(") drop-shadow(")})`,filter:fn};if(r=u.bracket.cssvar(e),r!=null)return{"--un-drop-shadow":`drop-shadow(${r})`,filter:fn}}var Uc=[[/^(?:(backdrop-)|filter-)?blur(?:-(.+))?$/,ke("blur",(e,t)=>t.blur?.[e||"DEFAULT"]||u.bracket.cssvar.px(e)),{custom:we,autocomplete:["(backdrop|filter)-blur-$blur","blur-$blur","filter-blur"]}],[/^(?:(backdrop-)|filter-)?brightness-(.+)$/,ke("brightness",e=>u.bracket.cssvar.percent(e)),{custom:we,autocomplete:["(backdrop|filter)-brightness-<percent>","brightness-<percent>"]}],[/^(?:(backdrop-)|filter-)?contrast-(.+)$/,ke("contrast",e=>u.bracket.cssvar.percent(e)),{custom:we,autocomplete:["(backdrop|filter)-contrast-<percent>","contrast-<percent>"]}],[/^(?:filter-)?drop-shadow(?:-(.+))?$/,Ed,{custom:_c,autocomplete:["filter-drop","filter-drop-shadow","filter-drop-shadow-color","drop-shadow","drop-shadow-color","filter-drop-shadow-$dropShadow","drop-shadow-$dropShadow","filter-drop-shadow-color-$colors","drop-shadow-color-$colors","filter-drop-shadow-color-(op|opacity)","drop-shadow-color-(op|opacity)","filter-drop-shadow-color-(op|opacity)-<percent>","drop-shadow-color-(op|opacity)-<percent>"]}],[/^(?:filter-)?drop-shadow-color-(.+)$/,B("--un-drop-shadow-color","drop-shadow","shadowColor")],[/^(?:filter-)?drop-shadow-color-op(?:acity)?-?(.+)$/,([,e])=>({"--un-drop-shadow-opacity":u.bracket.percent(e)})],[/^(?:(backdrop-)|filter-)?grayscale(?:-(.+))?$/,ke("grayscale",uo),{custom:we,autocomplete:["(backdrop|filter)-grayscale","(backdrop|filter)-grayscale-<percent>","grayscale-<percent>"]}],[/^(?:(backdrop-)|filter-)?hue-rotate-(.+)$/,ke("hue-rotate",e=>u.bracket.cssvar.degree(e)),{custom:we}],[/^(?:(backdrop-)|filter-)?invert(?:-(.+))?$/,ke("invert",uo),{custom:we,autocomplete:["(backdrop|filter)-invert","(backdrop|filter)-invert-<percent>","invert-<percent>"]}],[/^(backdrop-)op(?:acity)?-(.+)$/,ke("opacity",e=>u.bracket.cssvar.percent(e)),{custom:we,autocomplete:["backdrop-(op|opacity)","backdrop-(op|opacity)-<percent>"]}],[/^(?:(backdrop-)|filter-)?saturate-(.+)$/,ke("saturate",e=>u.bracket.cssvar.percent(e)),{custom:we,autocomplete:["(backdrop|filter)-saturate","(backdrop|filter)-saturate-<percent>","saturate-<percent>"]}],[/^(?:(backdrop-)|filter-)?sepia(?:-(.+))?$/,ke("sepia",uo),{custom:we,autocomplete:["(backdrop|filter)-sepia","(backdrop|filter)-sepia-<percent>","sepia-<percent>"]}],["filter",{filter:fn},{custom:_c}],["backdrop-filter",{"-webkit-backdrop-filter":pn,"backdrop-filter":pn},{custom:Rd}],["filter-none",{filter:"none"}],["backdrop-filter-none",{"-webkit-backdrop-filter":"none","backdrop-filter":"none"}],...P.map(e=>[`filter-${e}`,{filter:e}]),...P.map(e=>[`backdrop-filter-${e}`,{"-webkit-backdrop-filter":e,"backdrop-filter":e}])];var Bc=[[/^line-clamp-(\d+)$/,([,e])=>({overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":e,"line-clamp":e}),{autocomplete:["line-clamp","line-clamp-<num>"]}],...["none",...P].map(e=>[`line-clamp-${e}`,{overflow:"visible",display:"block","-webkit-box-orient":"horizontal","-webkit-line-clamp":e,"line-clamp":e}])];var Dc=[[/^\$ placeholder-(.+)$/,B("color","placeholder","accentColor"),{autocomplete:"placeholder-$colors"}],[/^\$ placeholder-op(?:acity)?-?(.+)$/,([,e])=>({"--un-placeholder-opacity":u.bracket.percent(e)}),{autocomplete:["placeholder-(op|opacity)","placeholder-(op|opacity)-<percent>"]}]];var mo={"--un-scroll-snap-strictness":"proximity"},Ic={preflightKeys:Object.keys(mo)},Nc=[[/^snap-(x|y)$/,([,e])=>({"scroll-snap-type":`${e} var(--un-scroll-snap-strictness)`}),{custom:Ic,autocomplete:"snap-(x|y|both)"}],[/^snap-both$/,()=>({"scroll-snap-type":"both var(--un-scroll-snap-strictness)"}),{custom:Ic}],["snap-mandatory",{"--un-scroll-snap-strictness":"mandatory"}],["snap-proximity",{"--un-scroll-snap-strictness":"proximity"}],["snap-none",{"scroll-snap-type":"none"}],["snap-start",{"scroll-snap-align":"start"}],["snap-end",{"scroll-snap-align":"end"}],["snap-center",{"scroll-snap-align":"center"}],["snap-align-none",{"scroll-snap-align":"none"}],["snap-normal",{"scroll-snap-stop":"normal"}],["snap-always",{"scroll-snap-stop":"always"}],[/^scroll-ma?()-?(.+)$/,L("scroll-margin"),{autocomplete:["scroll-(m|p|ma|pa|block|inline)","scroll-(m|p|ma|pa|block|inline)-$spacing","scroll-(m|p|ma|pa|block|inline)-(x|y|r|l|t|b|bs|be|is|ie)","scroll-(m|p|ma|pa|block|inline)-(x|y|r|l|t|b|bs|be|is|ie)-$spacing"]}],[/^scroll-m-?([xy])-?(.+)$/,L("scroll-margin")],[/^scroll-m-?([rltb])-?(.+)$/,L("scroll-margin")],[/^scroll-m-(block|inline)-(.+)$/,L("scroll-margin")],[/^scroll-m-?([bi][se])-?(.+)$/,L("scroll-margin")],[/^scroll-pa?()-?(.+)$/,L("scroll-padding")],[/^scroll-p-?([xy])-?(.+)$/,L("scroll-padding")],[/^scroll-p-?([rltb])-?(.+)$/,L("scroll-padding")],[/^scroll-p-(block|inline)-(.+)$/,L("scroll-padding")],[/^scroll-p-?([bi][se])-?(.+)$/,L("scroll-padding")]];var Gc=[[/^space-([xy])-(.+)$/,Kc,{autocomplete:["space-(x|y|block|inline)","space-(x|y|block|inline)-reverse","space-(x|y|block|inline)-$spacing"]}],[/^space-([xy])-reverse$/,([,e])=>({[`--un-space-${e}-reverse`]:1})],[/^space-(block|inline)-(.+)$/,Kc],[/^space-(block|inline)-reverse$/,([,e])=>({[`--un-space-${e}-reverse`]:1})]];function Kc([,e,t],{theme:r}){let n=r.spacing?.[t||"DEFAULT"]??u.bracket.cssvar.auto.fraction.rem(t||"1");if(n!=null){n==="0"&&(n="0px");let o=Y[e].map(i=>{let s=`margin${i}`,a=i.endsWith("right")||i.endsWith("bottom")?`calc(${n} * var(--un-space-${e}-reverse))`:`calc(${n} * calc(1 - var(--un-space-${e}-reverse)))`;return[s,a]});if(o)return[[`--un-space-${e}-reverse`,0],...o]}}var Hc=[["uppercase",{"text-transform":"uppercase"}],["lowercase",{"text-transform":"lowercase"}],["capitalize",{"text-transform":"capitalize"}],["normal-case",{"text-transform":"none"}]],qc=[...["manual","auto","none",...P].map(e=>[`hyphens-${e}`,{"-webkit-hyphens":e,"-ms-hyphens":e,hyphens:e}])],Yc=[["write-vertical-right",{"writing-mode":"vertical-rl"}],["write-vertical-left",{"writing-mode":"vertical-lr"}],["write-normal",{"writing-mode":"horizontal-tb"}],...w("write","writing-mode")],Xc=[["write-orient-mixed",{"text-orientation":"mixed"}],["write-orient-sideways",{"text-orientation":"sideways"}],["write-orient-upright",{"text-orientation":"upright"}],...w("write-orient","text-orientation")],Zc=[["sr-only",{position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0,0,0,0)","white-space":"nowrap","border-width":0}],["not-sr-only",{position:"static",width:"auto",height:"auto",padding:"0",margin:"0",overflow:"visible",clip:"auto","white-space":"normal"}]],Jc=[["isolate",{isolation:"isolate"}],["isolate-auto",{isolation:"auto"}],["isolation-auto",{isolation:"auto"}]],Qc=[["object-cover",{"object-fit":"cover"}],["object-contain",{"object-fit":"contain"}],["object-fill",{"object-fit":"fill"}],["object-scale-down",{"object-fit":"scale-down"}],["object-none",{"object-fit":"none"}],[/^object-(.+)$/,([,e])=>{if(G[e])return{"object-position":G[e]};if(u.bracketOfPosition(e)!=null)return{"object-position":u.bracketOfPosition(e).split(" ").map(t=>u.position.fraction.auto.px.cssvar(t)??t).join(" ")}},{autocomplete:`object-(${Object.keys(G).join("|")})`}]],el=[["bg-blend-multiply",{"background-blend-mode":"multiply"}],["bg-blend-screen",{"background-blend-mode":"screen"}],["bg-blend-overlay",{"background-blend-mode":"overlay"}],["bg-blend-darken",{"background-blend-mode":"darken"}],["bg-blend-lighten",{"background-blend-mode":"lighten"}],["bg-blend-color-dodge",{"background-blend-mode":"color-dodge"}],["bg-blend-color-burn",{"background-blend-mode":"color-burn"}],["bg-blend-hard-light",{"background-blend-mode":"hard-light"}],["bg-blend-soft-light",{"background-blend-mode":"soft-light"}],["bg-blend-difference",{"background-blend-mode":"difference"}],["bg-blend-exclusion",{"background-blend-mode":"exclusion"}],["bg-blend-hue",{"background-blend-mode":"hue"}],["bg-blend-saturation",{"background-blend-mode":"saturation"}],["bg-blend-color",{"background-blend-mode":"color"}],["bg-blend-luminosity",{"background-blend-mode":"luminosity"}],["bg-blend-normal",{"background-blend-mode":"normal"}],...w("bg-blend","background-blend")],tl=[["mix-blend-multiply",{"mix-blend-mode":"multiply"}],["mix-blend-screen",{"mix-blend-mode":"screen"}],["mix-blend-overlay",{"mix-blend-mode":"overlay"}],["mix-blend-darken",{"mix-blend-mode":"darken"}],["mix-blend-lighten",{"mix-blend-mode":"lighten"}],["mix-blend-color-dodge",{"mix-blend-mode":"color-dodge"}],["mix-blend-color-burn",{"mix-blend-mode":"color-burn"}],["mix-blend-hard-light",{"mix-blend-mode":"hard-light"}],["mix-blend-soft-light",{"mix-blend-mode":"soft-light"}],["mix-blend-difference",{"mix-blend-mode":"difference"}],["mix-blend-exclusion",{"mix-blend-mode":"exclusion"}],["mix-blend-hue",{"mix-blend-mode":"hue"}],["mix-blend-saturation",{"mix-blend-mode":"saturation"}],["mix-blend-color",{"mix-blend-mode":"color"}],["mix-blend-luminosity",{"mix-blend-mode":"luminosity"}],["mix-blend-plus-lighter",{"mix-blend-mode":"plus-lighter"}],["mix-blend-normal",{"mix-blend-mode":"normal"}],...w("mix-blend")],rl=[["min-h-dvh",{"min-height":"100dvh"}],["min-h-svh",{"min-height":"100svh"}],["min-h-lvh",{"min-height":"100lvh"}],["h-dvh",{height:"100dvh"}],["h-svh",{height:"100svh"}],["h-lvh",{height:"100lvh"}],["max-h-dvh",{"max-height":"100dvh"}],["max-h-svh",{"max-height":"100svh"}],["max-h-lvh",{"max-height":"100lvh"}]];var ho={"--un-border-spacing-x":0,"--un-border-spacing-y":0},nl={preflightKeys:Object.keys(ho)},ol="var(--un-border-spacing-x) var(--un-border-spacing-y)",il=[["inline-table",{display:"inline-table"}],["table",{display:"table"}],["table-caption",{display:"table-caption"}],["table-cell",{display:"table-cell"}],["table-column",{display:"table-column"}],["table-column-group",{display:"table-column-group"}],["table-footer-group",{display:"table-footer-group"}],["table-header-group",{display:"table-header-group"}],["table-row",{display:"table-row"}],["table-row-group",{display:"table-row-group"}],["border-collapse",{"border-collapse":"collapse"}],["border-separate",{"border-collapse":"separate"}],[/^border-spacing-(.+)$/,([,e],{theme:t})=>{let r=t.spacing?.[e]??u.bracket.cssvar.global.auto.fraction.rem(e);if(r!=null)return{"--un-border-spacing-x":r,"--un-border-spacing-y":r,"border-spacing":ol}},{custom:nl,autocomplete:["border-spacing","border-spacing-$spacing"]}],[/^border-spacing-([xy])-(.+)$/,([,e,t],{theme:r})=>{let n=r.spacing?.[t]??u.bracket.cssvar.global.auto.fraction.rem(t);if(n!=null)return{[`--un-border-spacing-${e}`]:n,"border-spacing":ol}},{custom:nl,autocomplete:["border-spacing-(x|y)","border-spacing-(x|y)-$spacing"]}],["caption-top",{"caption-side":"top"}],["caption-bottom",{"caption-side":"bottom"}],["table-auto",{"table-layout":"auto"}],["table-fixed",{"table-layout":"fixed"}],["table-empty-cells-visible",{"empty-cells":"show"}],["table-empty-cells-hidden",{"empty-cells":"hide"}]];var xo={"--un-pan-x":j,"--un-pan-y":j,"--un-pinch-zoom":j},go={preflightKeys:Object.keys(xo)},bo="var(--un-pan-x) var(--un-pan-y) var(--un-pinch-zoom)",sl=[[/^touch-pan-(x|left|right)$/,([,e])=>({"--un-pan-x":`pan-${e}`,"touch-action":bo}),{custom:go,autocomplete:["touch-pan","touch-pan-(x|left|right|y|up|down)"]}],[/^touch-pan-(y|up|down)$/,([,e])=>({"--un-pan-y":`pan-${e}`,"touch-action":bo}),{custom:go}],["touch-pinch-zoom",{"--un-pinch-zoom":"pinch-zoom","touch-action":bo},{custom:go}],["touch-auto",{"touch-action":"auto"}],["touch-manipulation",{"touch-action":"manipulation"}],["touch-none",{"touch-action":"none"}],...w("touch","touch-action")];var yo={"--un-ordinal":j,"--un-slashed-zero":j,"--un-numeric-figure":j,"--un-numeric-spacing":j,"--un-numeric-fraction":j},je={preflightKeys:Object.keys(yo)};function ze(e){return{...e,"font-variant-numeric":"var(--un-ordinal) var(--un-slashed-zero) var(--un-numeric-figure) var(--un-numeric-spacing) var(--un-numeric-fraction)"}}var al=[[/^ordinal$/,()=>ze({"--un-ordinal":"ordinal"}),{custom:je,autocomplete:"ordinal"}],[/^slashed-zero$/,()=>ze({"--un-slashed-zero":"slashed-zero"}),{custom:je,autocomplete:"slashed-zero"}],[/^lining-nums$/,()=>ze({"--un-numeric-figure":"lining-nums"}),{custom:je,autocomplete:"lining-nums"}],[/^oldstyle-nums$/,()=>ze({"--un-numeric-figure":"oldstyle-nums"}),{custom:je,autocomplete:"oldstyle-nums"}],[/^proportional-nums$/,()=>ze({"--un-numeric-spacing":"proportional-nums"}),{custom:je,autocomplete:"proportional-nums"}],[/^tabular-nums$/,()=>ze({"--un-numeric-spacing":"tabular-nums"}),{custom:je,autocomplete:"tabular-nums"}],[/^diagonal-fractions$/,()=>ze({"--un-numeric-fraction":"diagonal-fractions"}),{custom:je,autocomplete:"diagonal-fractions"}],[/^stacked-fractions$/,()=>ze({"--un-numeric-fraction":"stacked-fractions"}),{custom:je,autocomplete:"stacked-fractions"}],["normal-nums",{"font-variant-numeric":"normal"}]];var Td={"bg-blend":"background-blend-mode","bg-clip":"-webkit-background-clip","bg-gradient":"linear-gradient","bg-image":"background-image","bg-origin":"background-origin","bg-position":"background-position","bg-repeat":"background-repeat","bg-size":"background-size","mix-blend":"mix-blend-mode",object:"object-fit","object-position":"object-position",write:"writing-mode","write-orient":"text-orientation"},cl=[[/^(.+?)-(\$.+)$/,([,e,t])=>{let r=Td[e];if(r)return{[r]:u.cssvar(t)}}]];var ll=[[/^view-transition-([\w-]+)$/,([,e])=>({"view-transition-name":e})]];var ul=[cn,cl,ln,yc,bt,Zc,xt,ht,Dr,Kr,Bc,Jc,Hr,Ir,Ur,Gr,Qr,qr,mt,Zr,Xr,Lr,il,Ot,lc,gt,sl,$t,yt,Nc,pc,zr,xc,Wt,Lt,_t,Wr,Nr,Gc,Fc,Br,gc,bc,Rt,vt,St,Or,Vr,fc,Mr,en,Qc,Jr,Tr,on,Ct,Er,rn,Et,Hc,Tt,al,_r,jt,nn,sn,an,qc,Yc,Xc,mc,dc,Pr,el,tl,At,jr,zt,hc,Uc,tn,Ar,wt,kt,Dc,Fr,ll,rl,Yr].flat(1);var fl=[...$c];var $o={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"},light:{50:"#fdfdfd",100:"#fcfcfc",200:"#fafafa",300:"#f8f9fa",400:"#f6f6f6",500:"#f2f2f2",600:"#f1f3f5",700:"#e9ecef",800:"#dee2e6",900:"#dde1e3",950:"#d8dcdf"},dark:{50:"#4a4a4a",100:"#3c3c3c",200:"#323232",300:"#2d2d2d",400:"#222222",500:"#1f1f1f",600:"#1c1c1e",700:"#1b1b1b",800:"#181818",900:"#0f0f0f",950:"#080808"},get lightblue(){return this.sky},get lightBlue(){return this.sky},get warmgray(){return this.stone},get warmGray(){return this.stone},get truegray(){return this.neutral},get trueGray(){return this.neutral},get coolgray(){return this.gray},get coolGray(){return this.gray},get bluegray(){return this.slate},get blueGray(){return this.slate}};Object.values($o).forEach(e=>{typeof e!="string"&&e!==void 0&&(e.DEFAULT=e.DEFAULT||e[400],Object.keys(e).forEach(t=>{let r=+t/100;r===Math.round(r)&&(e[r]=e[t])}))});var pl={DEFAULT:"8px",0:"0",sm:"4px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},dl={DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],sm:"0 1px 1px rgb(0 0 0 / 0.05)",md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 rgb(0 0 0 / 0)"},ml={sans:["ui-sans-serif","system-ui","-apple-system","BlinkMacSystemFont",'"Segoe UI"',"Roboto",'"Helvetica Neue"',"Arial",'"Noto Sans"',"sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'].join(","),serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"].join(","),mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"].join(",")},hl={xs:["0.75rem","1rem"],sm:["0.875rem","1.25rem"],base:["1rem","1.5rem"],lg:["1.125rem","1.75rem"],xl:["1.25rem","1.75rem"],"2xl":["1.5rem","2rem"],"3xl":["1.875rem","2.25rem"],"4xl":["2.25rem","2.5rem"],"5xl":["3rem","1"],"6xl":["3.75rem","1"],"7xl":["4.5rem","1"],"8xl":["6rem","1"],"9xl":["8rem","1"]},gl={DEFAULT:"1.5rem",xs:"0.5rem",sm:"1rem",md:"1.5rem",lg:"2rem",xl:"2.5rem","2xl":"3rem","3xl":"4rem"},bl={DEFAULT:"1.5rem",none:"0",sm:"thin",md:"medium",lg:"thick"},xl={DEFAULT:["0 0 1px rgb(0 0 0 / 0.2)","0 0 1px rgb(1 0 5 / 0.1)"],none:"0 0 rgb(0 0 0 / 0)",sm:"1px 1px 3px rgb(36 37 47 / 0.25)",md:["0 1px 2px rgb(30 29 39 / 0.19)","1px 2px 4px rgb(54 64 147 / 0.18)"],lg:["3px 3px 6px rgb(0 0 0 / 0.26)","0 0 5px rgb(15 3 86 / 0.22)"],xl:["1px 1px 3px rgb(0 0 0 / 0.29)","2px 4px 7px rgb(73 64 125 / 0.35)"]},yl={none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2"},Co={tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},$l={thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},vl=Co,Ro={sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},wl={...Ro},kl={DEFAULT:"1px",none:"0"},Sl={DEFAULT:"1rem",none:"0",xs:"0.75rem",sm:"0.875rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem","7xl":"4.5rem","8xl":"6rem","9xl":"8rem"},Cl={DEFAULT:"150ms",none:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms"},Rl={DEFAULT:"0.25rem",none:"0",sm:"0.125rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},El={DEFAULT:["var(--un-shadow-inset) 0 1px 3px 0 rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 1px 2px -1px rgb(0 0 0 / 0.1)"],none:"0 0 rgb(0 0 0 / 0)",sm:"var(--un-shadow-inset) 0 1px 2px 0 rgb(0 0 0 / 0.05)",md:["var(--un-shadow-inset) 0 4px 6px -1px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 2px 4px -2px rgb(0 0 0 / 0.1)"],lg:["var(--un-shadow-inset) 0 10px 15px -3px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 4px 6px -4px rgb(0 0 0 / 0.1)"],xl:["var(--un-shadow-inset) 0 20px 25px -5px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 8px 10px -6px rgb(0 0 0 / 0.1)"],"2xl":"var(--un-shadow-inset) 0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)"},Tl={DEFAULT:"3px",none:"0"},jl={auto:"auto"},zl={mouse:"(hover) and (pointer: fine)"},Al={...He,...Ge,...Ke},xe={xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",prose:"65ch"},Ol={auto:"auto",...xe,screen:"100vw"},vo={none:"none",...xe,screen:"100vw"},Pl={auto:"auto",...xe,screen:"100vb"},Vl={auto:"auto",...xe,screen:"100vi"},Ml={auto:"auto",...xe,screen:"100vh"},wo={none:"none",...xe,screen:"100vh"},ko={none:"none",...xe,screen:"100vb"},So={none:"none",...xe,screen:"100vi"},Fl={...xe},jd={DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},zd={none:"none",all:"all",colors:["color","background-color","border-color","text-decoration-color","fill","stroke"].join(","),opacity:"opacity",shadow:"box-shadow",transform:"transform",get DEFAULT(){return[this.colors,"opacity","box-shadow","transform","filter","backdrop-filter"].join(",")}},Eo={width:Ol,height:Ml,maxWidth:vo,maxHeight:wo,minWidth:vo,minHeight:wo,inlineSize:Vl,blockSize:Pl,maxInlineSize:So,maxBlockSize:ko,minInlineSize:So,minBlockSize:ko,colors:$o,fontFamily:ml,fontSize:hl,fontWeight:$l,breakpoints:Ro,verticalBreakpoints:wl,borderRadius:Rl,lineHeight:yl,letterSpacing:Co,wordSpacing:vl,boxShadow:El,textIndent:gl,textShadow:xl,textStrokeWidth:bl,blur:pl,dropShadow:dl,easing:jd,transitionProperty:zd,lineWidth:kl,spacing:Sl,duration:Cl,ringWidth:Tl,preflightBase:Al,containers:Fl,zIndex:jl,media:zl};var _l={...Eo,aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},animation:{keyframes:{pulse:"{0%, 100% {opacity:1} 50% {opacity:.5}}",bounce:"{0%, 100% {transform:translateY(-25%);animation-timing-function:cubic-bezier(0.8,0,1,1)} 50% {transform:translateY(0);animation-timing-function:cubic-bezier(0,0,0.2,1)}}",spin:"{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}",ping:"{0%{transform:scale(1);opacity:1}75%,100%{transform:scale(2);opacity:0}}","bounce-alt":"{from,20%,53%,80%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1);transform:translate3d(0,0,0)}40%,43%{animation-timing-function:cubic-bezier(0.755,0.05,0.855,0.06);transform:translate3d(0,-30px,0)}70%{animation-timing-function:cubic-bezier(0.755,0.05,0.855,0.06);transform:translate3d(0,-15px,0)}90%{transform:translate3d(0,-4px,0)}}",flash:"{from,50%,to{opacity:1}25%,75%{opacity:0}}","pulse-alt":"{from{transform:scale3d(1,1,1)}50%{transform:scale3d(1.05,1.05,1.05)}to{transform:scale3d(1,1,1)}}","rubber-band":"{from{transform:scale3d(1,1,1)}30%{transform:scale3d(1.25,0.75,1)}40%{transform:scale3d(0.75,1.25,1)}50%{transform:scale3d(1.15,0.85,1)}65%{transform:scale3d(0.95,1.05,1)}75%{transform:scale3d(1.05,0.95,1)}to{transform:scale3d(1,1,1)}}","shake-x":"{from,to{transform:translate3d(0,0,0)}10%,30%,50%,70%,90%{transform:translate3d(-10px,0,0)}20%,40%,60%,80%{transform:translate3d(10px,0,0)}}","shake-y":"{from,to{transform:translate3d(0,0,0)}10%,30%,50%,70%,90%{transform:translate3d(0,-10px,0)}20%,40%,60%,80%{transform:translate3d(0,10px,0)}}","head-shake":"{0%{transform:translateX(0)}6.5%{transform:translateX(-6px) rotateY(-9deg)}18.5%{transform:translateX(5px) rotateY(7deg)}31.5%{transform:translateX(-3px) rotateY(-5deg)}43.5%{transform:translateX(2px) rotateY(3deg)}50%{transform:translateX(0)}}",swing:"{20%{transform:rotate3d(0,0,1,15deg)}40%{transform:rotate3d(0,0,1,-10deg)}60%{transform:rotate3d(0,0,1,5deg)}80%{transform:rotate3d(0,0,1,-5deg)}to{transform:rotate3d(0,0,1,0deg)}}",tada:"{from{transform:scale3d(1,1,1)}10%,20%{transform:scale3d(0.9,0.9,0.9) rotate3d(0,0,1,-3deg)}30%,50%,70%,90%{transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg)}40%,60%,80%{transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg)}to{transform:scale3d(1,1,1)}}",wobble:"{from{transform:translate3d(0,0,0)}15%{transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg)}30%{transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg)}45%{transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg)}60%{transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg)}75%{transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg)}to{transform:translate3d(0,0,0)}}",jello:"{from,11.1%,to{transform:translate3d(0,0,0)}22.2%{transform:skewX(-12.5deg) skewY(-12.5deg)}33.3%{transform:skewX(6.25deg) skewY(6.25deg)}44.4%{transform:skewX(-3.125deg)skewY(-3.125deg)}55.5%{transform:skewX(1.5625deg) skewY(1.5625deg)}66.6%{transform:skewX(-0.78125deg) skewY(-0.78125deg)}77.7%{transform:skewX(0.390625deg) skewY(0.390625deg)}88.8%{transform:skewX(-0.1953125deg) skewY(-0.1953125deg)}}","heart-beat":"{0%{transform:scale(1)}14%{transform:scale(1.3)}28%{transform:scale(1)}42%{transform:scale(1.3)}70%{transform:scale(1)}}",hinge:"{0%{transform-origin:top left;animation-timing-function:ease-in-out}20%,60%{transform:rotate3d(0,0,1,80deg);transform-origin:top left;animation-timing-function:ease-in-out}40%,80%{transform:rotate3d(0,0,1,60deg);transform-origin:top left;animation-timing-function:ease-in-out}to{transform:translate3d(0,700px,0);opacity:0}}","jack-in-the-box":"{from{opacity:0;transform-origin:center bottom;transform:scale(0.1) rotate(30deg)}50%{transform:rotate(-10deg)}70%{transform:rotate(3deg)}to{transform:scale(1)}}","light-speed-in-left":"{from{opacity:0;transform:translate3d(-100%,0,0) skewX(-30deg)}60%{opacity:1;transform:skewX(20deg)}80%{transform:skewX(-5deg)}to{transform:translate3d(0,0,0)}}","light-speed-in-right":"{from{opacity:0;transform:translate3d(100%,0,0) skewX(-30deg)}60%{opacity:1;transform:skewX(20deg)}80%{transform:skewX(-5deg)}to{transform:translate3d(0,0,0)}}","light-speed-out-left":"{from{opacity:1}to{opacity:0;transform:translate3d(-100%,0,0) skewX(30deg)}}","light-speed-out-right":"{from{opacity:1}to{opacity:0;transform:translate3d(100%,0,0) skewX(30deg)}}",flip:"{from{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,0) rotate3d(0,1,0,-360deg);animation-timing-function:ease-out}40%{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,150px) rotate3d(0,1,0,-190deg);animation-timing-function:ease-out}50%{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,150px) rotate3d(0,1,0,-170deg);animation-timing-function:ease-in}80%{transform:perspective(400px) scale3d(0.95,0.95,0.95) translate3d(0,0,0) rotate3d(0,1,0,0deg);animation-timing-function:ease-in}to{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,0) rotate3d(0,1,0,0deg);animation-timing-function:ease-in}}","flip-in-x":"{from{transform:perspective(400px) rotate3d(1,0,0,90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotate3d(1,0,0,-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotate3d(1,0,0,10deg);opacity:1}80%{transform:perspective(400px) rotate3d(1,0,0,-5deg)}to{transform:perspective(400px)}}","flip-in-y":"{from{transform:perspective(400px) rotate3d(0,1,0,90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotate3d(0,1,0,-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotate3d(0,1,0,10deg);opacity:1}80%{transform:perspective(400px) rotate3d(0,1,0,-5deg)}to{transform:perspective(400px)}}","flip-out-x":"{from{transform:perspective(400px)}30%{transform:perspective(400px) rotate3d(1,0,0,-20deg);opacity:1}to{transform:perspective(400px) rotate3d(1,0,0,90deg);opacity:0}}","flip-out-y":"{from{transform:perspective(400px)}30%{transform:perspective(400px) rotate3d(0,1,0,-15deg);opacity:1}to{transform:perspective(400px) rotate3d(0,1,0,90deg);opacity:0}}","rotate-in":"{from{transform-origin:center;transform:rotate3d(0,0,1,-200deg);opacity:0}to{transform-origin:center;transform:translate3d(0,0,0);opacity:1}}","rotate-in-down-left":"{from{transform-origin:left bottom;transform:rotate3d(0,0,1,-45deg);opacity:0}to{transform-origin:left bottom;transform:translate3d(0,0,0);opacity:1}}","rotate-in-down-right":"{from{transform-origin:right bottom;transform:rotate3d(0,0,1,45deg);opacity:0}to{transform-origin:right bottom;transform:translate3d(0,0,0);opacity:1}}","rotate-in-up-left":"{from{transform-origin:left top;transform:rotate3d(0,0,1,45deg);opacity:0}to{transform-origin:left top;transform:translate3d(0,0,0);opacity:1}}","rotate-in-up-right":"{from{transform-origin:right bottom;transform:rotate3d(0,0,1,-90deg);opacity:0}to{transform-origin:right bottom;transform:translate3d(0,0,0);opacity:1}}","rotate-out":"{from{transform-origin:center;opacity:1}to{transform-origin:center;transform:rotate3d(0,0,1,200deg);opacity:0}}","rotate-out-down-left":"{from{transform-origin:left bottom;opacity:1}to{transform-origin:left bottom;transform:rotate3d(0,0,1,45deg);opacity:0}}","rotate-out-down-right":"{from{transform-origin:right bottom;opacity:1}to{transform-origin:right bottom;transform:rotate3d(0,0,1,-45deg);opacity:0}}","rotate-out-up-left":"{from{transform-origin:left bottom;opacity:1}to{transform-origin:left bottom;transform:rotate3d(0,0,1,-45deg);opacity:0}}","rotate-out-up-right":"{from{transform-origin:right bottom;opacity:1}to{transform-origin:left bottom;transform:rotate3d(0,0,1,90deg);opacity:0}}","roll-in":"{from{opacity:0;transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg)}to{opacity:1;transform:translate3d(0,0,0)}}","roll-out":"{from{opacity:1}to{opacity:0;transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg)}}","zoom-in":"{from{opacity:0;transform:scale3d(0.3,0.3,0.3)}50%{opacity:1}}","zoom-in-down":"{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,-1000px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,60px,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","zoom-in-left":"{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(-1000px,0,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(10px,0,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","zoom-in-right":"{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(1000px,0,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(-10px,0,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","zoom-in-up":"{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,1000px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,-60px,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","zoom-out":"{from{opacity:1}50%{opacity:0;transform:scale3d(0.3,0.3,0.3)}to{opacity:0}}","zoom-out-down":"{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,-60px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}to{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,2000px,0);transform-origin:center bottom;animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","zoom-out-left":"{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(42px,0,0)}to{opacity:0;transform:scale(0.1) translate3d(-2000px,0,0);transform-origin:left center}}","zoom-out-right":"{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(-42px,0,0)}to{opacity:0;transform:scale(0.1) translate3d(2000px,0,0);transform-origin:right center}}","zoom-out-up":"{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,60px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}to{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,-2000px,0);transform-origin:center bottom;animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","bounce-in":"{from,20%,40%,60%,80%,to{animation-timing-function:ease-in-out}0%{opacity:0;transform:scale3d(0.3,0.3,0.3)}20%{transform:scale3d(1.1,1.1,1.1)}40%{transform:scale3d(0.9,0.9,0.9)}60%{transform:scale3d(1.03,1.03,1.03);opacity:1}80%{transform:scale3d(0.97,0.97,0.97)}to{opacity:1;transform:scale3d(1,1,1)}}","bounce-in-down":"{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:translate3d(0,0,0)}}","bounce-in-left":"{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:translate3d(0,0,0)}}","bounce-in-right":"{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:translate3d(0,0,0)}}","bounce-in-up":"{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translate3d(0,0,0)}}","bounce-out":"{20%{transform:scale3d(0.9,0.9,0.9)}50%,55%{opacity:1;transform:scale3d(1.1,1.1,1.1)}to{opacity:0;transform:scale3d(0.3,0.3,0.3)}}","bounce-out-down":"{20%{transform:translate3d(0,10px,0)}40%,45%{opacity:1;transform:translate3d(0,-20px,0)}to{opacity:0;transform:translate3d(0,2000px,0)}}","bounce-out-left":"{20%{opacity:1;transform:translate3d(20px,0,0)}to{opacity:0;transform:translate3d(-2000px,0,0)}}","bounce-out-right":"{20%{opacity:1;transform:translate3d(-20px,0,0)}to{opacity:0;transform:translate3d(2000px,0,0)}}","bounce-out-up":"{20%{transform:translate3d(0,-10px,0)}40%,45%{opacity:1;transform:translate3d(0,20px,0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}","slide-in-down":"{from{transform:translate3d(0,-100%,0);visibility:visible}to{transform:translate3d(0,0,0)}}","slide-in-left":"{from{transform:translate3d(-100%,0,0);visibility:visible}to{transform:translate3d(0,0,0)}}","slide-in-right":"{from{transform:translate3d(100%,0,0);visibility:visible}to{transform:translate3d(0,0,0)}}","slide-in-up":"{from{transform:translate3d(0,100%,0);visibility:visible}to{transform:translate3d(0,0,0)}}","slide-out-down":"{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(0,100%,0)}}","slide-out-left":"{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(-100%,0,0)}}","slide-out-right":"{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(100%,0,0)}}","slide-out-up":"{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(0,-100%,0)}}","fade-in":"{from{opacity:0}to{opacity:1}}","fade-in-down":"{from{opacity:0;transform:translate3d(0,-100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-down-big":"{from{opacity:0;transform:translate3d(0,-2000px,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-left":"{from{opacity:0;transform:translate3d(-100%,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-left-big":"{from{opacity:0;transform:translate3d(-2000px,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-right":"{from{opacity:0;transform:translate3d(100%,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-right-big":"{from{opacity:0;transform:translate3d(2000px,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-up":"{from{opacity:0;transform:translate3d(0,100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-up-big":"{from{opacity:0;transform:translate3d(0,2000px,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-top-left":"{from{opacity:0;transform:translate3d(-100%,-100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-top-right":"{from{opacity:0;transform:translate3d(100%,-100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-bottom-left":"{from{opacity:0;transform:translate3d(-100%,100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-bottom-right":"{from{opacity:0;transform:translate3d(100%,100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-out":"{from{opacity:1}to{opacity:0}}","fade-out-down":"{from{opacity:1}to{opacity:0;transform:translate3d(0,100%,0)}}","fade-out-down-big":"{from{opacity:1}to{opacity:0;transform:translate3d(0,2000px,0)}}","fade-out-left":"{from{opacity:1}to{opacity:0;transform:translate3d(-100%,0,0)}}","fade-out-left-big":"{from{opacity:1}to{opacity:0;transform:translate3d(-2000px,0,0)}}","fade-out-right":"{from{opacity:1}to{opacity:0;transform:translate3d(100%,0,0)}}","fade-out-right-big":"{from{opacity:1}to{opacity:0;transform:translate3d(2000px,0,0)}}","fade-out-up":"{from{opacity:1}to{opacity:0;transform:translate3d(0,-100%,0)}}","fade-out-up-big":"{from{opacity:1}to{opacity:0;transform:translate3d(0,-2000px,0)}}","fade-out-top-left":"{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(-100%,-100%,0)}}","fade-out-top-right":"{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(100%,-100%,0)}}","fade-out-bottom-left":"{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(-100%,100%,0)}}","fade-out-bottom-right":"{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(100%,100%,0)}}","back-in-up":"{0%{opacity:0.7;transform:translateY(1200px) scale(0.7)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}","back-in-down":"{0%{opacity:0.7;transform:translateY(-1200px) scale(0.7)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}","back-in-right":"{0%{opacity:0.7;transform:translateX(2000px) scale(0.7)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}","back-in-left":"{0%{opacity:0.7;transform:translateX(-2000px) scale(0.7)}80%{opacity:0.7;transform:translateX(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}","back-out-up":"{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:0.7;transform:translateY(-700px) scale(0.7)}}","back-out-down":"{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:0.7;transform:translateY(700px) scale(0.7)}}","back-out-right":"{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:0.7;transform:translateX(2000px) scale(0.7)}}","back-out-left":"{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateX(-2000px) scale(0.7)}100%{opacity:0.7;transform:translateY(-700px) scale(0.7)}}"},durations:{pulse:"2s","heart-beat":"1.3s","bounce-in":"0.75s","bounce-out":"0.75s","flip-out-x":"0.75s","flip-out-y":"0.75s",hinge:"2s"},timingFns:{pulse:"cubic-bezier(0.4,0,.6,1)",ping:"cubic-bezier(0,0,.2,1)","head-shake":"ease-in-out","heart-beat":"ease-in-out","pulse-alt":"ease-in-out","light-speed-in-left":"ease-out","light-speed-in-right":"ease-out","light-speed-out-left":"ease-in","light-speed-out-right":"ease-in"},properties:{"bounce-alt":{"transform-origin":"center bottom"},jello:{"transform-origin":"center"},swing:{"transform-origin":"top center"},flip:{"backface-visibility":"visible"},"flip-in-x":{"backface-visibility":"visible !important"},"flip-in-y":{"backface-visibility":"visible !important"},"flip-out-x":{"backface-visibility":"visible !important"},"flip-out-y":{"backface-visibility":"visible !important"},"rotate-in":{"transform-origin":"center"},"rotate-in-down-left":{"transform-origin":"left bottom"},"rotate-in-down-right":{"transform-origin":"right bottom"},"rotate-in-up-left":{"transform-origin":"left bottom"},"rotate-in-up-right":{"transform-origin":"right bottom"},"rotate-out":{"transform-origin":"center"},"rotate-out-down-left":{"transform-origin":"left bottom"},"rotate-out-down-right":{"transform-origin":"right bottom"},"rotate-out-up-left":{"transform-origin":"left bottom"},"rotate-out-up-right":{"transform-origin":"right bottom"},hinge:{"transform-origin":"top left"},"zoom-out-down":{"transform-origin":"center bottom"},"zoom-out-left":{"transform-origin":"left center"},"zoom-out-right":{"transform-origin":"right center"},"zoom-out-up":{"transform-origin":"center bottom"}},counts:{spin:"infinite",ping:"infinite",pulse:"infinite","pulse-alt":"infinite",bounce:"infinite","bounce-alt":"infinite"},category:{pulse:"Attention Seekers",bounce:"Attention Seekers",spin:"Attention Seekers",ping:"Attention Seekers","bounce-alt":"Attention Seekers",flash:"Attention Seekers","pulse-alt":"Attention Seekers","rubber-band":"Attention Seekers","shake-x":"Attention Seekers","shake-y":"Attention Seekers","head-shake":"Attention Seekers",swing:"Attention Seekers",tada:"Attention Seekers",wobble:"Attention Seekers",jello:"Attention Seekers","heart-beat":"Attention Seekers",hinge:"Specials","jack-in-the-box":"Specials","light-speed-in-left":"Lightspeed","light-speed-in-right":"Lightspeed","light-speed-out-left":"Lightspeed","light-speed-out-right":"Lightspeed",flip:"Flippers","flip-in-x":"Flippers","flip-in-y":"Flippers","flip-out-x":"Flippers","flip-out-y":"Flippers","rotate-in":"Rotating Entrances","rotate-in-down-left":"Rotating Entrances","rotate-in-down-right":"Rotating Entrances","rotate-in-up-left":"Rotating Entrances","rotate-in-up-right":"Rotating Entrances","rotate-out":"Rotating Exits","rotate-out-down-left":"Rotating Exits","rotate-out-down-right":"Rotating Exits","rotate-out-up-left":"Rotating Exits","rotate-out-up-right":"Rotating Exits","roll-in":"Specials","roll-out":"Specials","zoom-in":"Zooming Entrances","zoom-in-down":"Zooming Entrances","zoom-in-left":"Zooming Entrances","zoom-in-right":"Zooming Entrances","zoom-in-up":"Zooming Entrances","zoom-out":"Zooming Exits","zoom-out-down":"Zooming Exits","zoom-out-left":"Zooming Exits","zoom-out-right":"Zooming Exits","zoom-out-up":"Zooming Exits","bounce-in":"Bouncing Entrances","bounce-in-down":"Bouncing Entrances","bounce-in-left":"Bouncing Entrances","bounce-in-right":"Bouncing Entrances","bounce-in-up":"Bouncing Entrances","bounce-out":"Bouncing Exits","bounce-out-down":"Bouncing Exits","bounce-out-left":"Bouncing Exits","bounce-out-right":"Bouncing Exits","bounce-out-up":"Bouncing Exits","slide-in-down":"Sliding Entrances","slide-in-left":"Sliding Entrances","slide-in-right":"Sliding Entrances","slide-in-up":"Sliding Entrances","slide-out-down":"Sliding Exits","slide-out-left":"Sliding Exits","slide-out-right":"Sliding Exits","slide-out-up":"Sliding Exits","fade-in":"Fading Entrances","fade-in-down":"Fading Entrances","fade-in-down-big":"Fading Entrances","fade-in-left":"Fading Entrances","fade-in-left-big":"Fading Entrances","fade-in-right":"Fading Entrances","fade-in-right-big":"Fading Entrances","fade-in-up":"Fading Entrances","fade-in-up-big":"Fading Entrances","fade-in-top-left":"Fading Entrances","fade-in-top-right":"Fading Entrances","fade-in-bottom-left":"Fading Entrances","fade-in-bottom-right":"Fading Entrances","fade-out":"Fading Exits","fade-out-down":"Fading Exits","fade-out-down-big":"Fading Exits","fade-out-left":"Fading Exits","fade-out-left-big":"Fading Exits","fade-out-right":"Fading Exits","fade-out-right-big":"Fading Exits","fade-out-up":"Fading Exits","fade-out-up-big":"Fading Exits","fade-out-top-left":"Fading Exits","fade-out-top-right":"Fading Exits","fade-out-bottom-left":"Fading Exits","fade-out-bottom-right":"Fading Exits","back-in-up":"Back Entrances","back-in-down":"Back Entrances","back-in-right":"Back Entrances","back-in-left":"Back Entrances","back-out-up":"Back Exits","back-out-down":"Back Exits","back-out-right":"Back Exits","back-out-left":"Back Exits"}},media:{portrait:"(orientation: portrait)",landscape:"(orientation: landscape)",os_dark:"(prefers-color-scheme: dark)",os_light:"(prefers-color-scheme: light)",motion_ok:"(prefers-reduced-motion: no-preference)",motion_not_ok:"(prefers-reduced-motion: reduce)",high_contrast:"(prefers-contrast: high)",low_contrast:"(prefers-contrast: low)",opacity_ok:"(prefers-reduced-transparency: no-preference)",opacity_not_ok:"(prefers-reduced-transparency: reduce)",use_data_ok:"(prefers-reduced-data: no-preference)",use_data_not_ok:"(prefers-reduced-data: reduce)",touch:"(hover: none) and (pointer: coarse)",stylus:"(hover: none) and (pointer: fine)",pointer:"(hover) and (pointer: coarse)",mouse:"(hover) and (pointer: fine)",hd_color:"(dynamic-range: high)"},supports:{grid:"(display: grid)"},preflightBase:{...He,...xo,...mo,...yo,...ho,...Ge,...Ke,...fo,...po}};var Ll=[I("svg",e=>({selector:`${e.selector} svg`}))];var Wl=[I(".dark",e=>({prefix:`.dark $$ ${e.prefix}`})),I(".light",e=>({prefix:`.light $$ ${e.prefix}`})),N("@dark","@media (prefers-color-scheme: dark)"),N("@light","@media (prefers-color-scheme: light)")];var Nl={name:"aria",match(e,t){let r=F("aria-",e,t.generator.config.separators);if(r){let[n,o]=r,i=u.bracket(n)??t.theme.aria?.[n]??"";if(i)return{matcher:o,selector:s=>`${s}[aria-${i}]`}}}};function dn(e){return{name:`${e}-aria`,match(t,r){let n=F(`${e}-aria-`,t,r.generator.config.separators);if(n){let[o,i]=n,s=u.bracket(o)??r.theme.aria?.[o]??"";if(s)return{matcher:`${e}-[[aria-${s}]]:${i}`}}}}}var Kl=[dn("group"),dn("peer"),dn("parent"),dn("previous")];function To(e){let t=e.match(/^-?\d+\.?\d*/)?.[0]||"",r=e.slice(t.length);if(r==="px"){let n=Number.parseFloat(t)-.1;return Number.isNaN(n)?e:`${n}${r}`}return`calc(${e} - 0.1px)`}var Ul=/(max|min)-\[([^\]]*)\]:/;function Gl(){let e={};return{name:"breakpoints",match(t,r){if(Ul.test(t)){let o=t.match(Ul);return{matcher:t.replace(o[0],""),handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@media (${o[1]}-width: ${o[2]})`})}}let n=(Ee(r)??[]).map(({point:o,size:i},s)=>[o,i,s]);for(let[o,i,s]of n){e[o]||(e[o]=new RegExp(`^((?:([al]t-|[<~]|max-))?${o}(?:${r.generator.config.separators.join("|")}))`));let a=t.match(e[o]);if(!a)continue;let[,c]=a,l=t.slice(c.length);if(l==="container")continue;let p=c.startsWith("lt-")||c.startsWith("<")||c.startsWith("max-"),d=c.startsWith("at-")||c.startsWith("~"),h=3e3;return p?(h-=s+1,{matcher:l,handle:(m,g)=>g({...m,parent:`${m.parent?`${m.parent} $$ `:""}@media (max-width: ${To(i)})`,parentOrder:h})}):(h+=s+1,d&&s<n.length-1?{matcher:l,handle:(m,g)=>g({...m,parent:`${m.parent?`${m.parent} $$ `:""}@media (min-width: ${i}) and (max-width: ${To(n[s+1][1])})`,parentOrder:h})}:{matcher:l,handle:(m,g)=>g({...m,parent:`${m.parent?`${m.parent} $$ `:""}@media (min-width: ${i})`,parentOrder:h})})}},multiPass:!0,autocomplete:"(at-|lt-|max-|)$breakpoints:"}}var Hl=[I("*",e=>({selector:`${e.selector} > *`}))];function Ut(e,t){return{name:`combinator:${e}`,match(r,n){if(!r.startsWith(e))return;let o=n.generator.config.separators,i=ee(`${e}-`,r,o);if(!i){for(let a of o)if(r.startsWith(`${e}${a}`)){i=["",r.slice(e.length+a.length)];break}if(!i)return}let s=u.bracket(i[0])??"";return s===""&&(s="*"),{matcher:i[1],selector:a=>`${a}${t}${s}`}},multiPass:!0}}var ql=[Ut("all"," "),Ut("children",">"),Ut("next","+"),Ut("sibling","+"),Ut("siblings","~")],Yl={name:"@",match(e,t){if(e.startsWith("@container"))return;let r=F("@",e,t.generator.config.separators);if(r){let[n,o,i]=r,s=u.bracket(n),a;if(s?a=u.numberWithUnit(s):a=t.theme.containers?.[n]??"",a){let c=1e3+Object.keys(t.theme.containers??{}).indexOf(n);return i&&(c+=1e3),{matcher:o,handle:(l,p)=>p({...l,parent:`${l.parent?`${l.parent} $$ `:""}@container${i?` ${i} `:" "}(min-width: ${a})`,parentOrder:c})}}}},multiPass:!0};function Xl(e={}){if(e?.dark==="class"||typeof e.dark=="object"){let{dark:t=".dark",light:r=".light"}=typeof e.dark=="string"?{}:e.dark;return[I("dark",C(t).map(n=>o=>({prefix:`${n} $$ ${o.prefix}`}))),I("light",C(r).map(n=>o=>({prefix:`${n} $$ ${o.prefix}`})))]}return[N("dark","@media (prefers-color-scheme: dark)"),N("light","@media (prefers-color-scheme: light)")]}var Zl={name:"data",match(e,t){let r=F("data-",e,t.generator.config.separators);if(r){let[n,o]=r,i=u.bracket(n)??t.theme.data?.[n]??"";if(i)return{matcher:o,selector:s=>`${s}[data-${i}]`}}}};function mn(e){return{name:`${e}-data`,match(t,r){let n=F(`${e}-data-`,t,r.generator.config.separators);if(n){let[o,i,s]=n,a=u.bracket(o)??r.theme.data?.[o]??"";if(a)return{matcher:`${e}-[[data-${a}]]${s?`/${s}`:""}:${i}`}}}}}var Jl=[mn("group"),mn("peer"),mn("parent"),mn("previous")],Ql=[I("rtl",e=>({prefix:`[dir="rtl"] $$ ${e.prefix}`})),I("ltr",e=>({prefix:`[dir="ltr"] $$ ${e.prefix}`}))];function eu(){let e;return{name:"important",match(t,r){e||(e=new RegExp(`^(important(?:${r.generator.config.separators.join("|")})|!)`));let n,o=t.match(e);if(o?n=t.slice(o[0].length):t.endsWith("!")&&(n=t.slice(0,-1)),n)return{matcher:n,body:i=>(i.forEach(s=>{s[1]!=null&&(s[1]+=" !important")}),i)}}}}var tu=N("print","@media print"),ru={name:"media",match(e,t){let r=F("media-",e,t.generator.config.separators);if(r){let[n,o]=r,i=u.bracket(n)??"";if(i===""&&(i=t.theme.media?.[n]??""),i)return{matcher:o,handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@media ${i}`})}}},multiPass:!0},nu={name:"selector",match(e,t){let r=ee("selector-",e,t.generator.config.separators);if(r){let[n,o]=r,i=u.bracket(n);if(i)return{matcher:o,selector:()=>i}}}},ou={name:"layer",match(e,t){let r=F("layer-",e,t.generator.config.separators);if(r){let[n,o]=r,i=u.bracket(n)??n;if(i)return{matcher:o,handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@layer ${i}`})}}}},iu={name:"uno-layer",match(e,t){let r=F("uno-layer-",e,t.generator.config.separators);if(r){let[n,o]=r,i=u.bracket(n)??n;if(i)return{matcher:o,layer:i}}}},su={name:"scope",match(e,t){let r=ee("scope-",e,t.generator.config.separators);if(r){let[n,o]=r,i=u.bracket(n);if(i)return{matcher:o,selector:s=>`${i} $$ ${s}`}}}},au={name:"variables",match(e,t){if(!e.startsWith("["))return;let[r,n]=ye(e,"[","]")??[];if(!(r&&n))return;let o;for(let a of t.generator.config.separators)if(n.startsWith(a)){o=n.slice(a.length);break}if(o==null)return;let i=u.bracket(r)??"",s=i.startsWith("@");if(s||i.includes("&"))return{matcher:o,handle(a,c){let l=s?{parent:`${a.parent?`${a.parent} $$ `:""}${i}`}:{selector:i.replace(/&/g,a.selector)};return c({...a,...l})}}},multiPass:!0},cu={name:"theme-variables",match(e,t){if(rr(e))return{matcher:e,handle(r,n){return n({...r,entries:JSON.parse(nr(JSON.stringify(r.entries),t.theme))})}}}},lu=/^-?[0-9.]+(?:[a-z]+|%)?$/,uu=/-?[0-9.]+(?:[a-z]+|%)?/,Ad=[/\b(opacity|color|flex|backdrop-filter|^filter|transform)\b/];function Od(e){let t=e.match(pt)||e.match($r);if(t){let[r,n]=ue(`(${t[2]})${t[3]}`,"(",")"," ")??[];if(r)return`calc(${t[1]}${r} * -1)${n?` ${n}`:""}`}}var Pd=/\b(hue-rotate)\s*(\(.*)/;function Vd(e){let t=e.match(Pd);if(t){let[r,n]=ue(t[2],"(",")"," ")??[];if(r){let o=lu.test(r.slice(1,-1))?r.replace(uu,i=>i.startsWith("-")?i.slice(1):`-${i}`):`(calc(${r} * -1))`;return`${t[1]}${o}${n?` ${n}`:""}`}}}var fu={name:"negative",match(e){if(e.startsWith("-"))return{matcher:e.slice(1),body:t=>{if(t.find(n=>n[0]===ro))return;let r=!1;return t.forEach(n=>{let o=n[1]?.toString();if(!o||o==="0"||Ad.some(a=>a.test(n[0])))return;let i=Od(o);if(i){n[1]=i,r=!0;return}let s=Vd(o);if(s){n[1]=s,r=!0;return}lu.test(o)&&(n[1]=o.replace(uu,a=>a.startsWith("-")?a.slice(1):`-${a}`),r=!0)}),r?t:[]}}}},Ye=Object.fromEntries([["first-letter","::first-letter"],["first-line","::first-line"],"any-link","link","visited","target",["open","[open]"],"default","checked","indeterminate","placeholder-shown","autofill","optional","required","valid","invalid","user-valid","user-invalid","in-range","out-of-range","read-only","read-write","empty","focus-within","hover","focus","focus-visible","active","enabled","disabled","popover-open","root","empty",["even-of-type",":nth-of-type(even)"],["even",":nth-child(even)"],["odd-of-type",":nth-of-type(odd)"],["odd",":nth-child(odd)"],"first-of-type",["first",":first-child"],"last-of-type",["last",":last-child"],"only-child","only-of-type",["backdrop-element","::backdrop"],["placeholder","::placeholder"],["before","::before"],["after","::after"],["file","::file-selector-button"]].map(e=>Array.isArray(e)?e:[e,`:${e}`])),pu=Object.keys(Ye),Xe=Object.fromEntries([["backdrop","::backdrop"]].map(e=>Array.isArray(e)?e:[e,`:${e}`])),du=Object.keys(Xe),Md=["not","is","where","has"],mu=Object.fromEntries([["selection",["::selection"," *::selection"]],["marker",["::marker"," *::marker"]]]),jo=Object.entries(Ye).filter(([,e])=>!e.startsWith("::")).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|"),zo=Object.entries(Xe).filter(([,e])=>!e.startsWith("::")).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|"),_e=Md.join("|"),Bl=Object.keys(mu).sort((e,t)=>t.length-e.length).join("|");function Fd(e,t,r){let n=new RegExp(`^(${le(t)}:)(\\S+)${le(r)}\\1`),o,i,s,a,c=d=>{let h=ee(`${e}-`,d,[]);if(!h)return;let[m,g]=h,b=u.bracket(m);if(b==null)return;let $=g.split(o,1)?.[0]??"",S=`${t}${Q($)}`;return[$,d.slice(d.length-(g.length-$.length-1)),b.includes("&")?b.replace(/&/g,S):`${S}${b}`]},l=d=>{let h=d.match(i)||d.match(s);if(!h)return;let[m,g,b]=h,$=h[3]??"",S=Ye[b]||Xe[b]||`:${b}`;return g&&(S=`:${g}(${S})`),[$,d.slice(m.length),`${t}${Q($)}${S}`,b]},p=d=>{let h=d.match(a);if(!h)return;let[m,g,b]=h,$=h[3]??"",S=`:${g}(${b})`;return[$,d.slice(m.length),`${t}${Q($)}${S}`]};return{name:`pseudo:${e}`,match(d,h){if(o&&i&&s||(o=new RegExp(`(?:${h.generator.config.separators.join("|")})`),i=new RegExp(`^${e}-(?:(?:(${_e})-)?(${jo}))(?:(/\\w+))?(?:${h.generator.config.separators.join("|")})`),s=new RegExp(`^${e}-(?:(?:(${_e})-)?(${zo}))(?:(/\\w+))?(?:${h.generator.config.separators.filter(v=>v!=="-").join("|")})`),a=new RegExp(`^${e}-(?:(${_e})-)?\\[(.+)\\](?:(/\\w+))?(?:${h.generator.config.separators.filter(v=>v!=="-").join("|")})`)),!d.startsWith(e))return;let m=c(d)||l(d)||p(d);if(!m)return;let[g,b,$,S=""]=m;return{matcher:b,handle:(v,E)=>E({...v,prefix:`${$}${r}${v.prefix}`.replace(n,"$1$2:"),sort:pu.indexOf(S)??du.indexOf(S)})}},multiPass:!0}}var _d=["::-webkit-resizer","::-webkit-scrollbar","::-webkit-scrollbar-button","::-webkit-scrollbar-corner","::-webkit-scrollbar-thumb","::-webkit-scrollbar-track","::-webkit-scrollbar-track-piece","::file-selector-button"],Dl=Object.entries(Ye).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|"),Il=Object.entries(Xe).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|");function hu(){let e,t,r;return[{name:"pseudo",match(n,o){e&&t||(e=new RegExp(`^(${Dl})(?:${o.generator.config.separators.join("|")})`),t=new RegExp(`^(${Il})(?:${o.generator.config.separators.filter(s=>s!=="-").join("|")})`));let i=n.match(e)||n.match(t);if(i){let s=Ye[i[1]]||Xe[i[1]]||`:${i[1]}`,a=pu.indexOf(i[1]);return a===-1&&(a=du.indexOf(i[1])),a===-1&&(a=void 0),{matcher:n.slice(i[0].length),handle:(c,l)=>{let p=s.includes("::")&&!_d.includes(s)?{pseudo:`${c.pseudo}${s}`}:{selector:`${c.selector}${s}`};return l({...c,...p,sort:a,noMerge:!0})}}}},multiPass:!0,autocomplete:`(${Dl}|${Il}):`},{name:"pseudo:multi",match(n,o){r||(r=new RegExp(`^(${Bl})(?:${o.generator.config.separators.join("|")})`));let i=n.match(r);if(i)return mu[i[1]].map(a=>({matcher:n.slice(i[0].length),handle:(c,l)=>l({...c,pseudo:`${c.pseudo}${a}`})}))},multiPass:!1,autocomplete:`(${Bl}):`}]}function gu(){let e,t,r;return{match(n,o){e&&t||(e=new RegExp(`^(${_e})-(${jo})(?:${o.generator.config.separators.join("|")})`),t=new RegExp(`^(${_e})-(${zo})(?:${o.generator.config.separators.filter(s=>s!=="-").join("|")})`),r=new RegExp(`^(${_e})-(\\[.+\\])(?:${o.generator.config.separators.filter(s=>s!=="-").join("|")})`));let i=n.match(e)||n.match(t)||n.match(r);if(i){let s=i[1],c=ye(i[2],"[","]")?u.bracket(i[2]):Ye[i[2]]||Xe[i[2]]||`:${i[2]}`;return{matcher:n.slice(i[0].length),selector:l=>`${l}:${s}(${c})`}}},multiPass:!0,autocomplete:`(${_e})-(${jo}|${zo}):`}}function bu(e={}){let t=!!e?.attributifyPseudo,r=e?.prefix??"";r=(Array.isArray(r)?r:[r]).filter(Boolean)[0]??"";let n=(o,i)=>Fd(o,t?`[${r}${o}=""]`:`.${r}${o}`,i);return[n("group"," "),n("peer","~"),n("parent",">"),n("previous","+"),n("group-aria"," "),n("peer-aria","~"),n("parent-aria",">"),n("previous-aria","+")]}var Ld=/(part-\[(.+)\]:)(.+)/,xu={match(e){let t=e.match(Ld);if(t){let r=`part(${t[2]})`;return{matcher:e.slice(t[1].length),selector:n=>`${n}::${r}`}}},multiPass:!0},yu={name:"starting",match(e){if(e.startsWith("starting:"))return{matcher:e.slice(9),handle:(t,r)=>r({...t,parent:"@starting-style"})}}},$u={name:"supports",match(e,t){let r=F("supports-",e,t.generator.config.separators);if(r){let[n,o]=r,i=u.bracket(n)??"";if(i===""&&(i=t.theme.supports?.[n]??""),i)return{matcher:o,handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@supports ${i}`})}}},multiPass:!0};function Ao(e){return[Nl,Zl,ou,nu,iu,fu,yu,eu(),$u,tu,ru,Gl(),...ql,...hu(),gu(),...bu(e),xu,...Xl(e),...Ql,su,...Hl,Yl,au,...Jl,...Kl,cu]}var vu=[N("contrast-more","@media (prefers-contrast: more)"),N("contrast-less","@media (prefers-contrast: less)")],wu=[N("motion-reduce","@media (prefers-reduced-motion: reduce)"),N("motion-safe","@media (prefers-reduced-motion: no-preference)")],ku=[N("landscape","@media (orientation: landscape)"),N("portrait","@media (orientation: portrait)")];var Su=e=>{if(!e.startsWith("_")&&(/space-[xy]-.+$/.test(e)||/divide-/.test(e)))return{matcher:e,selector:t=>{let r=">:not([hidden])~:not([hidden])";return t.includes(r)?t:`${t}${r}`}}},Cu=[I("@hover",e=>({parent:`${e.parent?`${e.parent} $$ `:""}@media (hover: hover) and (pointer: fine)`,selector:`${e.selector||""}:hover`}))];function Ru(e,t,r){return`calc(${t} + (${e} - ${t}) * ${r} / 100)`}function Eu(e,t,r){let n=[e,t],o=[];for(let s=0;s<2;s++){let a=typeof n[s]=="string"?q(n[s]):n[s];if(!a||!["rgb","rgba"].includes(a.type))return;o.push(a)}let i=[];for(let s=0;s<3;s++)i.push(Ru(o[0].components[s],o[1].components[s],r));return{type:"rgb",components:i,alpha:Ru(o[0].alpha??1,o[1].alpha??1,r)}}function Tu(e,t){return Eu("#fff",e,t)}function ju(e,t){return Eu("#000",e,t)}function Wd(e,t){let r=Number.parseFloat(`${t}`);if(!Number.isNaN(r))return r>0?ju(e,t):Tu(e,-r)}var Ud={tint:Tu,shade:ju,shift:Wd};function zu(){let e;return{name:"mix",match(t,r){e||(e=new RegExp(`^mix-(tint|shade|shift)-(-?\\d{1,3})(?:${r.generator.config.separators.join("|")})`));let n=t.match(e);if(n)return{matcher:t.slice(n[0].length),body:o=>(o.forEach(i=>{if(i[1]){let s=q(`${i[1]}`);if(s){let a=Ud[n[1]](s,n[2]);a&&(i[1]=A(a))}}}),o)}}}}var Au=(e,{theme:t})=>{let r=e.match(/^(.*)\b(placeholder-)(.+)$/);if(r){let[,n="",o,i]=r;if(Me(i,t,"accentColor")||Bd(i))return{matcher:`${n}placeholder-$ ${o}${i}`}}};function Bd(e){let t=e.match(/^op(?:acity)?-?(.+)$/);return t&&t[1]!=null?u.bracket.percent(t[1])!=null:!1}function Ou(e){return[Au,Su,...Ao(e),...vu,...ku,...wu,...Ll,...Wl,...Cu,zu()]}var Pu=(e={})=>(e.important=e.important??!1,{...eo(e),name:"@unocss/preset-wind3",theme:_l,rules:ul,shortcuts:fl,variants:Ou(e),postprocess:Xa(e)});var Dd=(e={})=>({...Pu(e),name:"@unocss/preset-uno"}),Vu=Dd;function Id(e){return e.replace(/-(\w)/g,(t,r)=>r?r.toUpperCase():"")}function Mu(e){return e.charAt(0).toUpperCase()+e.slice(1)}function Fu(e){return e.replace(/(?:^|\B)([A-Z])/g,"-$1").toLowerCase()}var _u=["Webkit","Moz","ms"];function Lu(e){let t={};function r(n){let o=t[n];if(o)return o;let i=Id(n);if(i!=="filter"&&i in e)return t[n]=Fu(i);i=Mu(i);for(let s=0;s<_u.length;s++){let a=`${_u[s]}${i}`;if(a in e)return t[n]=Fu(Mu(a))}return n}return({entries:n})=>n.forEach(o=>{o[0].startsWith("--")||(o[0]=r(o[0]))})}function Wu(e){return e.replace(/&amp;/g,"&").replace(/&gt;/g,">").replace(/&lt;/g,"<")}async function Oo(e={}){if(typeof window>"u"){console.warn("@unocss/runtime been used in non-browser environment, skipped.");return}let t=window,r=window.document,n=()=>r.documentElement,o=t.__unocss||{},i=Object.assign({},e,o.runtime),s=i.defaults||{},a=i.cloakAttribute??"un-cloak";i.autoPrefix&&(s.postprocess=C(s.postprocess)).unshift(Lu(r.createElement("div").style)),i.configResolved?.(o,s);let c=await Yo(o,s),l=R=>i.inject?i.inject(R):n().prepend(R),p=()=>i.rootElement?i.rootElement():r.body,d=new Map,h=!0,m=new Set,g,b,$=[],S=()=>new Promise(R=>{$.push(R),b!=null&&clearTimeout(b),b=setTimeout(()=>H().then(()=>{let O=$;$=[],O.forEach(W=>W())}),0)});function v(R,O=!1){if(R.nodeType!==1)return;let W=R;W.hasAttribute(a)&&W.removeAttribute(a),O&&W.querySelectorAll(`[${a}]`).forEach(D=>{D.removeAttribute(a)})}function E(R,O){let W=d.get(R);if(!W)if(W=r.createElement("style"),W.setAttribute("data-unocss-runtime-layer",R),d.set(R,W),O==null)l(W);else{let D=E(O),z=D.parentNode;z?z.insertBefore(W,D.nextSibling):l(W)}return W}async function H(){let R=[...m],O=await c.generate(R);return O.layers.reduce((D,z)=>(E(z,D).innerHTML=O.getLayer(z)??"",z),void 0),R.filter(D=>!O.matched.has(D)).forEach(D=>m.delete(D)),{...O,getStyleElement:D=>d.get(D),getStyleElements:()=>d}}async function V(R){let O=m.size;await c.applyExtractors(R,void 0,m),O!==m.size&&await S()}async function y(R=p()){let O=R&&R.outerHTML;O&&(await V(`${O} ${Wu(O)}`),v(n()),v(R,!0))}let x=new MutationObserver(R=>{h||R.forEach(async O=>{if(O.target.nodeType!==1)return;let W=O.target;for(let D of d)if(W===D[1])return;if(O.type==="childList")O.addedNodes.forEach(async D=>{if(D.nodeType!==1)return;let z=D;g&&!g(z)||(await V(z.outerHTML),v(z))});else{if(g&&!g(W))return;if(O.attributeName!==a){let D=Array.from(W.attributes).map(X=>X.value?`${X.name}="${X.value}"`:X.name).join(" "),z=`<${W.tagName.toLowerCase()} ${D}>`;await V(z)}v(W)}})}),k=!1;function T(){if(k)return;let R=i.observer?.target?i.observer.target():p();R&&(x.observe(R,{childList:!0,subtree:!0,attributes:!0,attributeFilter:i.observer?.attributeFilter}),k=!0)}function Z(){i.bypassDefined&&Nd(c.blocked),y(),T()}function J(){r.readyState==="loading"?t.addEventListener("DOMContentLoaded",Z):Z()}let ce=t.__unocss_runtime=t.__unocss_runtime={version:c.version,uno:c,async extract(R){M(R)||(R.forEach(O=>m.add(O)),R=""),await V(R)},extractAll:y,inspect(R){g=R},toggleObserver(R){R===void 0?h=!h:h=!!R,!k&&!h&&J()},update:H,presets:t.__unocss_runtime?.presets??{}};i.ready?.(ce)!==!1&&(h=!1,J())}function Nd(e=new Set){for(let t=0;t<document.styleSheets.length;t++){let r=document.styleSheets[t],n;try{if(n=r.cssRules||r.rules,!n)continue;Array.from(n).flatMap(o=>o.selectorText?.split(/,/g)||[]).forEach(o=>{o&&(o=o.trim(),o.startsWith(".")&&(o=o.slice(1)),e.add(o))})}catch{continue}}return e}Oo({defaults:{presets:[Vu()]}});})();
