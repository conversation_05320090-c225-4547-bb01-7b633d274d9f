var baseUrl = localStorage.getItem("fdb-domainName") + "/index.php?s=";

const navigationArr = [
  {
    navigation: "添加预约",
    id: 0,
    isTab: true,
    findArr: [
      {
        navigation: "预约上门",
        id: 1,
      },
      {
        navigation: "预约到店",
        id: 2,
      },
    ],
  },
  {
    navigation: "查看预约",
    id: 3,
    isTab: false,
  },
];

var tableData = [];

var reservation = new Vue({
  el: "#reservation",
  data() {
    return {
      loading: false,
      orderNo: 0,
      url: baseUrl,
      loginInfo: {},
      bookerdoor: 1,
      tabIndex: 0,
      findIndex: 1,
      getAllserState: 1, //getAllser的state的值
      navigationArr: navigationArr,
      //预约表单
      formReservation: {
        phone: "",
        people: "",
        time: "",
        remarks: "",
        address: "",
      },
      // 预约-会员信息
      memberInfo: [],

      selectService: [
        {
          server: "添加服务",
          skuName: "",
          serverPrice: 0,
          technician: "",
          serverid: 0,
          sertime: 0,
          skuid: 0,
          staffid: 0,
        },
      ],
      curSelectService: 0,
      // 添加服务
      isServer: false,
      serverListArr: [],
      // 选择服务人员
      isTechnician: false,
      technicianArr: [],
      //选择规格
      isSku: false,
      skuArr: [],
      skuTitle: "所有标签",
      skuCategory: "",
      tempServer: {},

      subServer: [],
      subServerArr: [], //可能丢弃

      // serverId: '',
      // serviceDuration: '',
      // skuid: 0,
      // staffid: '',

      /*查看预约*/
      tabCur: 6,
      tableData: tableData,
      limit: 10,
      currentPage: 1,
      allCount: 0,
      reservationCall: "",

      isDetails: false,
      detailsTable: {},

      //取消预约
      isCancelReservation: false,
      curReason: {},
      reasonIndex: "",
      cancelReason: [
        {
          label: "客户想换个时间",
          id: 0,
        },
        {
          label: "客户暂时不想要了",
          id: 1,
        },
        {
          label: "门店暂时无法提供该服务",
          id: 2,
        },
        {
          label: "没有合适的" + globalTechnicianName + "提供服务",
          id: 3,
        },
        {
          label: "其他原因",
          id: 4,
        },
      ],
      //修改预约
      changeTechnicianIndex: 0,

      // 服务开单
      billingInfo: [],
      //新添一个phone 来保存预约走向开单时候传递的会员phone
      order_phone: 0,
      seeFlag: true,
      isChooseTime: false,
      dayLabel: [],
      chooseTimeU: "reservation-choose-time-ul-li choose-time-active",
      chooseTimeUli: "reservation-choose-time-ul-li",
      chooseTimeUliStyle: false,

      //1：正常 2：选中 3：过期 4：已被选过
      timeLabel1: [
        { id: 0, time: "00:00", isactive: 1 },
        { id: 1, time: "00:15", isactive: 1 },
        { id: 2, time: "00:30", isactive: 1 },
        { id: 3, time: "00:45", isactive: 1 },
        { id: 4, time: "01:00", isactive: 1 },
        { id: 5, time: "01:15", isactive: 1 },
        { id: 6, time: "01:30", isactive: 1 },
        { id: 7, time: "01:45", isactive: 1 },
        { id: 8, time: "02:00", isactive: 1 },
        { id: 9, time: "02:15", isactive: 1 },
        { id: 10, time: "02:30", isactive: 1 },
        { id: 11, time: "02:45", isactive: 1 },
        { id: 12, time: "03:00", isactive: 1 },
        { id: 13, time: "03:15", isactive: 1 },
        { id: 14, time: "03:30", isactive: 1 },
        { id: 15, time: "03:45", isactive: 1 },
        { id: 16, time: "04:00", isactive: 1 },
        { id: 17, time: "04:15", isactive: 1 },
        { id: 18, time: "04:30", isactive: 1 },
        { id: 19, time: "04:45", isactive: 1 },
        { id: 20, time: "05:00", isactive: 1 },
        { id: 21, time: "05:15", isactive: 1 },
        { id: 22, time: "05:30", isactive: 1 },
        { id: 23, time: "05:45", isactive: 1 },
        { id: 24, time: "06:00", isactive: 1 },
        { id: 25, time: "06:15", isactive: 1 },
        { id: 26, time: "06:30", isactive: 1 },
        { id: 27, time: "06:45", isactive: 1 },
        { id: 28, time: "07:00", isactive: 1 },
        { id: 29, time: "07:15", isactive: 1 },
        { id: 30, time: "07:30", isactive: 1 },
        { id: 31, time: "07:45", isactive: 1 },
        { id: 32, time: "08:00", isactive: 1 },
        { id: 33, time: "08:15", isactive: 1 },
        { id: 34, time: "08:30", isactive: 1 },
        { id: 35, time: "08:45", isactive: 1 },
        { id: 36, time: "09:00", isactive: 1 },
        { id: 37, time: "09:15", isactive: 1 },
        { id: 38, time: "09:30", isactive: 1 },
        { id: 39, time: "09:45", isactive: 1 },
        { id: 40, time: "10:00", isactive: 1 },
        { id: 41, time: "10:15", isactive: 1 },
        { id: 42, time: "10:30", isactive: 1 },
        { id: 43, time: "10:45", isactive: 1 },
        { id: 44, time: "11:00", isactive: 1 },
        { id: 45, time: "11:15", isactive: 1 },
        { id: 46, time: "11:30", isactive: 1 },
        { id: 47, time: "11:45", isactive: 1 },
        { id: 48, time: "12:00", isactive: 1 },
        { id: 49, time: "12:15", isactive: 1 },
        { id: 50, time: "12:30", isactive: 1 },
        { id: 51, time: "12:45", isactive: 1 },
        { id: 52, time: "13:00", isactive: 1 },
        { id: 53, time: "13:15", isactive: 1 },
        { id: 54, time: "13:30", isactive: 1 },
        { id: 55, time: "13:45", isactive: 1 },
        { id: 56, time: "14:00", isactive: 1 },
        { id: 57, time: "14:15", isactive: 1 },
        { id: 58, time: "14:30", isactive: 1 },
        { id: 59, time: "14:45", isactive: 1 },
        { id: 60, time: "15:00", isactive: 1 },
        { id: 61, time: "15:15", isactive: 1 },
        { id: 62, time: "15:30", isactive: 1 },
        { id: 63, time: "15:45", isactive: 1 },
        { id: 64, time: "16:00", isactive: 1 },
        { id: 65, time: "16:15", isactive: 1 },
        { id: 66, time: "16:30", isactive: 1 },
        { id: 67, time: "16:45", isactive: 1 },
        { id: 68, time: "17:00", isactive: 1 },
        { id: 69, time: "17:15", isactive: 1 },
        { id: 70, time: "17:30", isactive: 1 },
        { id: 71, time: "17:45", isactive: 1 },
        { id: 72, time: "18:00", isactive: 1 },
        { id: 73, time: "18:15", isactive: 1 },
        { id: 74, time: "18:30", isactive: 1 },
        { id: 75, time: "18:45", isactive: 1 },
        { id: 76, time: "19:00", isactive: 1 },
        { id: 77, time: "19:15", isactive: 1 },
        { id: 78, time: "19:30", isactive: 1 },
        { id: 79, time: "19:45", isactive: 1 },
        { id: 80, time: "20:00", isactive: 1 },
        { id: 81, time: "20:15", isactive: 1 },
        { id: 82, time: "20:30", isactive: 1 },
        { id: 83, time: "20:45", isactive: 1 },
        { id: 84, time: "21:00", isactive: 1 },
        { id: 85, time: "21:15", isactive: 1 },
        { id: 86, time: "21:30", isactive: 1 },
        { id: 87, time: "21:45", isactive: 1 },
        { id: 88, time: "22:00", isactive: 1 },
        { id: 89, time: "22:15", isactive: 1 },
        { id: 90, time: "22:30", isactive: 1 },
        { id: 91, time: "22:45", isactive: 1 },
        { id: 92, time: "23:00", isactive: 1 },
        { id: 93, time: "23:15", isactive: 1 },
        { id: 94, time: "23:30", isactive: 1 },
        { id: 95, time: "23:45", isactive: 1 },
      ],
      timeLabel: [],
      formReservationTime: "",

      //服务人员 点客
      curIndex: 0,
      curTab: {},
      isGuest: false,
      technicianData: {},
    };
  },

  mounted: function () {
    this.getLoginInfo();
    this.getData();
    this.handleUrl();
  },

  computed: {
    reservationMoney: function () {
      var all = 0;
      this.selectService.forEach(function (item) {
        all += parseFloat(item.serverPrice);
      });
      return all.toFixed(2);
    },
    servermessLen: function () {
      return (servermess = this.detailsTable.servermess.length);
    },
  },

  watch: {
    reservationCall: function (n, o) {
      if (!n && o) {
        this.currentPage = 1;
        this.getallBooker();
      }
    },
  },

  methods: {
    //处理url
    handleUrl: function () {
      var url = location.search;
      if (url.indexOf("&") != -1) {
        let phone = url.split("&")[0].split("=")[1];
        this.formReservation.phone = phone;
        this.bindInquire(phone);
        //解决跳转到预约页面时，header中的选中样式还在会员的问题
        //0:收银台 1:商品 2:订单 3:会员 4:预约 5:通知
        try {
          parent.window.activeHeader(4);
        } catch (e) {

        }
      }
    },

    //格式化日期：yyyy-MM-dd
    formatDate: function (date) {
      var myyear = date.getFullYear();
      var mymonth = date.getMonth() + 1;
      var myweekday = date.getDate();

      if (mymonth < 10) {
        mymonth = "0" + mymonth;
      }
      if (myweekday < 10) {
        myweekday = "0" + myweekday;
      }
      return myyear + "-" + mymonth + "-" + myweekday;
    },

    //格式化周几
    formatWeek: function (date) {
      var week;
      if (date.getDay() == 0) week = "星期日";
      if (date.getDay() == 1) week = "星期一";
      if (date.getDay() == 2) week = "星期二";
      if (date.getDay() == 3) week = "星期三";
      if (date.getDay() == 4) week = "星期四";
      if (date.getDay() == 5) week = "星期五";
      if (date.getDay() == 6) week = "星期六";
      return week;
    },

    //选择时间
    chooseTime: function () {

      let _self = this;
      _self.dayLabel = [];
      try {
        let timeList = [];
        for (let i = 0; i < 89; i++) {
          let date = new Date();
          timeList.push(date.setDate(date.getDate() + i));
        }
        var id = 0;
        timeList.forEach((item) => {
          let time = new Date(item);
          _self.dayLabel.push({
            id: id,
            time: _self.formatDate(time),
            label: _self.formatWeek(time),
            isactive: false,
          });
          id = id + 1;
        });
        _self.dayLabel.forEach((item) => {
          if (item.id == 0) {
            item.isactive = true;
          } else {
            item.isactive = false;
          }
        });
        _self.dayLabel[0].label = "今天";
        _self.dayLabel[1].label = "明天";
        _self.dayLabel[2].label = "后天";
        _self.formReservation.time = _self.dayLabel[0].time;
        let selectedStartTime = "";
        $.ajax({
          url: _self.url + "/schedulDate",
          type: "post",
          data: {
            merchantid: _self.loginInfo.merchantid, // 商户id
            staffid: 0,
            storeid: _self.loginInfo.storeid,
            date: _self.dayLabel[0].time,
            sertime: 15,
          },
          success: function (res) {
            //isactive  1：正常 2：选中 3：过期 4：已被选过
            // var res = JSON.parse(res);
            if (res.code == 1) {
              if (res.data.today.type == 1) {
                let start = res.data.today.conven[0].match(
                  /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                )[0];
                let end = res.data.today.conven[1].match(
                  /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                )[0];
                let commonList = _self.handleTime(start, end);
                commonList.forEach((item) => {
                  item.isactive = 3;
                });
                _self.timeLabel = commonList;
                _self.isChooseTime = true;
                _self.$message({
                  type: "warning",
                  message: "该" + globalTechnicianName + "今天休息",
                  duration: 1500,
                });
              } else {
                if (res.data.clude.length != 0) {
                  let selectList = [];
                  res.data.clude.forEach((item) => {
                    selectedStartTime = item.match(
                      /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                    )[0];
                    selectList = selectList.concat(
                      _self.handleCludeTime(selectedStartTime)
                    );
                  });

                  let start = res.data.today.conven[0].match(
                    /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                  )[0];
                  let end = res.data.today.conven[1].match(
                    /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                  )[0];
                  let commonList = _self.handleTime(start, end);
                  selectList.forEach((item) => {
                    item.isactive = 4;
                    commonList.forEach((item1) => {
                      if (item.id == item1.id) {
                        item1.isactive = item.isactive;
                      }
                    });
                  });
                  let finalList = _self.handleLimitTime(commonList);
                  _self.timeLabel = finalList;
                  _self.isChooseTime = true;
                } else {
                  let start = res.data.today.conven[0].match(
                    /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                  )[0];
                  let end = res.data.today.conven[1].match(
                    /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                  )[0];
                  let commonList = _self.handleTime(start, end);
                  // console.log(commonList)
                  // _self.commonList.forEach(item=>{
                  //     item.isactive=1;
                  // })
                  let finalList = _self.handleLimitTime(commonList);
                  _self.timeLabel = finalList;
                  _self.isChooseTime = true;
                }
              }
            }
          },
        });
      } catch (e) {

      }
    },
    //处理已经过去的时间
    handleLimitTime(data) {
      var d = new Date();
      var year = d.getFullYear();
      var month = change(d.getMonth() + 1);
      var day = change(d.getDate());
      var hour = change(d.getHours());
      var minute = change(d.getMinutes());
      let existDay = "";
      let list = [];

      function change(t) {
        if (t < 10) {
          return "0" + t;
        } else {
          return t;
        }
      }

      var time = year + "-" + month + "-" + day;
      if (this.formReservation.time.indexOf(":") == -1) {
        existDay = this.formReservation.time;
      } else {
        existDay = this.formReservation.time.split(" ")[0];
      }
      if (time == existDay) {
        this.timeLabel1.forEach((item) => {
          if (item.time <= hour + ":" + minute) {
            list = JSON.parse(JSON.stringify(this.timeLabel1)).slice(
              0,
              item.id + 1
            );
          }
        });
      }
      //isactive  1：正常 2：选中 3：过期 4：已被选过
      for (let j = 0; j < list.length; j++) {
        let item = list[j];
        item.isactive = 3;
        for (let i = 0; i < data.length; i++) {
          let item1 = data[i];
          if (item.id == item1.id && item1.time >= data[0].time) {
            item1.isactive = item.isactive;
            flag = true;
            break;
          }
        }
      }
      return data;
    },
    handleCludeTime: function (time) {
      let newTimelabel = [];
      this.timeLabel1.forEach((item) => {
        if (item.time == time) {
          newTimelabel.push(item);
        }
      });
      return newTimelabel;
    },

    //处理时间点
    handleTime: function (start, end) {
      let _self = this;
      let listStart = 0;
      let listEnd = 0;
      let newTimelabel = [];
      _self.timeLabel1.forEach((item) => {
        item.isactive = 1;
        if (item.time == start) {
          listStart = item.id;
        }
        if (item.time == end) {
          listEnd = item.id;
        }
      });
      // console.log(_self.timeLabel1);
      if (parseInt(listStart) < parseInt(listEnd)) {
        newTimelabel = JSON.parse(JSON.stringify(_self.timeLabel1)).slice(
          parseInt(listStart),
          parseInt(listEnd) + 1
        );
      } else {
        let list = JSON.parse(JSON.stringify(_self.timeLabel1)).slice(
          parseInt(listStart)
        );
        let list1 = JSON.parse(JSON.stringify(_self.timeLabel1)).slice(
          0,
          parseInt(listEnd) + 1
        );
        newTimelabel = list.concat(list1);
      }
      return newTimelabel;
    },

    //绑定天数
    chooseTimeDay: function (data) {
      // console.log(11111111111111)
      // console.log(data)
      let _self = this;
      this.dayLabel.forEach((item) => {
        // debugger
        if (item.time == data.time) {
          // console.log(22222222222222222222)
          item.isactive = true;
          this.formReservation.time = item.time;
          let selectedStartTime = "";
          new Promise(function (resolve, reject) {
            $.ajax({
              url: _self.url + "/schedulDate",
              type: "post",
              data: {
                merchantid: _self.loginInfo.merchantid, // 商户id
                staffid: 0, //服务人员id
                storeid: _self.loginInfo.storeid,
                date: item.time,
                sertime: 15,
              },
              success: function (res) {
                // var res = JSON.parse(res);
                if (res.code == 1) {
                  resolve(res);
                }
              },
              error: function (e) {

                reject(e);
              },
            });
          }).then(function (res) {
            //isactive  1：正常 2：选中 3：过期 4：已被选过
            if (res.code == 1) {
              if (res.data.today.type == 1) {
                let start = res.data.today.conven[0].match(
                  /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                )[0];
                let end = res.data.today.conven[1].match(
                  /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                )[0];
                let commonList = _self.handleTime(start, end);
                commonList.forEach((item) => {
                  item.isactive = 3;
                });
                _self.timeLabel = commonList;
                _self.isChooseTime = true;
                _self.$message({
                  type: "warning",
                  message: "该" + globalTechnicianName + "今天休息",
                  duration: 1500,
                });
              } else {
                if (res.data.clude.length != 0) {
                  let selectList = [];
                  res.data.clude.forEach((item) => {
                    selectedStartTime = item.match(
                      /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                    )[0];
                    selectList = selectList.concat(
                      _self.handleCludeTime(selectedStartTime)
                    );
                  });
                  let start = res.data.today.conven[0].match(
                    /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                  )[0];
                  let end = res.data.today.conven[1].match(
                    /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                  )[0];
                  let commonList = _self.handleTime(start, end);
                  selectList.forEach((item) => {
                    item.isactive = 4;
                    commonList.forEach((item1) => {
                      if (item.id == item1.id) {
                        item1.isactive = item.isactive;
                      }
                    });
                  });
                  let finalList = _self.handleLimitTime(commonList);
                  _self.timeLabel = finalList;
                  _self.isChooseTime = true;
                } else {
                  // _self.timeLabel=[];

                  let start = res.data.today.conven[0].match(
                    /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                  )[0];
                  let end = res.data.today.conven[1].match(
                    /(([0-1]\d)|(2[0-4])):[0-5]\d/g
                  )[0];
                  let commonList = _self.handleTime(start, end);
                  // console.log(commonList);
                  let finalList = _self.handleLimitTime(commonList);
                  // console.log(finalList)
                  _self.timeLabel = finalList;
                  // _self.timeLabel.forEach(item=>{
                  //     item.isactive=1;
                  // })
                  // console.log(_self.timeLabel)
                  _self.$forceUpdate();
                  _self.isChooseTime = true;
                }
              }
            }
          });
        } else {
          item.isactive = false;
        }
      });
    },
    //获取当天的时间段或者选择的时间段
    getTimeQuantum: function (date) {
      let _self = this;
      let commonList = [];
      $.ajax({
        url: _self.url + "/schedulDate",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          staffid: 0,
          storeid: _self.loginInfo.storeid,
          date: date,
          sertime: 15,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            let start = res.data[0].match(/(([0-1]\d)|(2[0-4])):[0-5]\d/g)[0];
            let end = res.data[1].match(/(([0-1]\d)|(2[0-4])):[0-5]\d/g)[0];
            commonList = _self.handleTime(start, end);
            return commonList;
          }
        },
      });
    },

    chooseTimeLabel: function (data) {
      let _self = this;
      let date = new Date();
      let nowTime = this.formatDate(date);
      if (data.isactive == 3) {
        this.$message({
          type: "warning",
          message: "时间已经过去了",
          duration: 1500,
        });
      }
      if (data.isactive == 4) {
        this.$message({
          type: "warning",
          message: "当前时间段已经被预约",
          duration: 1500,
        });
      }
      if (data.isactive == 1 || data.isactive == 2) {
        _self.timeLabel.forEach((item) => {
          if (item.isactive == 1 || item.isactive == 2) {
            if (item.id == data.id) {
              item.isactive = 2;
              if (_self.formReservation.time.indexOf(" ") != -1) {
                _self.formReservation.time =
                  _self.formReservation.time.split(" ")[0] + " " + item.time;
              } else {
                _self.formReservation.time =
                  _self.formReservation.time + " " + item.time;
              }

              if (_self.timeLabel[0].time >= data.time) {
                if (
                  localStorage.getItem("tomorrow") !=
                  _self.formReservation.time.split(" ")[0]
                ) {
                  let id = 0;
                  _self.dayLabel.forEach((item) => {
                    if (item.time == _self.formReservation.time.split(" ")[0]) {
                      id = item.id;
                    }
                  });

                  _self.formReservation.time =
                    _self.dayLabel[id + 1].time + " " + item.time;
                  localStorage.setItem("tomorrow", _self.dayLabel[id + 1].time);
                }
              } else {

                localStorage.removeItem("tomorrow");
              }
              _self.formReservationTime = _self.formReservation.time;
              try {
                if (_self.selectService.length > 1) {
                  for (let i = 0; i < _self.selectService.length; i++) {
                    if (i != 0) {
                      _self.selectService[i].staffid = 0;
                      _self.selectService[i].technician =
                        "选择" + globalTechnicianName;
                    }
                  }
                }
              } catch (e) {

              }
              _self.isChooseTime = false;
            } else {
              item.isactive = 1;
            }
          }
        });

        if (_self.selectService) {
          _self.selectService.forEach((item) => {
            item.technician = "选择" + globalTechnicianName;
            item.staffid = 0;
          });
        }
      }
    },
    getLoginInfo: function () {
      if (localStorage.getItem("loginInfo")) {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
      } else {
        this.$message({
          type: "error",
          message: "请登录",
          duration: 1500,
        });
      }
    },
    bindTab: function (index) {
      var self = this;
      this.tabIndex = index;

      this.navigationArr.forEach(function (item, i) {
        self.navigationArr[i].isTab = false;
      });
      if (!this.navigationArr[index].isTab) {
        this.navigationArr[index].isTab = !this.navigationArr[index].isTab;
      }

      if (index == 1 && this.seeFlag) {
        this.getallBooker();
      } else {
        this.seeFlag = true;
      }
    },
    bindFindTab: function (i) {

      this.findIndex = i;
      // 0 上门  1 到店 (前台)
      if (this.findIndex == 0) {
        // 是否支持预约上门 2 不支持

        if (this.bookerdoor == 2) {
          this.$message({
            type: "warning",
            message: "该门店暂不支持预约上门",
            duration: 1500,
          });
          this.findIndex = 1;
          this.getAllser(2);
        } else {
          // this.getAllser(2)
          //设置获取服务的接口数据state为2
          this.getAllserState = 2;
        }
      } else {
        // this.getAllser(1);
        //设置获取服务的接口数据state为2
        this.getAllserState = 1;
      }
    },
    // 获取服务信息
    getAllser: function () {
      var _self = this;
      var uid = 0;
      if (_self.memberInfo && _self.memberInfo.id) {
        uid = _self.memberInfo.id;
      }
      $.ajax({
        url: _self.url + "/android/Booker/getAllser",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          search: "", // 搜索值 默认空
          state: _self.getAllserState, // 预约服务类型1.到店 2.上门
          storeid: _self.loginInfo.storeid,
          uid: uid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.serverListArr = res.data;


          }
        },
      });
    },
    // 服务人员信息
    getAllstaff: function (index1) {
      var _self = this;
      var serverId, skuid, type;
      if (!this.isDetails && JSON.stringify(_self.detailsTable) == "{}") {
        var index = _self.curSelectService;
        serverId = _self.selectService[index].serverid;
        skuid = _self.selectService[index].skuid;


      } else {
        var cur = _self.changeTechnicianIndex;
        if (JSON.stringify(_self.detailsTable) !== "{}") {
          serverId = _self.detailsTable.servermess[cur].serverid;
          skuid = _self.detailsTable.servermess[cur].skuid;

        }
      }

      // console.log(localStorage.getItem("tomorrow"))
      // console.log(localStorage.getItem("tomorrow") - _self.formReservationTime.split(" ")[0])
      // console.log(_self.formReservationTime.split(" ")[0])
      // console.log(localStorage.getItem("tomorrow") != _self.formReservationTime.split(" ")[0])
      if (
        localStorage.getItem("tomorrow") !=
        _self.formReservationTime.split(" ")[0]
      ) {
        type = 0;
      } else {
        type = 1;
      }

      $.ajax({
        url: _self.url + "/android/Booker/getAllstaff",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          storeid: _self.loginInfo.storeid,
          search: "", // 搜索值 默认空
          sertime: _self.selectService[index1].sertime, // 搜索值 默认空
          skuid: skuid,
          serverid: serverId,
          date: _self.formReservationTime || 0,
          type: type || 0,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            if (res.data[0].serMess != []) {


              _self.technicianArr = res.data;
              if (
                _self.technicianData &&
                _self.technicianData.data &&
                _self.technicianData.data.id != 0
              ) {

                let techData = _self.technicianData.data;
                for (let i = 0; i < _self.technicianArr.length; i++) {
                  let item = _self.technicianArr[i].serMess;
                  if (i == 0) {
                    if (item.length > 0) {
                      for (let j = 0; j < item.length; j++) {
                        let item1 = item[j];
                        if (techData.id == item1.id) {
                          item1.checked = techData.checked;
                          item1.isGuest = techData.isGuest;
                        } else {
                          item1.checked = false;
                          item1.isGuest = false;
                        }
                      }
                    }
                  } else {
                    if (item.length > 0) {
                      for (let j = 0; j < item.length; j++) {
                        let item1 = item[j];
                        item1.checked = false;
                        item1.isGuest = false;
                      }
                    }
                  }
                }
              } else {
                for (let i = 0; i < _self.technicianArr.length; i++) {
                  let item = _self.technicianArr[i].serMess;
                  if (item.length > 0) {
                    for (let j = 0; j < item.length; j++) {
                      let item1 = item[j];
                      item1.checked = false;
                      item1.isGuest = false;
                    }
                  }
                }
              }

              _self.isTechnician = true;
              _self.$forceUpdate();
            } else {
              _self.$message({
                type: "warning",
                message: "没有可选择的" + globalTechnicianName,
                duration: 1500,
              });
            }
          }
        },
      });
    },
    //获取规格信息
    getAllsku: function (serverId) {
      var _self = this;
      var uid = 0;
      if (_self.memberInfo && _self.memberInfo.id) {
        uid = _self.memberInfo.id;
      }
      $.ajax({
        url: _self.url + "/getAllsku",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid, // 商户id
          serverid: serverId, // 搜索值 默认空
          storeid: _self.loginInfo.storeid,
          uid: uid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.skuArr = res.data;
          }
        },
      });
    },
    // 添加服务和服务人员
    bindAddTo: function () {
      if (!this.formReservationTime && this.selectService.length != 0) {
        this.$message({
          type: "warning",
          message: "多项服务请先选时间",
          duration: 1500,
        });
      } else {
        if (this.selectService.length < 3) {
          var data = {
            server: "添加服务",
            skuName: "",
            serverPrice: 0,
            technician: "选择" + globalTechnicianName,
            serverId: 0,
            sertime: 0,
            skuid: 0,
            staffid: 0,
          };
          this.selectService.push(data);
        } else {
          this.$message({
            type: "warning",
            message: "最多选三项服务",
            duration: 1500,
          });
        }
      }
    },
    bindRem: function (index) {
      this.selectService.splice(index, 1);
    },
    getData: function () {
      var _self = this;
      //是否支持预约上门
      $.ajax({
        url: _self.url + "/android/Bookerconf/getBesconf",
        type: "post",
        data: {
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.bookerdoor = res.data.bookerdoor;
            // console.log(_self.bookerdoor)
          }
        },
      });
    },
    //添加服务
    addServer: function (index) {
      this.curSelectService = index;
      if (!this.formReservation.phone) {
        this.$message.error({
          message: "手机号输入有误",
          duration: 1500,
        });
      } else {
        this.isServer = true;
        if (this.isServer) {
          this.getAllser();
        }
      }
    },
    // 选择
    confirmServer: function (obj) {


      var index = this.curSelectService;

      var serverId = obj.data.service_id;

      this.$set(this.selectService[index], "serverid", serverId);
      //is_sku 有无规格 1 有
      if (obj.data.is_sku == 1) {
        this.tempServer = obj;
        this.skuTitle = obj.curTab.label_name;
        this.skuCategory = obj.data.service_name;
        this.isSku = true;

        this.getAllsku(serverId);
      } else {
        this.isServer = obj.data.is;
        this.selectService[index].server = obj.data.service_name;
        this.selectService[index].skuName = "";
        this.selectService[index].skuid = 0;
        if (this.memberInfo && this.memberInfo.id) {
          this.selectService[index].serverPrice = obj.data.price;
        } else {
          this.selectService[index].serverPrice = obj.data.price;
        }

        this.modeBesconf(serverId);
      }
    },
    bindSku: function (data) {

      var skuId = data.id;
      var index = this.curSelectService;
      this.$set(this.selectService[index], "skuid", skuId);

      this.selectService[index].server = this.tempServer.data.service_name;
      this.selectService[index].skuName = data.sku;
      // this.selectService[index].serverPrice = this.tempServer.data.price;
      if (this.memberInfo && this.memberInfo.id) {
        this.selectService[index].serverPrice = data.price;
      } else {
        this.selectService[index].serverPrice = data.price;
      }
      var serverId = this.selectService[index].serverid;
      this.isSku = false;
      this.isServer = false;
      this.modeBesconf(serverId);
    },
    // 预约模式配置信息
    modeBesconf: function (id) {
      var _self = this;
      var serverid = id;
      var index = _self.curSelectService;
      $.ajax({
        url: _self.url + "/modeBesconf",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          serverid: serverid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.serviceDuration = res.data;
            var index = _self.curSelectService;
            _self.$set(
              _self.selectService[index],
              "sertime",
              _self.serviceDuration
            );
          }
        },
      });
    },
    // 选择服务人员
    bindTechnician: function (index) {
      this.curSelectService = index;
      if (this.selectService[index].server == "添加服务") {
        this.$message({
          type: "warning",
          message: "请先选择服务",
          duration: 1500,
        });
      } else {
        if (this.formReservationTime) {
          this.getAllstaff(index);
        } else {
          this.$message({
            type: "warning",
            message: "请先选择时间",
            duration: 1500,
          });
        }
      }
    },
    //确认and修改服务人员
    confirmTechnician: function (obj) {

      var _self = this;
      _self.isTechnician = obj.data.is;
      if (this.tabIndex == 0 && !_self.isDetails) {
        // _self.isTechnician = obj.data.is;

        var staffid = obj.data.id;
        var index = _self.curSelectService;
        _self.selectService[index].technician = obj.data.nickname;
        _self.$set(_self.selectService[index], "staffid", staffid);
      } else {

        var cur = _self.changeTechnicianIndex;
        var nickname = obj.data.nickname;
        var id = obj.data.id;
        _self.$set(_self.detailsTable.servermess[cur], "nickname", nickname);
        _self.$set(_self.detailsTable.servermess[cur], "staffid", id);
        // console.log(`修改服务人员 ${nickname}`);
      }
    },

    // 清除预约表单
    reservationEmpty: function () {
      var _self = this;
      _self.formReservation = {
        phone: "",
        people: "",
        time: "",
        remarks: "",
        address: "",
      };
      _self.selectService = [
        {
          server: "添加服务",
          skuName: "",
          serverPrice: 0,
          technician: "选择" + globalTechnicianName,
          serverid: 0,
          sertime: 0,
          skuid: 0,
          staffid: 0,
        },
      ];
      _self.timeLabel = [];
      _self.dayLabel = [];
      _self.formReservationTime = "";
    },
    verificationFrom() {
      var item = this.selectService.find(function (item) {
        return (
          item.server == "添加服务" ||
          item.technician == "选择" + globalTechnicianName
        );
      });
      var reg = /^1(3|4|5|7|8|9)\d{9}$/;
      if (!reg.test(this.formReservation.phone)) {
        this.$message.error({
          message: "手机号输入有误",
          duration: 1500,
        });
      } else if (!this.formReservation.people) {
        this.$message.error({
          message: "请输入到店人",
          duration: 1500,
        });
      } else if (item && item.server == "添加服务") {
        this.$message.error({
          message: "选择您的服务项目",
          duration: 1500,
        });
      } else if (
        item &&
        this.findIndex == 0 &&
        item.technician == "选择" + globalTechnicianName
      ) {
        this.$message.error({
          message: "添加服务" + globalTechnicianName,
          duration: 1500,
        });
      } else if (!this.formReservationTime) {
        this.$message.error({
          message: "选择预约时间",
          duration: 1500,
        });
      } else if (this.findIndex == 0 && !this.formReservation.address) {
        this.$message.error({
          message: "地址不能为空",
          duration: 1500,
        });
      } else {
        //this.subAddBooker()
        this.subAddBooker2();
      }
    },
    bindSubReservation: function () {
      this.verificationFrom();
      // this.subAddBooker2()
    },
    // 选择预约时间
    changeTime: function (val) {
      var _self = this;
      var curTime = new Date().getTime();
      var val = new Date(val).getTime();
      if (val < curTime) {
        _self.$message({
          type: "error",
          message: "预约时间必须大于当前时间",
          duration: 1500,
          onClose: function () {
            _self.formReservation.time = "";
            _self.formReservationTime = "";
          },
        });
      }
    },
    //添加预约
    subAddBooker2: function () {
      var _self = this;
      this.loading = true;
      var uid = 0;
      if (_self.memberInfo && _self.memberInfo.id) {
        uid = _self.memberInfo.id;
      }
      var state = _self.findIndex == 0 ? 2 : 1;
      var server = JSON.stringify(_self.selectService);
      var obj = {
        address: _self.formReservation.address, // 预约到店不用传 预约上门必传
        arrdate: _self.formReservation.time, // 到店时间
        kind: 1, // 1.约服务人员2.约时间
        phone: _self.formReservation.phone, // 手机号
        remarks: _self.formReservation.remarks, // 备注
        server: server, // 服务信息  serverid  服务id skuid  规格id  staffid 服务人员id
        shoper: _self.formReservation.people, // 到店人 预约到店不用传 预约上门必传
        state: state, // 1.预约到店 2.预约上门
        merchantid: _self.loginInfo.merchantid, // 商户id
        storeid: _self.loginInfo.storeid,
        source: 3, //预约来源
        uid: uid,
      };
      var data = JSON.stringify(obj);
      $.ajax({
        url: _self.url + "/addBooker2",
        type: "post",
        data: {
          serverdata: data,
        },
        success: function (res) {
          _self.loading = false;
          // var res = JSON.parse(res);

          if (res.code == 1) {
            _self.$message({
              type: "success",
              message: res.msg,
              duration: 1500,
            });
            _self.reservationEmpty();
            _self.bindTab(1);
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
      });
    },
    //实体卡登录  0527502818
    loadEntityCard: function (card) {
      let _self = this;
      $.ajax({
        url: _self.url + "/android/Member/readEntityCard",
        type: "post",
        data: {
          card_voucher: card,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1 && res.data.length > 0) {
            _self.memberInfo = res.data[0];
            if (_self.memberInfo && _self.memberInfo.id) {
              _self.formReservation.people = _self.memberInfo.member_name;
              _self.formReservation.phone = _self.memberInfo.phone;
            }
          } else {
            _self.$message({
              type: "warning",
              message: "未找到该会员",
              duration: 1500,
            });
            _self.memberInfo = {};
            _self.formReservation.people = "";
          }
          _self.selectService = [
            {
              server: "添加服务",
              skuName: "",
              serverPrice: 0,
              technician: "选择" + globalTechnicianName,
              serverid: 0,
              sertime: 0,
              skuid: 0,
              staffid: 0,
            },
          ];
        },
        error: function (e) {

        },
      });
    },

    // 会员查询
    bindInquire: function (phone) {
      var reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (!phone) {
        this.$message.error({
          message: "输入手机号,登录会员",
          duration: 1500,
        });
      } else if (phone.length != "11" && !reg.test(phone)) {
        if (phone.length == 10) {
          this.loadEntityCard(phone);
        } else {
          this.$message.error({
            message: "会员手机号输入有误",
            duration: 1500,
          });
        }
      } else {
        this.memberSearch(phone);
      }
    },
    //  输入搜索
    bindInquireMember: function (phone) {
      var reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (phone.length == "11" && reg.test(phone)) {
        this.memberSearch(phone);
      }
    },
    // 会员查询
    memberSearch: function (phone) {
      var _self = this;
      this.loading = true;
      $.ajax({
        url: _self.url + "/android/vip/memberSearch",
        type: "post",
        data: {
          keyword: phone,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          _self.loading = false;
          // var res = JSON.parse(res);
          if (res.code == 1 && res.data.length > 0) {
            _self.memberInfo = res.data[0];
            if (_self.memberInfo && _self.memberInfo.id) {
              _self.formReservation.people = _self.memberInfo.member_name;
            }
          } else {
            _self.$message({
              type: "warning",
              message: "未找到该会员",
              duration: 1500,
            });
            _self.memberInfo = {};
            _self.formReservation.people = "";
          }
          _self.selectService = [
            {
              server: "添加服务",
              skuName: "",
              serverPrice: 0,
              technician: "选择" + globalTechnicianName,
              serverid: 0,
              sertime: 0,
              skuid: 0,
              staffid: 0,
            },
          ];
        },
        error: function (error) {
          _self.loading = false;

        },
      });
    },
    /*
     *
     * 查看预约
     *
     * */

    tabCurReservation: function () {
      this.currentPage = 1;
      this.getallBooker();
      this.$nextTick(() => {
        this.$refs.reservationTable.bodyWrapper.scrollTop = 0;
      });
    },
    getallBooker: function () {
      var _self = this;
      _self.loading = true;
      $.ajax({
        url: _self.url + "/getallBooker",
        type: "POST",
        data: {
          limit: _self.limit, // 条数
          page: _self.currentPage, // 页数
          keyword: _self.reservationCall, // 手机号
          status: _self.tabCur, // 1.待服务2.已超时3.已开单4.已取消5.未分配6.全部
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            // _self.tableData = []
            _self.tableData = res.data;
            _self.tableData.forEach((item) => {
              if (item.arrdate) {
                // console.log("获取今天明天等")
                var now = new Date();
                var toDay = new Date();
                var tomorrow = now.setDate(now.getDate() + 1);
                toDay = _self.formatDate(toDay);
                tomorrow = _self.formatDate(new Date(tomorrow));
                let listDay = item.arrdate.split(" ")[0];
                let listTime = item.arrdate.split(" ")[1];
                if (listDay == toDay) {
                  item.arrdate = "今天 " + listTime;
                }
                if (listDay == tomorrow) {
                  item.arrdate = "明天 " + listTime;
                }
              }
            });
            _self.allCount = res.count;
            _self.loading = false;
            _self.seeFlag = false;
          } else {
            _self.tableData = [];
            _self.loading = false;
          }
        },
      });
    },
    // 修改服务人员
    bindChangeTechnician: function (index) {
      this.$message({
        type: "warning",
        message: "如需修改" + globalTechnicianName + "，请重新预约",
        duration: 1500,
      });
      //console.log(this.changeTechnicianIndex);
    },
    // 修改预约时间
    modifyAppointment: function (val) {
      var _self = this;
      var curTime = new Date().getTime();
      var val = new Date(val).getTime();
      if (val < curTime) {
        _self.$message({
          type: "error",
          message: "预约时间必须大于当前时间",
          duration: 1500,
        });
      }
    },
    bingGetSearch: function () {
      var _self = this;
      if (this.reservationCall) {
        _self.currentPage = 1;
        this.getallBooker();
      }
    },
    bindSeeDetails: function (row) {

      this.isDetails = true;
      this.orderNo = row.pre_order;
      // this.detailsTable.push(row)
      this.getoneBooker(row.id);
    },
    // 请求预约详情
    getoneBooker: function (id) {
      var _self = this;
      var id = id;
      _self.loading = true;
      $.ajax({
        url: _self.url + "/android/Booker/getoneBooker",
        type: "POST",
        data: {
          id: id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          _self.loading = false;
          if (res.code == 1) {
            _self.detailsTable = res.data;
            _self.order_phone = res.data["phone"];
            // console.log('需要信息一',_self.detailsTable);
          }
        },
      });
    },
    bindCloseisDetails: function () {
      this.isDetails = false;
    },
    bindChooseReason: function (val) {
      this.curReason = this.cancelReason.find(function (item) {
        return item.id == val;
      });
    },
    bindConfirmReservation: function () {
      var _self = this;
      _self.loading = true;
      if (JSON.stringify(_self.curReason) == "{}") {
        _self.$message({
          type: "error",
          message: "请选择取消原因",
          duration: 1500,
        });
        return false;
      }
      $.ajax({
        url: _self.url + "/android/Booker/cancelBooker",
        type: "POST",
        data: {
          id: _self.detailsTable.id,
          paystate: _self.detailsTable.paystate,
          reason: _self.curReason.label, // 取消原因
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message({
              type: "success",
              message: res.data,
              duration: 1500,
            });
            _self.isDetails = false;
            _self.isCancelReservation = false;
            _self.tabCurReservation();
            _self.loading = false;
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
            _self.loading = false;
          }
        },
      });
    },
    // 详情修改 预约时间 服务人员
    bindSave: function () {
      var _self = this;
      var time = _self.detailsTable.arrdate;
      var curTime = new Date().getTime();
      var val = new Date(time).getTime();
      if (val < curTime) {
        _self.$message({
          type: "error",
          message: "预约时间必须大于当前时间",
          duration: 1500,
        });
        return false;
      } else {
        var _self = this;
        var cur = _self.changeTechnicianIndex;
        var obj = {
          address: _self.detailsTable.address,
          arrdate: _self.detailsTable.arrdate,
          id: _self.detailsTable.id,
          paystate: _self.detailsTable.paystate == "待支付" ? "1" : "2",
          remarks: _self.detailsTable.remarks,
          server: _self.detailsTable.servermess,
          state: _self.detailsTable.state,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        };
        var updateData = JSON.stringify(obj);
        $.ajax({
          url: _self.url + "/updateBooker",
          type: "POST",
          data: {
            serverdata: updateData,
          },
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              _self.$message({
                type: "success",
                message: res.data,
                duration: 1500,
              });
              _self.isDetails = false;
              _self.currentPage = 1;
              _self.getallBooker();
            } else {
              _self.$message({
                type: "error",
                message: res.msg,
                duration: 1500,
              });
            }
          },
        });
      }
    },
    //分页
    handleSizeChange(val) {

    },
    handleCurrentChange(val) {

      this.currentPage = val;
      this.getallBooker();
      this.$nextTick(() => {
        this.$refs.reservationTable.bodyWrapper.scrollTop = 0;
      });
    },
    // 服务开单
    serviceBilling: function () {
      var _self = this;

      if (!this.detailsTable.moretime) {
        let servermess = this.detailsTable.servermess;
        // console.log('查看第一个数据',_self.detailsTable);
        var buyerId = 0;
        if (this.memberInfo && this.memberInfo.id) {
          buyerId = this.memberInfo.id;
        }
        var orderItems = [];
        var technicians = [];

        for (var i = 0; i < servermess.length; i++) {
          orderItems.push({
            cardDetailsId: 0, // (开单使用)使用卡项详情的id
            cardId: 0, // 卡项id
            discount: "10", // 折扣  （开单选择充值卡有）
            equityType: 1, // 1 无权益 2折扣 3抵扣 4优惠金额
            goodsId: servermess[i].id, // 商品id 服务，产品卡项都填写id
            itemId: "0",
            itemImgId: "0", // 预览图id
            itemName: servermess[i].sername, // 商品名称
            itemType: 1, // 1 服务 2产品 3卡项 4充值
            num: 1, // 数量，除产品外，其他都填写1
            originPrice: servermess[i].price * 100, // 充值金额  原价( 分 )
            recharge_money: 0, // 充值金额（本金）金额 （分） 手动充值时必传
            realPay: servermess[i].price * 100, // 充值金额真实支付（分）
            present_money: 0, // 充值（赠送）金额 (分) 手动充值时必传
            salesmen: [], // 选择的销售id
            skuId: servermess[i].skuid, // 规格id，非规格天写0
            skuName: servermess[i].skuname, // 规格名称（如：红色,大）没有填空字符串
            stage: "1",
            technicians: [
              {
                id: servermess[i].staffid,
                nickname: servermess[i].nickname,
              },
            ], // 当前阶段（填写1）取单会返回该字段，只有1可删除、编辑权益优惠信息
          });
        }
        // 服务人员id
        // console.log(orderItems);
        // console.log('收货地址', _self.detailsTable.address);
        // $.ajax({
        //     url: _self.url + "/orderSave",
        //     type: 'post',
        //     data: {
        //         addressInfo: _self.detailsTable.address,  // 收货地址信息
        //         bookerid: 0,        // 预约id 来源是预约时使用
        //         buyerId: buyerId,          // 用户id
        //         cashierId: _self.loginInfo.id,      // 收银员id
        //         dispatchFee: 0,  // 运费 （分）
        //         dispatchType: 0, // 配送类型： 1，到店自提，2，配送，0，非配送
        //         merchantid: _self.loginInfo.merchantid,     //  商户id
        //         orderGiftItems: '',                    //订单礼品数组（预留字段）
        //         orderNo: 0,               // 订单号（取单时需要传）
        //         orderType: 1,           // 1：品项（产品服务开单），2：品项（产品服务开单）3：购买卡项，4：充值 5：充卡6直接收款
        //         promotions: '',             // 预留字段（优惠信息）
        //         remark: _self.detailsTable.remarks,   // 订单备注
        //         sourceType: 1,         // 来源类型 1,开单，2,预约，3,取单
        //         storeid: _self.loginInfo.storeid,               // 店铺id
        //         totalPay: _self.detailsTable.payment * 100,             // 订单总价（分）
        //         orderItems: JSON.stringify(orderItems),
        //         shift_no: _self.loginInfo.shift_no,
        //         extraData:{}
        //     },
        //     success: function (res) {
        //         // var res = JSON.parse(res);
        //         _self.loading = false;
        //         if (res.code == 1) {
        //             _self.billingInfo = res.data;
        //             console.log('接口数据', res.data);
        //             _self.isDetails = false;
        //             var obj = {
        //                 orderReservation: 1,
        //                 data: _self.billingInfo
        //             };
        //
        //             return false;
        //             // localStorage.setItem('billing', JSON.stringify(obj));
        //             // window.location.href = "cashier_system.html"
        //
        //         } else {
        //             _self.$message({
        //                 type: "error",
        //                 message: res.msg,
        //                 duration: 1500,
        //             })
        //         }
        //     },
        //     error: function (error) {
        //         _self.loading = false;
        //         console.log(error);
        //     }
        // })

        var orderNo = _self.orderNo;
        //window.location.href = "cashier_system.html?orderNo=" + orderNo;
        let href = "cashier_system.html?orderNo=" + orderNo;
        top.app.toPage(href);
      } else {
        _self.$message({
          type: "type",
          message: "服务已超时，请修改预约时间",
          duration: 1500,
        });
      }
    },
    //服务人员点客
    bindServerTab: function (index, item) {
      this.curIndex = index;
      this.curTab = item;
    },

    bindSelectService: function (item, data) {
      // debugger
      //只能选中一个
      for (let i = 0; i < data.length; i++) {
        let dataItem = data[i];
        if (item.id == dataItem.id) {
          dataItem.checked = true;
          dataItem.isGuest = true;
        } else {
          dataItem.checked = false;
          dataItem.isGuest = false;
        }
      }
      this.$forceUpdate();
      this.technicianData = {
        is: false,
        data: item,
        curTab: this.curTab,
      };
    },
    verifyGuest: function (data) {

      this.technicianData.data.isGuest = data.isGuest;
      this.$forceUpdate();
    },
    cancelChooseTechno: function () {
      this.isTechnician = false;
    },
    saveChooseTechno: function () {
      this.confirmTechnician(this.technicianData);
    },
  },
});

// $(document).ajaxStart(function () {
//     // 只要ajax请求发生就会执行
//     reservation.loading = true
// })
//
// $(document).ajaxStop(function () {
//     // 只要ajax请求结束 发生就会执行
//     reservation.loading = false
// })
