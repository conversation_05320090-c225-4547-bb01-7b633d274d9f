var baseUrl = localStorage.getItem("fdb-domainName") + "/index.php?s=";

//卡项列表
const card = [
  {
    card_name: "美容卡100次卡",
    aging: "永久有效",
    faceValue: "100",
    giveAway: "100",
    type: 0,
  },
  {
    card_name: "美容卡100次卡",
    aging: "永久有效",
    faceValue: "100",
    giveAway: "100",
    type: 1,
  },
];

//状态
const labelStatusArr = [
  {
    label: "所有状态",
    id: 0,
  },
  {
    label: "上架中",
    id: 1,
  },
  {
    label: "未上架",
    id: 2,
  },
];

const labelSortArr = [
  {
    label: "升序",
    id: 1,
  },
  {
    label: "降序",
    id: 2,
  },
];

const shop = new Vue({
  el: "#shop",
  data: {
    globalTechnicianName: globalTechnicianName,
    url: baseUrl,
    loginInfo: {},
    loading: false,
    tabCur: "0",
    keyword: "",
    placeholderText: "请输入服务名称",
    //标签选择
    labelArr: [],
    labelStatusArr: labelStatusArr,
    labelSortArr: labelSortArr,
    filterTabel: {
      isType: false,
      typeIndex: -1,
      isStatus: false,
      statusIndex: 0,
      isSort: false,
      sortIndex: 0,
      labelId: "",
      statusId: 0,
      sortId: 1,
    },

    // 商品详情
    productDetails: [],
    serviceDetails: {},
    defaultCur: {},
    curId: 0,
    detailsLoading: true,
    //服务
    shopArr: [],
    serverIndex: 0,
    serverPage: 1,
    serverLimit: 10,
    isScroll: false,
    allCount: 0,
    //编辑
    curEdit: {},
    isEdit: false,

    previewImageUrl: "",
    isPreview: false,
    PreviewLen: "",
    //卡项
    cardArr: [],
    cardIndex: 0,
    curDetails: {},
    cardType: "",
    cardBeApplicable: [],
    limit: 10,
    page: 1,
    isCardScroll: false,
    allCardCount: 0,
    //发卡
    isCardIssue: false,
    tableData: [],
    // 产品
    productArr: [],
    productIndex: 0,
    productDetailsObj: {},
    productId: 0,
    productClass: "",
    productPage: 1,
    productLimit: 10,
    productPriceTabel: [],
    isProductScroll: false,
    productCount: 0,
    objProduct: [],
    //该值用来存产品类表选中的产品store_status
    productstatus: null,

    // 触底加载
    loadingtip: "加载中···",
    busy: false, //加载显示状态
    busyCard: false,
    busyProduct: false,
  },
  mounted() {
    this.getLoginInfo();
    this.serviceList(0);
    this.getInit();
  },
  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      if (!isNaN(money)) {
        return (money / 100).toFixed(2);
      }
      return "";
    },
  },
  methods: {
    isTextOverflow(text, maxLength) {
      return text?.length > maxLength;
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      data.forEach((column, index) => {
        if (index === 0) {
          if (column["card_type"] == 1 && column["once_cardtype"] == 3) {
            sums[index] = "服务总数(不包含赠送)";
          }
          return;
        } else if (index === 3) {
          sums[index] = "";
          return;
        } else if (index === 2) {
          if (column["card_type"] == 1 && column["once_cardtype"] == 3) {
            if (column["totalNum"]) {
              sums[index] = "总" + column["totalNum"] + "次";
            } else {
              sums[index] = "总" + 0 + "次";
            }
          }

          return;
        }
      });

      return sums;
    },

    headerClass: function ({ row, rowIndex }) {
      return "background:#f6f6f6;color:#333;fontSize:15px;font-weight:600";
    },

    getLoginInfo: function () {
      if (localStorage.getItem("loginInfo")) {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
      } else {
        this.$message({
          type: "error",
          message: "请登录",
          duration: 1500,
        });
      }
    },

    bindTab: function (val) {
      this.filterTabel = {
        isType: false,
        typeIndex: -1,
        isStatus: false,
        statusIndex: 0,
        isSort: false,
        sortIndex: 0,
        labelId: "",
        statusId: 0,
        sortId: 1,
      };
      switch (val) {
        case "0":
          this.getInit();
          this.keyword = "";
          this.placeholderText = "请输入服务名称";
          this.$nextTick(() => {
            this.$refs.goodsServer.scrollTop = 0;
            this.inputFocus(this.$refs.goodsKeyWord);
          });
          break;
        case "1":
          this.page = 1;
          this.getCardClass();
          this.getCardDataByPage(0);
          this.keyword = "";
          this.placeholderText = "请输入卡项名称";
          this.$nextTick(() => {
            this.$refs.goodsCard.scrollTop = 0;
            this.inputFocus(this.$refs.goodsKeyWord);
          });
          break;
        case "2":
          this.productPage = 1;
          this.getProductLabelClass();
          this.getProductData(0);
          this.keyword = "";
          this.placeholderText = "请输入产品名称";
          this.$nextTick(() => {
            this.$refs.goodsProduct.scrollTop = 0;
            this.inputFocus(this.$refs.goodsKeyWord);
          });
          break;
      }
    },

    //input 获得焦点
    inputFocus: function (dom) {
      this.$nextTick(
        function () {
          dom.focus();
        }.bind(this)
      );
    },

    // 标签选择
    bindLabel: function (type, index, data) {
      switch (this.tabCur) {
        case "0":
          this.serverPage = 1;
          switch (type) {
            case 0:
              this.filterTabel.labelId = data.id;
              this.filterTabel.typeIndex = index;
              this.filterTabel.isType = false;
              break;
            case 1:
              this.filterTabel.statusId = data.id;
              this.filterTabel.statusIndex = index;
              this.filterTabel.isStatus = false;
              break;
            case 2:
              this.filterTabel.sortId = data.id;
              this.filterTabel.sortIndex = index;
              this.filterTabel.isSort = false;
              break;
          }
          this.serverIndex = 0;
          this.serviceList(0);
          break;
        case "1":
          this.page = 1;
          switch (type) {
            case 0:
              this.filterTabel.labelId = data.id;
              this.filterTabel.typeIndex = index;
              this.filterTabel.isType = false;
              break;
            case 1:
              this.filterTabel.statusId = data.id;
              this.filterTabel.statusIndex = index;
              this.filterTabel.isStatus = false;
              break;
            case 2:
              this.filterTabel.sortId = data.id;
              this.filterTabel.sortIndex = index;
              this.filterTabel.isSort = false;
              break;
          }
          this.cardIndex = 0;
          this.getCardDataByPage(0);
          break;
        case "2":
          this.productPage = 1;
          switch (type) {
            case 0:
              this.filterTabel.labelId = data.id;
              this.filterTabel.typeIndex = index;
              this.filterTabel.isType = false;
              break;
            case 1:
              this.filterTabel.statusId = data.id;
              this.filterTabel.statusIndex = index;
              this.filterTabel.isStatus = false;
              break;
            case 2:
              this.filterTabel.sortId = data.id;
              this.filterTabel.sortIndex = index;
              this.filterTabel.isSort = false;
              break;
          }
          this.productIndex = 0;
          this.getProductData();
          break;
      }
    },

    //取消
    bindIsTypeCancel: function (type) {
      switch (type) {
        case 0:
          this.filterTabel.isType = false;
          break;
        case 1:
          this.filterTabel.isStatus = false;
          break;
        case 2:
          this.filterTabel.isSort = false;
          break;
      }
    },

    // 加载更多
    loadMore: function () {
      var _self = this;
      _self.isScroll = true;
      if (_self.allCount == _self.shopArr.length) {
        _self.loadingtip = "数据已全部加载";
        return;
      } else {
        _self.busy = true;
        _self.loadingtip = "加载中···";
        _self.serverPage++;
        _self.serviceList(1);
      }
    },

    serviceList: function (flag) {
      var _self = this;
      _self.loading = true;
      if (!flag) {
        _self.detailsLoading = true;
      }
      //服务
      $.ajax({
        url: _self.url + "/android/Service/serviceList",
        type: "post",
        data: {
          keyword: _self.keyword,
          labelid: _self.filterTabel.labelId,
          sort: _self.filterTabel.sortId, // 默认正序，1正序 2倒序
          status: _self.filterTabel.statusId, // 1上架。  2下架
          storeid: _self.loginInfo.storeid,
          page: _self.serverPage,
          limit: _self.serverLimit,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0 && res.data.length > 0) {
            _self.allCount = res.count;
            if (flag == 1) {
              // 多次加载数据
              _self.loading = false;
              _self.shopArr = _self.shopArr.concat(res.data);
              if (
                res.count == 0 &&
                _self.shopArr.length == res.data.length &&
                res.data.length == 0
              ) {
                _self.isScroll = true;
                return false;
              } else {
                _self.isScroll = false;
              }
              _self.detailsLoading = false;
            } else {
              // 第一次加载数据
              _self.shopArr = res.data;
              _self.defaultCur = res.data[0];
              var id = _self.defaultCur.id;
              _self.$nextTick(function () {
                _self.details(id);
              });
              _self.isScroll = false;
            }
          } else {
            _self.shopArr = [];
            _self.defaultCur = {};
            _self.serviceDetails = {};
            _self.productDetails = [];
            _self.loading = false;
            _self.detailsLoading = false;
          }
        },
      });
    },

    //首页--标签接口
    // labelList: function () {
    //     this.loading = true;
    //     var _self = this;
    //     let type=0;
    //     if(_self.billingType==0){
    //         type=4;
    //     }else if(_self.billingType==1){
    //         type=2;
    //     }
    //     $.ajax({
    //         url: _self.url + "/android/Goodsclass/goodsClassLabel",
    //         type: 'post',
    //         data: {
    //             merchantid: _self.loginInfo.merchantid,
    //             storeid: _self.loginInfo.storeid,
    //             type: 4,
    //         },
    //         success: function (res) {
    //             // var res = JSON.parse(res);
    //             if (res.code == 1) {
    //                 _self.cashier_open_order_service_label = res.data;
    //                 _self.cashier_open_order_service_label.unshift({ label_name: '所有标签' });
    //                 _self.loading = false;
    //             } else {
    //                 _self.loading = false;
    //                 _self.$message({
    //                     type: "error",
    //                     message: res.msg,
    //                     duration: 1500
    //                 })
    //             }
    //         }
    //     });
    // },

    getInit() {
      this.inputFocus(this.$refs.goodsKeyWord);
      var _self = this;
      //标签
      $.ajax({
        url: _self.url + "/android/Goodsclass/goodsClassLabel",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          type: 4,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.labelArr = res.data;
          } else {
            _self.labelArr = [];
          }
        },
      });
    },

    // 服务详情
    details: function (id) {
      var _self = this;
      var serviceid = id;
      _self.detailsLoading = true;
      $.ajax({
        url: _self.url + "/android/Service/details",
        type: "post",
        data: {
          serviceid: serviceid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.productDetails = res.data;
            _self.serviceDetails = _self.productDetails.service;
            _self.loading = false;
          } else {
            _self.productDetails = [];
            _self.loading = false;
          }
          _self.detailsLoading = false;
        },
      });
    },

    // 产品选中
    bindShop: function (type, index, data) {
      switch (type) {
        case 0:
          var serverId = data.id;
          this.serverIndex = index;
          this.details(serverId);
          break;
        case 1:
          this.cardType = data.type;
          this.cardIndex = index;
          // this.getCardDataByPage(1)
          var cardId = this.cardArr[this.cardIndex].id;
          this.curDetails = data;
          this.getCardDetails(cardId);
          break;
        case 2:
          var productId = data.id;
          this.productIndex = index;
          this.objProduct = data;
          this.getProductInfo(productId);
          break;
      }
    },

    //编辑
    bindEdit: function (type) {
      // type  0 服务 1 卡项 2 产品
      var _self = this;
      _self.loading = true;
      $.ajax({
        url: _self.url + "/android/Service/getSerFind",
        type: "post",
        data: {
          serviceid: _self.defaultCur.id,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.curEdit = res.data;
            _self.isEdit = true;
            _self.loading = false;
          }
        },
      });
    },

    //提交编辑修改
    subEdit: function () {
      var _self = this;
      _self.loading = true;
      $.ajax({
        url: _self.url + "/android/Service/EdidService",
        type: "post",
        data: {
          id: _self.curEdit.id,
          img_url: _self.curEdit.img_url,
          is_sku: _self.curEdit.is_sku,
          material_id: _self.curEdit.material_id,
          price: _self.curEdit.price,
          service_name: _self.curEdit.service_name,
          sku: _self.curEdit.sku,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message({
              type: "success",
              messags: res.msg,
              duration: 1500,
            });
            _self.serviceList(1);
            _self.loading = false;
            _self.isEdit = false;
          } else {
            _self.$message({
              type: "error",
              messags: res.msg,
              duration: 1500,
            });
            _self.loading = false;
            _self.isEdit = false;
          }
        },
      });
    },

    //服务上and下架
    EdStatus: function () {
      var _self = this;
      _self.loading = true;
      var status = _self.serviceDetails.status == 1 ? 2 : 1;
      $.ajax({
        url: _self.url + "/android/Service/EdStatus",
        type: "post",
        data: {
          serviceid: _self.productDetails["service"]["service_id"],
          status: status,
          id: _self.productDetails["service"]["id"],
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message({
              type: "success",
              message: res.msg,
              duration: 1500,
            });
            _self.serverPage = 1;
            _self.serverIndex = 0;
            _self.serviceList(0);
            _self.loading = false;
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
            _self.loading = false;
          }
        },
      });
    },

    //卡项上and下架
    upperLowerShelf: function (index) {
      var _self = this;
      _self.loading = true;
      var status = _self.curDetails.status == 1 ? 2 : 1;
      $.ajax({
        url: _self.url + "/android/Card/upperLowerShelf",
        type: "post",
        data: {
          // id: _self.curDetails.id,
          id: _self.curDetails.id,
          type: status,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message({
              type: "success",
              message: res.msg,
              duration: 1500,
            });
            _self.page = 1;
            _self.cardIndex = 0;
            _self.getCardDataByPage(0);
            _self.loading = false;
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
            _self.loading = false;
          }
        },
      });
    },

    // 上/下架
    bindChangeFrame: function (index) {
      var _self = this;

      var shopType = _self.tabCur;
      switch (shopType) {
        case "0":
          _self.EdStatus(index);
          break;
        case "1":
          _self.upperLowerShelf(index);
          break;
        case "2":
          _self.UpperLowerProducts(index);
          break;
      }
    },

    /**
     * 卡项
     *
     *
     * */

    getCardClass: function () {
      //卡项标签
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Card/getCardClass",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.labelArr = res.data;
          }
        },
      });
    },

    loadMoreCard: function () {
      var _self = this;
      _self.isCardScroll = true;
      if (_self.allCardCount == _self.cardArr.length) {
        _self.loadingtip = "数据已全部加载";
        return;
      } else {
        _self.busyCard = true;
        _self.loadingtip = "加载中···";
        _self.page++;
        _self.getCardDataByPage(1);
      }
    },

    // 卡项列表
    getCardDataByPage: function (flag) {
      var _self = this;
      _self.loading = true;
      $.ajax({
        url: _self.url + "/android/Card/getCardDataByPage",
        type: "post",
        data: {
          cardType: _self.filterTabel.labelId,
          keyword: _self.keyword,
          limit: _self.limit,
          page: _self.page,
          order: _self.filterTabel.sortId,
          status: _self.filterTabel.statusId,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          // console.log(res);
          if (res.data.length > 0 && _self.page == 1) {
            var cardId = res.data[0]["id"];
            _self.getCardDetails(cardId);
          }
          if (res.code == 0 && res.data.length > 0) {
            _self.allCardCount = res.count;
            if (flag == 1) {
              _self.loading = false;
              _self.cardArr = _self.cardArr.concat(res.data);
              if (res.count == 0) {
                _self.isCardScroll = true;
              } else {
                _self.isCardScroll = false;
              }
            } else {
              _self.cardArr = res.data;
              _self.curDetails = _self.cardArr[0];
              _self.loading = false;
              _self.isCardScroll = false;
            }
          } else {
            _self.cardArr = [];
            _self.curDetails = {};
            _self.loading = false;
          }
        },
      });
    },

    // 卡项权益
    getCardDetails: function (cardId) {
      var _self = this;
      var cardId = cardId;
      _self.loading = true;
      _self.detailsLoading = true;
      $.ajax({
        url: _self.url + "/android/Card/getCardDetails",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          cardId: cardId,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.cardBeApplicable = res.data;
            _self.loading = false;
          } else {
            _self.cardBeApplicable = [];
            _self.loading = false;
          }
          _self.detailsLoading = false;
        },
      });
    },

    // 发卡
    bindCardIssue: function () {
      var _self = this;
      $.ajax({
        // 这个函数没用上
        url: _self.url + "/grantCard",
        type: "post",
        data: {
          cardid: _self.curDetails.id,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
          } else {
            _self.$message.error({
              message: "获取失败",
              duration: 1500,
            });
          }
        },
      });
      $.ajax({
        url: _self.url + "/api/Card/getXcxCardDetails",
        type: "post",
        data: {
          cardid: _self.curDetails.id,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
          } else {
            _self.$message.error({
              message: "获取失败",
              duration: 1500,
            });
          }
        },
      });
    },

    /**
     *   产品
     *
     * */

    getProductLabelClass: function () {
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Goodsclass/goodsClassLabel",
        type: "post",
        data: {
          type: 2,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.labelArr = res.data;
          } else {
            _self.labelArr = [];
          }
        },
      });
    },

    loadMoreProduct: function () {
      var _self = this;
      _self.isProductScroll = true;
      if (_self.productCount == _self.productArr.length) {
        _self.loadingtip = "数据已全部加载";
        return;
      } else {
        _self.busyProduct = true;
        _self.loadingtip = "加载中···";
        _self.productPage++;
        _self.getProductData(1);
      }
    },

    //获取产品的全部列表
    getProductData: function (flag) {
      var _self = this;
      this.loading = true;
      $.ajax({
        url: _self.url + "/android/Product/getProductData",
        type: "post",
        data: {
          class: _self.productClass, // 分类id	 没有 空
          keyword: _self.keyword,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          label: _self.filterTabel.labelId,
          order: _self.filterTabel.sortId,
          status: _self.filterTabel.statusId,
          limit: _self.productLimit,
          page: _self.productPage,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 0) {
            _self.loading = false;
            _self.productCount = res.count;
            if (flag == 1) {

              _self.productArr = _self.productArr.concat(res.data);
              if (res.count == 0) {
                _self.isProductScroll = true;
              } else {
                _self.isProductScroll = false;
              }
            } else {
              _self.loading = false;
              _self.productArr = res.data;
              _self.objProduct = _self.productArr[0];
              var productId = _self.productArr[0].id;
              _self.getProductInfo(productId);
            }
          } else {
            _self.loading = false;
            _self.productArr = [];
          }
        },
      });
    },

    //获取产品详情  其中productDetailsObj是用来渲染详情信息的对象
    getProductInfo: function (productId) {
      var _self = this;
      this.loading = true;
      _self.detailsLoading = true;
      $.ajax({
        url: _self.url + "/android/Product/getProductInfo",
        type: "post",
        data: {
          productId: productId,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.loading = false;
            _self.productDetailsObj = res.data;
            if (
              _self.productDetailsObj &&
              _self.productDetailsObj.skuinfo &&
              _self.productDetailsObj.skuinfo.skulist
            ) {
              _self.productPriceTabel = _self.productDetailsObj.skuinfo.skulist;
            } else {
              _self.productPriceTabel = [];
            }
          } else {
            _self.loading = false;
            _self.productDetails = {};
          }
          _self.detailsLoading = false;
        },
      });
    },

    //产品上下架
    UpperLowerProducts: function (index) {
      var _self = this;
      _self.loading = true;
      var status = _self.objProduct.store_status == 1 ? 2 : 1;
      $.ajax({
        url: _self.url + "/Api/Product/upperLowerShelf",
        type: "post",
        data: {
          id: _self.productDetailsObj.store_product_id,
          type: status,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message({
              type: "success",
              message: res.msg,
              duration: 5000,
            });
            _self.productPage = 1;
            _self.productIndex = 0;
            _self.getProductData(0);
            _self.loading = false;
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
            _self.loading = false;
          }
        },
      });
    },

    //搜索
    bindSearch: function () {
      switch (this.tabCur) {
        case "0":
          this.serverPage = 1;
          this.serviceList(0);
          break;
        case "1":
          this.getCardDataByPage();
          break;
        case "2":
          this.getProductData();
          break;
      }
    },

    // bindInputSearch: function (val) {
    //     var self = this;
    //     console.log("输入框输入");
    //     if (val == '') {
    //         switch (this.tabCur) {
    //             case '0':
    //                 this.serverPage = 1;
    //                 this.serviceList(0)
    //                 break;
    //             case '1':
    //                 this.getCardDataByPage()
    //                 break;
    //             case '2':
    //                 this.getProductData()
    //                 break;
    //         }
    //     }
    // },

    handleProgress: function (event, file, fileList) {
      this.PreviewLen = fileList;
    },
    handleRemove(file, fileList) {

    },
    handlePictureCardPreview(file) {
      this.previewImageUrl = file.url;
      this.isPreview = true;
    },
  },

  watch: {
    //  商品(服务-卡项-产品)搜索
    keyword: function (n, o) {
      if (!n && o) {
        switch (this.tabCur) {
          case "0":
            this.serverPage = 1;
            this.serviceList(0);
            break;
          case "1":
            this.getCardDataByPage();
            break;
          case "2":
            this.getProductData();
            break;
        }
      }
    },
  },
});
