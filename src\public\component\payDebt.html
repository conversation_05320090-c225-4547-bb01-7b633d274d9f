<div class="pay-wrap" v-cloak>
  <el-dialog
    :visible.sync="cz_qudan"
    fullscreen
    :show-close="false"
    class="cz_kaidan"
  >
    <div class="kaidan">
      <div slot="title">
        <div class="cz_qudan_top" style="background: #f6f6f6; color: #333">
          <div class="title-left" @click="cz_qudan_close">关闭</div>
          <div style="flex: 1; text-align: center">收银台</div>
        </div>
      </div>
      <div
        class="cash-register"
        v-if="(orderNumber.indexOf(',')==-1) && kd_xinxi_list.order_number"
      >
        <div class="left_main">
          <div class="left_mid">
            <app-heading title="订单信息"></app-heading>
            <ul class="order_info_ul">
              <li class="order_info_li">
                <span>订单编号</span>
                <span>{{kd_xinxi_list.order_number}}</span>
              </li>
              <li class="order_info_li">
                <span>下单时间</span>
                <span>{{kd_xinxi_list.order_time}}</span>
              </li>
              <li
                v-if="kd_xinxi_list_buyer && kd_xinxi_list_buyer.id!=0"
                class="order_info_li"
              >
                <span>会员编号</span>
                <span v-if="kd_xinxi_list.buyer">
                  {{kd_xinxi_list.buyer.member_number}}
                </span>
              </li>
              <li class="order_info_li">
                <span>下单人</span>
                <span v-if="kd_xinxi_list_buyer && kd_xinxi_list_buyer.id!=0">
                  {{kd_xinxi_list_buyer.member_name}}
                </span>
                <span v-else>普通顾客</span>
              </li>
              <li class="order_info_li">
                <span>收银员</span>
                <span>{{kd_xinxi_list_cashierInfo.nickname}}</span>
              </li>
            </ul>

            <app-heading title="消费明细"></app-heading>
            <ul class="order_info_ul border-margin" style="border-bottom: 0">
              <li
                class="order_info_wrap"
                v-for="(item,index) in orderDetails.orderInfo"
              >
                <div class="order_info_index">{{index+1}}</div>
                <div class="order_item">
                  <div class="order_info_li">
                    <span>{{item.name}}</span>
                    <span>￥{{item.price}}</span>
                  </div>
                  <div class="order_info_li">
                    <span>数量</span>
                    <span>*{{item.num}}</span>
                  </div>
                  <!-- <div class="order_info_li" v-if="item.reduceprice">

                                        <span v-if="item.equity_type==2">折扣</span>
                                        <span v-if="item.equity_type==3">抵扣</span>
                                        <span v-if="item.equity_type==4">优惠金额</span>
                                        <span>-￥{{item.reduceprice | filterMoney}}</span>
                                    </div> -->
                  <div class="order_info_li" v-if="item.equity_type!=1">
                    <span v-if="item.equity_type==3">次卡抵扣</span>
                    <span v-if="item.equity_type==2">充值卡折扣</span>
                    <span class="order-label2" v-if="item.equity_type!=4">
                      -￥{{item.reduceprice | formatMark}}
                    </span>
                  </div>
                  <div class="order_info_li" v-if="item.equity_type==4">
                    <span v-if="item.equity_type==4">优惠金额</span>
                    <span
                      v-if="item.reduceprice && item.reduceprice.indexOf('-')==-1"
                    >
                      +￥{{item.reduceprice | formatMark}}
                    </span>
                    <span
                      class="order-label2"
                      v-if="item.reduceprice && item.reduceprice.indexOf('-')!=-1"
                    >
                      -￥{{item.reduceprice | formatMark}}
                    </span>
                  </div>
                  <div class="order_info_li">
                    <span>小计</span>
                    <span>￥{{item.Subtotal}}</span>
                  </div>
                </div>
              </li>
            </ul>
            <app-heading
              v-if="orderDetails.presentData && orderDetails.presentData.length>0"
              title="赠送明细"
            ></app-heading>
            <ul class="order_info_ul border-margin" style="border-bottom: 0">
              <li
                v-for="(item,index) in orderDetails.presentData"
                class="order_info_wrap"
              >
                <div class="order_info_index">{{index + 1}}</div>
                <div class="order_item" style="margin-bottom: 20px">
                  <div class="order_info_li">
                    <span>
                      <!-- <span>{{item.name}}</span>
                                            <span v-if="item.sku_name">| {{item.sku_name}}</span> -->
                      <span v-if="item.itemType==1">{{item.name}}（服务）</span>
                      <span v-if="item.itemType==2 && !item.sku_name">
                        {{item.name}}（产品）
                      </span>
                      <span v-if="item.itemType==2 && item.sku_name">
                        {{item.name}}
                      </span>
                      <span v-if="item.sku_name">
                        | {{item.sku_name}}（产品）
                      </span>
                    </span>
                    <span class="order-label2">
                      ￥{{item.price | filterMoney}}
                    </span>
                  </div>
                  <div class="order_info_li">
                    <span>数量</span>
                    <span class="order-label2">*{{item.num}}</span>
                  </div>
                  <div class="order_info_li">
                    <span>赠送状态</span>
                    <span class="order-label2" v-if="item.status==0">
                      仅选择
                    </span>
                    <span class="order-label2" v-if="item.status==1">
                      已赠送
                    </span>
                    <span class="order-label2" v-if="item.status==2">
                      已退回
                    </span>
                  </div>
                </div>
              </li>
            </ul>
            <ul class="order_info_ul border-margin">
              <li class="order_info_li">
                <span>合计</span>
                <span>￥{{receivableing | filterMoney}}</span>
              </li>
              <li
                class="order_info_li"
                v-if="kd_xinxi_list.member_counpon_money!=0 && kd_xinxi_list.member_coupon!=0"
              >
                <span>优惠</span>
                <span>
                  -￥{{kd_xinxi_list.member_counpon_money | filterMoney}}
                </span>
              </li>
              <li class="order_info_li" v-if="kd_xinxi_list.dismoney">
                <span>充值卡折扣</span>
                <span v-if="kd_xinxi_list.dismoney">
                  -￥{{kd_xinxi_list.dismoney | filterMoney}}
                </span>
              </li>
              <li class="order_info_li" v-if="kd_xinxi_list.deduction">
                <span>次卡抵扣</span>
                <span v-if="kd_xinxi_list.deduction">
                  -￥{{kd_xinxi_list.deduction | filterMoney}}
                </span>
              </li>
              <li class="order_info_li" v-if="kd_xinxi_list.manually">
                <span>优惠金额</span>
                <span v-if="kd_xinxi_list.manually>0">
                  -￥{{orderDetails.manuallys}}
                </span>
                <span v-else>+￥{{orderDetails.manuallys}}</span>
              </li>
              <li class="order_info_li" v-if="orderDetails.small_change_money">
                <span>抹零</span>
                <span v-if="orderDetails.small_change_money">
                  -￥{{orderDetails.small_change_money | filterMoney}}
                </span>
              </li>
              <li class="order_info_li" v-if="kd_xinxi_list.net_receipts>0">
                <span>预约定金</span>
                <span v-if="kd_xinxi_list.net_receipts">
                  ￥{{kd_xinxi_list.net_receipts | filterMoney}}
                </span>
              </li>
              <!-- <li class="order_info_li" v-if="isDebtFlag">
                                <span>合计</span>
                                <span>￥{{orderDetails.receivable}}</span>
                            </li> -->
              <li class="order_info_li" v-if="isDebtFlag">
                <span>已付款</span>
                <span>-￥{{orderDetails.alreadyPay | filterMoney}}</span>
              </li>
              <li class="order_info_li" v-if="isDebtMoney">
                <span>欠款</span>
                <span>￥{{debtForm.debtMoney | filterMoney}}</span>
              </li>
              <li class="order_info_li">
                <span v-show="endPay==0">待支付</span>
                <span v-show="endPay==1">已支付</span>
                <!--<span v-if="billToPay==1" >￥{{kd_xinxi_list.toBePay | filterMoney}}</span>-->
                <span v-if="!isDebtMoney">
                  ￥{{kd_xinxi_list.toBePay | filterMoney}}
                </span>
                <span v-if="isDebtMoney">￥{{debtForm.payMoney || 0.00}}</span>
              </li>
            </ul>
            <!--<div class="code-attention">-->
            <!--<div class="shoucang_img">-->
            <!--<img src="../images/dianpu_shoucang.png" alt="">-->
            <!--</div>-->
            <!--<div class="shoucang_font">扫码收藏店铺，享更多优惠哦！</div>-->

            <!--</div>-->
          </div>
          <div class="left_but">
            <div class="wait_pay">
              <span v-show="endPay==0">待支付</span>
              <span v-show="endPay==1">已支付</span>
            </div>

            <!--<div v-if="billToPay==1 || billToPay==2" class="wait_money">￥{{kd_xinxi_list.toBePay | filterMoney}}</div>-->
            <!--<div class="wait_money">￥{{showMoneys}}</div>-->
            <div class="wait_money" v-if="!isDebtMoney">
              ￥{{kd_xinxi_list.toBePay | filterMoney}}
            </div>
            <div class="wait_money" v-if="isDebtMoney">
              ￥{{debtForm.payMoney || 0.00}}
            </div>
          </div>
        </div>

        <div v-show="endPay==0" class="right_main">
          <ul class="cash-register-type" v-if="kd_xinxi_list.toBePay>=0">
            <li
              v-if="isCard && kd_xinxi_list_buyer && kd_xinxi_list_buyer.id!=0 && payCardInfo.length>0 && isPayStatus==0"
              class="pay-type"
              :class="isPay==0?'pay-typeActive':''"
              @click="bindPay(0)"
            >
              会员余额
            </li>
            <li
              class="pay-type"
              :class="isPay==1?'pay-typeActive':''"
              @click="bindPay(1)"
            >
              微信/支付宝
            </li>
            <li
              class="pay-type"
              :class="isPay==2?'pay-typeActive':''"
              @click="bindPay(2)"
            >
              现金
            </li>
            <li
              class="pay-type"
              :class="isPay==4?'pay-typeActive':''"
              @click="bindPay(4)"
            >
              自定义记账
            </li>
          </ul>
          <div class="debt">
            <div
              class="iconfont iconjizhang"
              v-if="(kd_xinxi_list.type==1 || kd_xinxi_list.type==2 || kd_xinxi_list.type==3 || kd_xinxi_list.type==5) && kd_xinxi_list.vip_id"
              @click="toDebt"
              style="color: #b9bccd; font-size: 24px"
            ></div>
          </div>
          <ul
            class="cash-register-type"
            v-if="kd_xinxi_list.net_receipts>0 && kd_xinxi_list.toBePay<0"
          >
            <li class="pay-type pay-typeActive">定金退还</li>
          </ul>

          <el-row
            class="pay-key-wrap"
            v-if="isPay==2"
            type="flex"
            justify="center"
          >
            <el-col :sm="24" :md="22" :lg="18">
              <div
                style="
                  display: -webkit-inline-box;
                  height: 53px;
                  line-height: 53px;
                  width: 100%;
                "
              >
                <div class="zj_show_price1">
                  <span class="zj_show_price_font1">实收</span>
                  <div class="zj_show_price_font2">
                    <span>￥</span>
                    <!-- <input type="text" v-if="billToPay==1 || billToPay==2" ref="actualHarvest" v-model.trim="composeMoney" @keyup.enter="payTheBill" @input="manualPrice"> -->
                    <input
                      type="text"
                      ref="actualHarvest"
                      v-model.trim="cz_shou_qian"
                      @keyup.enter="payTheBill"
                      @input="manualPrice"
                    />
                  </div>
                  <!--<span v-if="billToPay==1" class="zj_font2_label">{{kd_xinxi_list.toBePay | filterMoney}}</span>-->
                  <span class="zj_font2_label">{{kd_shishou}}</span>
                </div>
                <div style="clear: both; display: inline">
                  <!-- <el-switch class="switchStyle" v-model="zeroMwitch" active-color="#7958b5" active-text="抹零"
                                    inactive-color="#e8e4f3" inactive-text="关">
                                   </el-switch> -->
                  <el-button
                    type="primary"
                    style="height: 52px; width: 94px; margin-bottom: 4px"
                    v-if="!isReturnZero"
                    @click="returnZero(0)"
                  >
                    抹零
                  </el-button>
                  <el-button
                    type="primary"
                    style="height: 52px; width: 94px; margin-bottom: 4px"
                    v-if="isReturnZero"
                    @click="returnZero(1)"
                  >
                    已抹零
                  </el-button>
                </div>
              </div>
              <div class="zj_price_menu">
                <div class="zj_menu_num">
                  <ul>
                    <li
                      v-for="(value,index) in zj_price_menu"
                      style="display: flex"
                    >
                      <p
                        :class="value.key1 == '00' ? 'zj_two_zero' : 'zj_num_1'"
                        @click="cz_input_num(index,value1)"
                        :id="value1"
                      >
                        {{value.key1}}
                      </p>
                      <p
                        :class="value.key2 == '0' ? 'zj_one_zero' : 'zj_num_2'"
                        @click="cz_input_num(index,value2)"
                        :id="value2"
                      >
                        {{value.key2}}
                      </p>
                      <p
                        :class="value.key3 == '.' ? 'zj_xiao_dian' : 'zj_num_3'"
                        @click="cz_input_num(index,value3)"
                        :id="value3"
                      >
                        {{value.key3}}
                      </p>
                    </li>
                  </ul>
                </div>
                <div class="zj_menu_take">
                  <ul>
                    <li class="zj_menu_take_font1" @click="cz_del_all">C</li>
                    <li
                      class="iconfont iconshanchuyigeshuzi"
                      @click="cz_del_one"
                    ></li>
                    <li class="zj_menu_take_font2" @click="payTheBill">收款</li>
                  </ul>
                </div>
              </div>
            </el-col>
          </el-row>

          <div v-if="isPay==1" class="kd_weixin">
            <div>扫码枪收款</div>
            <div>扫一扫用户付款码</div>
            <div>
              <img src="images/saomaqiang.png" width="180px" />
            </div>
            <div>
              <input
                v-model.trim="paymentCode"
                type="text"
                ref="paymentCode"
                maxlength="18"
                placeholder="扫描或输入付款码"
                @keyup.enter="phonePay"
              />
            </div>
            <div>
              <img
                src="images/scan_code.png"
                style="width: 50px; margin-bottom: 10px"
              />
              <div class="kd_bottom">扫码枪收款</div>
            </div>
          </div>

          <div v-if="isPay==0">
            <div class="setHeight">
              <div class="right_font3_title" v-if="sellCardType==1">
                请选择余额账户
              </div>
              <ul class="offer-wrap" style="margin-bottom: 15px">
                <li
                  class="paymentOffer-list checked"
                  v-for="(item,index) in requisiteCard"
                >
                  <p class="offer-name">{{item.card_info}}</p>
                  <p class="offer-faceValue">
                    余额： ￥{{item.residuebalance | filterMoney}}
                  </p>
                  <!-- <p class="offer-faceValue">
                                        <span>本金:￥{{item.capitalbalance | filterMoney}}</span>
                                        <span>赠额:￥{{item.presentbalance | filterMoney}}</span>
                                    </p> -->
                  <p style="font-weight: bold; font-size: 16px">
                    <span>
                      将支付 :&emsp;
                      <span style="color: #3363FF">
                        ￥{{item.realPay | filterMoney}}
                      </span>
                    </span>
                  </p>
                </li>
              </ul>
              <ul class="offer-wrap" v-if="sellCardType==1">
                <li
                  class="paymentOffer-list"
                  v-for="(item,index) in payCardInfo"
                  :class="item.checked?'checked':''"
                  v-if="item.cardid>=0"
                  @click="bindPayCard(item,index)"
                >
                  <p class="offer-name">{{item.card_info}}</p>
                  <p class="offer-faceValue">
                    ￥{{item.residuebalance | filterMoney}}
                  </p>
                  <!-- <p>
                                        <span>本金:￥{{item.capitalbalance | filterMoney}}</span>
                                        <span>赠额:￥{{item.presentbalance | filterMoney}}</span>
                                    </p> -->
                </li>
              </ul>
            </div>
            <div class="right_font3_button">
              <el-button type="primary" size="medium" @click="payTheBill">
                完成收款
              </el-button>
            </div>
          </div>

          <div class="return-money" v-if="isPay==3">
            <span style="padding-bottom: 8px">
              定金退还{{Math.abs(kd_xinxi_list.toBePay) | filterMoney}}元
            </span>
            <span class="">退还的定金按原路退回</span>
            <!-- <span class="return-money-confirm">确定</span> -->
            <el-button
              class="return-money-btn"
              type="primary"
              size="large"
              @click="returnPay"
            >
              确认
            </el-button>
          </div>

          <!-- 自定义记账 -->
          <el-row
            class="pay-key-wrap"
            v-if="isPay==4"
            type="flex"
            justify="center"
          >
            <el-col :sm="24" :md="22" :lg="18">
              <div style="text-align: center">
                <div class="">
                  <h3 style="margin: 16px 0">选择记账方式</h3>
                  <p style="margin: 16px 0">
                    如不是通过会员余额、现金、微信/支付宝或刷脸完成支付的
                  </p>
                  <p style="margin: 16px 0">可选择以下自定义记账方式完成订单</p>
                </div>
              </div>
              <div
                style="
                  display: -webkit-inline-box;
                  line-height: 53px;
                  width: 100%;
                "
              >
                <div>
                  <span style="font-size: 18px">支付方式:</span>
                  <br />
                  <el-tag
                    v-for="store of customizePayType"
                    v-bind:key="store.payType"
                    :class="store.payType==custmizePayType?'pay_type_tag pay_type_active':'pay_type_tag'"
                    @click.native="chooseCustmizePayType(store.payType)"
                  >
                    {{store.name}}
                  </el-tag>
                </div>
              </div>
              <div class="pay_type_div">
                <div class="pay_type_money" @click="custmizePayTypeBill">
                  收款
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!--支付成功-->
        <div v-show="endPay==1" class="right_main">
          <div class="pay-success">
            <i class="el-icon-success successIcon"></i>
            <h2>收款完成</h2>
            <div class="success-btn printBtn" @click="bindPrint">打印小票</div>
            <div
              v-if="!kd_xinxi_list_buyer.phone"
              class="success-btn newBilling"
              @click="newBilling"
            >
              新建服务开单
            </div>
            <div
              v-else
              class="success-btn newBilling"
              @click="newMemberBilling"
            >
              新建会员开单
            </div>
            <div
              class="success-btn"
              @click="modifyEmployeePerformance"
              v-if="loginModify == 1&&modifyEmployeeStatus != 0"
            >
              修改员工业绩
            </div>
          </div>
        </div>
      </div>
      <!--批量还款-->
      <div class="cash-register" v-if="(orderNumber.indexOf(',')!=-1)">
        <div class="left_main">
          <div class="left_mid">
            <template v-for="(order,key) in debtDataList.orderList">
              <app-heading
                title="订单信息"
                style="margin-top: 20px"
              ></app-heading>
              <ul class="order_info_ul">
                <li class="order_info_li">
                  <span>订单编号</span>
                  <span>{{order.order_number}}</span>
                </li>
                <li class="order_info_li">
                  <span>下单时间</span>
                  <span>{{order.order_time}}</span>
                </li>
                <li
                  v-if="order.buyer && order.buyer.id!=0"
                  class="order_info_li"
                >
                  <span>会员编号</span>
                  <span v-if="kd_xinxi_list.buyer">
                    {{order.buyer.member_number}}
                  </span>
                </li>
                <li class="order_info_li">
                  <span>下单人</span>
                  <span v-if="order.buyer && order.buyer.id!=0">
                    {{order.buyer.member_name}}
                  </span>
                  <span v-else>普通顾客</span>
                </li>
                <li class="order_info_li">
                  <span>收银员</span>
                  <span>{{order.nickname}}</span>
                </li>
              </ul>

              <app-heading title="消费明细"></app-heading>
              <ul class="order_info_ul border-margin" style="border-bottom: 0">
                <li
                  class="order_info_wrap"
                  v-for="(item,index) in order.orderDetails"
                >
                  <div class="order_info_index">{{index+1}}</div>
                  <div class="order_item">
                    <div class="order_info_li">
                      <span>{{item.name}}</span>
                      <span>￥{{item.price | filterMoney}}</span>
                    </div>
                    <div class="order_info_li">
                      <span>数量</span>
                      <span>*{{item.num}}</span>
                    </div>
                    <div class="order_info_li" v-if="item.equity_type!=1">
                      <span v-if="item.equity_type==3">次卡抵扣</span>
                      <span v-if="item.equity_type==2">充值卡折扣</span>
                      <span class="order-label2" v-if="item.equity_type!=4">
                        -￥{{item.reduceprice | formatMark | filterMoney}}
                      </span>
                    </div>
                    <div class="order_info_li" v-if="item.equity_type==4">
                      <span v-if="item.equity_type==4">优惠金额</span>
                      <span v-if="item.reduceprice && item.reduceprice<0">
                        +￥{{item.reduceprice | formatMark | filterMoney}}
                      </span>
                      <span
                        class="order-label2"
                        v-if="item.reduceprice && item.reduceprice>0"
                      >
                        -￥{{item.reduceprice | formatMark | filterMoney}}
                      </span>
                    </div>
                    <div class="order_info_li">
                      <span>小计</span>
                      <span>￥{{item.smallTotal | filterMoney}}</span>
                    </div>
                  </div>
                </li>
              </ul>
              <app-heading
                v-if="order.presentData && order.presentData.length>0"
                title="赠送明细"
              ></app-heading>
              <ul class="order_info_ul border-margin" style="border-bottom: 0">
                <li
                  v-for="(item,index) in order.presentData"
                  class="order_info_wrap"
                >
                  <div class="order_info_index">{{index + 1}}</div>
                  <div class="order_item" style="margin-bottom: 20px">
                    <div class="order_info_li">
                      <span>
                        <!-- <span>{{item.name}}</span>
                                            <span v-if="item.sku_name">| {{item.sku_name}}</span> -->
                        <span v-if="item.itemType==1">
                          {{item.name}}（服务）
                        </span>
                        <span v-if="item.itemType==2 && !item.sku_name">
                          {{item.name}}（产品）
                        </span>
                        <span v-if="item.itemType==2 && item.sku_name">
                          {{item.name}}
                        </span>
                        <span v-if="item.sku_name">
                          | {{item.sku_name}}（产品）
                        </span>
                      </span>
                      <span class="order-label2">
                        ￥{{item.price | filterMoney}}
                      </span>
                    </div>
                    <div class="order_info_li">
                      <span>数量</span>
                      <span class="order-label2">*{{item.num}}</span>
                    </div>
                    <div class="order_info_li">
                      <span>赠送状态</span>
                      <span class="order-label2" v-if="item.status==0">
                        仅选择
                      </span>
                      <span class="order-label2" v-if="item.status==1">
                        已赠送
                      </span>
                      <span class="order-label2" v-if="item.status==2">
                        已退回
                      </span>
                    </div>
                  </div>
                </li>
              </ul>
              <ul class="order_info_ul border-margin">
                <li class="order_info_li">
                  <span>合计</span>
                  <span>￥{{order.receivable | filterMoney}}</span>
                </li>
                <li
                  class="order_info_li"
                  v-if="order.member_counpon_money!=0 && order.member_coupon!=0"
                >
                  <span>优惠</span>
                  <span>-￥{{order.member_counpon_money | filterMoney}}</span>
                </li>
                <li class="order_info_li" v-if="order.dismoney">
                  <span>充值卡折扣</span>
                  <span v-if="order.dismoney">
                    -￥{{order.dismoney | filterMoney}}
                  </span>
                </li>
                <li class="order_info_li" v-if="order.deduction">
                  <span>次卡抵扣</span>
                  <span v-if="order.deduction">
                    -￥{{order.deduction | filterMoney}}
                  </span>
                </li>
                <li class="order_info_li" v-if="order.manually">
                  <span>优惠金额</span>
                  <span v-if="order.manually>0">
                    -￥{{order.manually | filterMoney}}
                  </span>
                  <span v-else>+￥{{order.manually | filterMoney}}</span>
                </li>
                <li class="order_info_li" v-if="order.small_change_money">
                  <span>抹零</span>
                  <span v-if="order.small_change_money">
                    -￥{{order.small_change_money | filterMoney}}
                  </span>
                </li>
                <li class="order_info_li" v-if="order.net_receipts>0">
                  <span>预约定金</span>
                  <span v-if="order.net_receipts">
                    ￥{{order.net_receipts | filterMoney}}
                  </span>
                </li>
                <li class="order_info_li" v-if="isDebtFlag">
                  <span>已付款</span>
                  <span>
                    -￥{{(order.toBePay - order.debtMoney) | filterMoney}}
                  </span>
                </li>
                <li class="order_info_li" v-if="isDebtFlag">
                  <span>欠款</span>
                  <span>￥{{order.debtMoney | filterMoney}}</span>
                </li>
                <li class="order_info_li">
                  <span v-show="endPay==0">待支付</span>
                  <span v-show="endPay==1">已支付</span>
                  <span v-if="!isDebtMoney">
                    ￥{{order.debtMoney | filterMoney}}
                  </span>
                  <span v-if="isDebtMoney">
                    ￥{{order.debtMoney | filterMoney}}
                  </span>
                </li>
              </ul>
            </template>
          </div>
          <div class="left_but">
            <div class="wait_pay">
              <span v-show="endPay==0">待支付</span>
              <span v-show="endPay==1">已支付</span>
            </div>
            <div class="wait_money">
              ￥{{debtDataList.payMoney | filterMoney}}
            </div>
          </div>
        </div>
        <!-- 普通单据 -->
        <div
          v-if="(orderNumber.indexOf(',')==-1)"
          v-show="endPay==0"
          class="right_main"
        >
          <ul class="cash-register-type" v-if="kd_xinxi_list.payMoney>=0">
            <li
              v-if="0 && kd_xinxi_list.buyer.id && kd_xinxi_list.payCardInfo.length>0 && sellCardType==1"
              class="pay-type"
              :class="isPay==0?'pay-typeActive':''"
              @click="bindPay(0)"
            >
              会员余额
            </li>
            <li
              class="pay-type"
              :class="isPay==1?'pay-typeActive':''"
              @click="bindPay(1)"
            >
              微信/支付宝
            </li>
            <li
              class="pay-type"
              :class="isPay==2?'pay-typeActive':''"
              @click="bindPay(2)"
            >
              现金
            </li>
            <li
              class="pay-type"
              :class="isPay==4?'pay-typeActive':''"
              @click="bindPay(4)"
            >
              自定义记账
            </li>
          </ul>
          <el-row
            class="pay-key-wrap"
            v-if="isPay==2"
            type="flex"
            justify="center"
          >
            <el-col :sm="24" :md="22" :lg="18">
              <div
                style="
                  display: -webkit-inline-box;
                  height: 53px;
                  line-height: 53px;
                  width: 100%;
                "
              >
                <div class="zj_show_price1">
                  <span class="zj_show_price_font1">实收</span>
                  <div class="zj_show_price_font2">
                    <span>￥</span>
                    <input
                      type="text"
                      ref="actualHarvest"
                      v-model.trim="cz_shou_qian"
                      @keyup.enter="payTheBill"
                      @input="manualPrice"
                    />
                  </div>
                  <span class="zj_font2_label">{{kd_shishou}}</span>
                </div>
              </div>
              <div class="zj_price_menu">
                <div class="zj_menu_num">
                  <ul>
                    <li
                      v-for="(value,index) in zj_price_menu"
                      style="display: flex"
                    >
                      <p
                        :class="value.key1 == '00' ? 'zj_two_zero' : 'zj_num_1'"
                        @click="cz_input_num(index,value1)"
                        :id="value1"
                      >
                        {{value.key1}}
                      </p>
                      <p
                        :class="value.key2 == '0' ? 'zj_one_zero' : 'zj_num_2'"
                        @click="cz_input_num(index,value2)"
                        :id="value2"
                      >
                        {{value.key2}}
                      </p>
                      <p
                        :class="value.key3 == '.' ? 'zj_xiao_dian' : 'zj_num_3'"
                        @click="cz_input_num(index,value3)"
                        :id="value3"
                      >
                        {{value.key3}}
                      </p>
                    </li>
                  </ul>
                </div>
                <div class="zj_menu_take">
                  <ul>
                    <li class="zj_menu_take_font1" @click="cz_del_all">C</li>
                    <li
                      class="iconfont iconshanchuyigeshuzi"
                      @click="cz_del_one"
                    ></li>
                    <li class="zj_menu_take_font2" @click="payTheBill">收款</li>
                  </ul>
                </div>
              </div>
            </el-col>
          </el-row>

          <div v-if="isPay==1" class="kd_weixin">
            <div>扫码枪收款</div>
            <div>扫一扫用户付款码</div>
            <div>
              <img src="images/saomaqiang.png" width="180px" />
            </div>
            <div>
              <input
                v-model.trim="paymentCode"
                type="text"
                ref="paymentCode"
                maxlength="18"
                placeholder="扫描或输入付款码"
                @keyup.enter="phonePay"
              />
            </div>
            <div>
              <img
                src="images/scan_code.png"
                style="width: 50px; margin-bottom: 10px"
              />
              <div class="kd_bottom">扫码枪收款</div>
            </div>
          </div>

          <div v-if="isPay==0">
            <div class="setHeight">
              <div class="right_font3_title" v-if="sellCardType==1">
                请选择余额账户
              </div>
              <ul class="offer-wrap" v-if="sellCardType==1">
                <li
                  class="paymentOffer-list"
                  v-for="(item,index) in payCardInfo"
                  :class="item.checked?'checked':''"
                  v-if="item.cardid>=0"
                  @click="bindPayCard(item,index)"
                >
                  <p class="offer-name">{{item.card_info}}</p>
                  <p class="offer-faceValue">
                    ￥{{item.residuebalance | filterMoney}}
                  </p>
                  <p
                    style="font-weight: bold; font-size: 16px"
                    v-if="item.checked"
                  >
                    <span>
                      将支付 :&emsp;
                      <span style="color: #3363FF">
                        ￥{{item.realPay | filterMoney}}
                      </span>
                    </span>
                  </p>
                </li>
              </ul>
            </div>
            <div class="right_font3_button">
              <el-button type="primary" size="medium" @click="payTheBill">
                完成收款
              </el-button>
            </div>
          </div>

          <div class="return-money" v-if="isPay==3">
            <span style="padding-bottom: 8px">
              定金退还{{Math.abs(kd_xinxi_list.toBePay) | filterMoney}}元
            </span>
            <span class="">退还的定金按原路退回</span>
            <!-- <span class="return-money-confirm">确定</span> -->
            <el-button
              class="return-money-btn"
              type="primary"
              size="large"
              @click="returnPay"
            >
              确认
            </el-button>
          </div>

          <!-- 自定义记账 -->
          <el-row
            class="pay-key-wrap"
            v-if="isPay==4"
            type="flex"
            justify="center"
          >
            <el-col :sm="24" :md="22" :lg="18">
              <div style="text-align: center">
                <div class="">
                  <h3 style="margin: 16px 0">选择记账方式</h3>
                  <p style="margin: 16px 0">
                    如不是通过会员余额、现金、微信/支付宝或刷脸完成支付的
                  </p>
                  <p style="margin: 16px 0">可选择以下自定义记账方式完成订单</p>
                </div>
              </div>
              <div
                style="
                  display: -webkit-inline-box;
                  line-height: 53px;
                  width: 100%;
                "
              >
                <div>
                  <span style="font-size: 18px">支付方式:</span>
                  <br />
                  <el-tag
                    v-for="store of customizePayType"
                    v-bind:key="store.payType"
                    :class="store.payType==custmizePayType?'pay_type_tag pay_type_active':'pay_type_tag'"
                    @click.native="chooseCustmizePayType(store.payType)"
                  >
                    {{store.name}}
                  </el-tag>
                </div>
              </div>
              <div class="pay_type_div">
                <div class="pay_type_money" @click="custmizePayTypeBill">
                  收款
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <!-- 批量还款 -->
        <div
          v-if="(orderNumber.indexOf(',')!=-1)"
          v-show="endPay==0"
          class="right_main"
        >
          <ul class="cash-register-type" v-if="debtDataList.payMoney>=0">
            <li
              v-if="0 && debtDataList.buyer.id && debtDataList.payCardInfo.length>0 && sellCardType==1"
              class="pay-type"
              :class="isPay==0?'pay-typeActive':''"
              @click="bindPay(0)"
            >
              会员余额
            </li>
            <li
              class="pay-type"
              :class="isPay==1?'pay-typeActive':''"
              @click="bindPay(1)"
            >
              微信/支付宝
            </li>
            <li
              class="pay-type"
              :class="isPay==2?'pay-typeActive':''"
              @click="bindPay(2)"
            >
              现金
            </li>
            <li
              class="pay-type"
              :class="isPay==4?'pay-typeActive':''"
              @click="bindPay(4)"
            >
              自定义记账
            </li>
          </ul>
          <el-row
            class="pay-key-wrap"
            v-if="isPay==2"
            type="flex"
            justify="center"
          >
            <el-col :sm="24" :md="22" :lg="18">
              <div
                style="
                  display: -webkit-inline-box;
                  height: 53px;
                  line-height: 53px;
                  width: 100%;
                "
              >
                <div class="zj_show_price1">
                  <span class="zj_show_price_font1">实收</span>
                  <div class="zj_show_price_font2">
                    <span>￥</span>
                    <input
                      type="text"
                      ref="actualHarvest"
                      v-model.trim="cz_shou_qian"
                      @keyup.enter="payTheBill"
                      @input="manualPrice"
                    />
                  </div>
                  <span class="zj_font2_label">{{kd_shishou}}</span>
                </div>
              </div>
              <div class="zj_price_menu">
                <div class="zj_menu_num">
                  <ul>
                    <li
                      v-for="(value,index) in zj_price_menu"
                      style="display: flex"
                    >
                      <p
                        :class="value.key1 == '00' ? 'zj_two_zero' : 'zj_num_1'"
                        @click="cz_input_num(index,value1)"
                        :id="value1"
                      >
                        {{value.key1}}
                      </p>
                      <p
                        :class="value.key2 == '0' ? 'zj_one_zero' : 'zj_num_2'"
                        @click="cz_input_num(index,value2)"
                        :id="value2"
                      >
                        {{value.key2}}
                      </p>
                      <p
                        :class="value.key3 == '.' ? 'zj_xiao_dian' : 'zj_num_3'"
                        @click="cz_input_num(index,value3)"
                        :id="value3"
                      >
                        {{value.key3}}
                      </p>
                    </li>
                  </ul>
                </div>
                <div class="zj_menu_take">
                  <ul>
                    <li class="zj_menu_take_font1" @click="cz_del_all">C</li>
                    <li
                      class="iconfont iconshanchuyigeshuzi"
                      @click="cz_del_one"
                    ></li>
                    <li class="zj_menu_take_font2" @click="payTheBill">收款</li>
                  </ul>
                </div>
              </div>
            </el-col>
          </el-row>

          <div v-if="isPay==1" class="kd_weixin">
            <div>扫码枪收款</div>
            <div>扫一扫用户付款码</div>
            <div>
              <img src="images/saomaqiang.png" width="180px" />
            </div>
            <div>
              <input
                v-model.trim="paymentCode"
                type="text"
                ref="paymentCode"
                maxlength="18"
                placeholder="扫描或输入付款码"
                @keyup.enter="phonePay"
              />
            </div>
            <div>
              <img
                src="images/scan_code.png"
                style="width: 50px; margin-bottom: 10px"
              />
              <div class="kd_bottom">扫码枪收款</div>
            </div>
          </div>

          <div v-if="isPay==0">
            <div class="setHeight">
              <div class="right_font3_title" v-if="sellCardType==1">
                请选择余额账户
              </div>
              <ul class="offer-wrap" v-if="sellCardType==1">
                <li
                  class="paymentOffer-list"
                  v-for="(item,index) in payCardInfo"
                  :class="item.checked?'checked':''"
                  v-if="item.cardid>=0"
                  @click="bindPayCard(item,index)"
                >
                  <p class="offer-name">{{item.card_info}}</p>
                  <p class="offer-faceValue">
                    ￥{{item.residuebalance | filterMoney}}
                  </p>
                  <p
                    style="font-weight: bold; font-size: 16px"
                    v-if="item.checked"
                  >
                    <span>
                      将支付 :&emsp;
                      <span style="color: #3363FF">
                        ￥{{item.realPay | filterMoney}}
                      </span>
                    </span>
                  </p>
                </li>
              </ul>
            </div>
            <div class="right_font3_button">
              <el-button type="primary" size="medium" @click="payTheBill">
                完成收款
              </el-button>
            </div>
          </div>

          <!-- 自定义记账 -->
          <el-row
            class="pay-key-wrap"
            v-if="isPay==4"
            type="flex"
            justify="center"
          >
            <el-col :sm="24" :md="22" :lg="18">
              <div style="text-align: center">
                <div class="">
                  <h3 style="margin: 16px 0">选择记账方式</h3>
                  <p style="margin: 16px 0">
                    如不是通过会员余额、现金、微信/支付宝或刷脸完成支付的
                  </p>
                  <p style="margin: 16px 0">可选择以下自定义记账方式完成订单</p>
                </div>
              </div>
              <div
                style="
                  display: -webkit-inline-box;
                  line-height: 53px;
                  width: 100%;
                "
              >
                <div>
                  <span style="font-size: 18px">支付方式:</span>
                  <br />
                  <el-tag
                    v-for="store of customizePayType"
                    v-bind:key="store.payType"
                    :class="store.payType==custmizePayType?'pay_type_tag pay_type_active':'pay_type_tag'"
                    @click.native="chooseCustmizePayType(store.payType)"
                  >
                    {{store.name}}
                  </el-tag>
                </div>
              </div>
              <div class="pay_type_div">
                <div class="pay_type_money" @click="custmizePayTypeBill">
                  收款
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!--支付成功-->
        <div v-show="endPay==1" class="right_main">
          <div class="pay-success">
            <i class="el-icon-success successIcon"></i>
            <h2>收款完成</h2>
            <div class="success-btn printBtn" @click="bindPrint">打印小票</div>
            <div
              v-if="!kd_xinxi_list_buyer.phone"
              class="success-btn newBilling"
              @click="newBilling"
            >
              新建服务开单
            </div>
            <div
              v-else
              class="success-btn newBilling"
              @click="newMemberBilling"
            >
              新建会员开单
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>

  <!-- 会员消费确认 -->
  <el-dialog
    title="会员消费确认"
    :visible.sync="vipComsumeConfirm"
    width="360px"
    top="30vh"
  >
    <div
      style="
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0px 15px;
      "
    >
      <div>
        <span style="cursor: pointer" @click="vipPassCodeComfirmAlert">
          <img
            style="height: 80px; width: 80px"
            src="./images/vip_scan.png"
            alt=""
          />
          <span style="display: block; margin-top: 8px; text-align: center">
            会员动态码
          </span>
        </span>
        <el-dialog
          width="360px"
          top="30vh"
          title="会员动态码"
          :visible.sync="vipPassCodeComfirm"
          append-to-body
        >
          <el-input
            type="text"
            v-model="vipPassCode"
            :autofocus="true"
            placeholder="请扫描会员动态码"
            @keyup.enter.native="vipPasswordCodeComfirm"
            ref="inputVipPasswordCode"
          ></el-input>
          <el-button
            type="primary"
            style="margin: 10px 0px 0px 78%"
            @click="vipPasswordCodeComfirm"
          >
            确认
          </el-button>
        </el-dialog>
      </div>
      <div>
        <span style="cursor: pointer" @click="vipPasswordAlert">
          <img
            style="height: 80px; width: 80px"
            src="./images/vip_pass.png"
            alt=""
          />
          <span style="display: block; margin-top: 8px; text-align: center">
            会员密码
          </span>
        </span>
        <el-dialog
          width="360px"
          top="30vh"
          title="会员密码"
          :visible.sync="vipPassComfirm"
          append-to-body
        >
          <el-input
            type="password"
            v-model="vipPassword"
            :autofocus="true"
            placeholder="请输入会员密码"
            @keyup.enter.native="vipPasswordComfirm"
            ref="inputVipPassword"
          ></el-input>
          <el-button
            type="primary"
            style="margin: 10px 0px 0px 78%"
            @click="vipPasswordComfirm"
          >
            确认
          </el-button>
        </el-dialog>
      </div>
    </div>
  </el-dialog>

  <!-- 会员欠款 -->
  <el-dialog
    title="欠款"
    class="select-offer"
    :visible.sync="isDebt"
    width="500px"
    top="8vh"
  >
    <el-form
      ref="debtForm"
      :model="debtForm"
      label-width="100px"
      style="width: 80%; margin: 0 auto"
    >
      <el-form-item label="订单金额:">
        <span>￥{{debtForm.orderMoney | filterMoney}}</span>
        <!-- <el-input v-model="debtForm.orderMoney" disabled></el-input> -->
      </el-form-item>
      <el-form-item label="本次支付:">
        <div style="display: flex; flex-direction: row; border: 1px solid #ccc">
          <div style="padding: 0px 13px; border-right: 1px solid #ccc">￥</div>
          <input
            style="outline: none; border: 0; padding-left: 12px"
            v-model="debtForm.payMoney"
            @input="handleDebtMoney"
          />
        </div>
      </el-form-item>
      <el-form-item label="欠款:">
        <!-- <el-input v-model="debtForm.orderMoney" disabled></el-input> -->
        <span>￥{{debtForm.debtMoney | filterMoney}}</span>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="cancelDebt">去支付</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>

  <!--默认打印-->
  <app-print-debt
    :store="userInfo"
    :order-info="kd_xinxi_list"
    :member="kd_xinxi_list_buyer"
    :pay-type="isPay"
    :pay-money="cz_shou_qian"
    :change-money="kd_shishou"
  ></app-print-debt>
  <!--打印样式-->
  <div
    class="printWrap"
    :style="{width:paperwidth +'px'}"
    ref="printorderstr"
    style="display: none; top: 150px; right: 0px"
  >
    <template v-for="(item,index) in printSet.set">
      <template v-if="item.name=='store'">
        <app-store :store-set="item.set" :store="userInfo"></app-store>
      </template>
      <template v-if="item.name=='header'">
        <app-header
          :header-set="item.set"
          :order-header="kd_xinxi_list"
        ></app-header>
      </template>
      <template v-if="item.name=='goods'">
        <app-goods
          :goods-set="item.set"
          :goods="orderDetails.orderInfo"
          direct="0"
        ></app-goods>
      </template>
      <!-- <template v-if="item.name=='vip'">
        <app-vip :vip-set="item.set" :member="kd_xinxi_list_buyer"></app-vip>
      </template> -->
      <!--<template v-if="item.name=='takegoods'">-->
      <!--<app-address :address-set="item.set"></app-address>-->
      <!--</template>-->
      <template v-if="item.name=='footer'">
        <app-footer-debt
          :footer-set="item.set"
          :order-footer="orderDetails"
          :pay-type="isPay"
          :pay-money="cz_shou_qian"
          :change-money="kd_shishou"
        ></app-footer-debt>
      </template>
      <template v-if="item.name=='line'">
        <app-line :line-set="item.set"></app-line>
      </template>
      <template v-if="item.name=='info'">
        <app-info :info-set="item.set"></app-info>
      </template>
      <template v-if="item.name=='text'">
        <app-text :text-set="item.set"></app-text>
      </template>
    </template>
  </div>
</div>
