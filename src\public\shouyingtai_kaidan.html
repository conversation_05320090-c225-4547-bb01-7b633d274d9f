<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />

    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>收银台开单</title>
    <link rel="stylesheet" href="./vue/element/<EMAIL>" />
    <link rel="stylesheet" href="./css/shouyingtai_kaidan.css" />
    <link rel="stylesheet" href="component/css/component.css" />
    <link rel="stylesheet" href="./css/chongzhi.css" />
    <link rel="stylesheet" href="./css/card.css" />
    <link rel="stylesheet" href="./css/css-comment.css" />
    <link
      rel="stylesheet"
      href="https://at.alicdn.com/t/font_1156348_j67g0vzn8ig.css"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="css/print.css" />
  </head>
  <body>
    <div id="app" v-cloak>
      <!--顶部导航条-->
      <!--主体-->
      <div
        class="main"
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255,255,255,0)"
      >
        <!--左侧导航条-->
        <div class="left">
          <div class="left_menu">
            <div
              class="left_menu_nav"
              :class="isactive1 == value.id ? 'addclass' : '' "
              @click="getmenu(key,value.id)"
              v-for="(value, key) in todos2"
            >
              <span>{{value.word}}</span>
            </div>
          </div>
        </div>
        <!--收银台开单-->
        <ul class="main-right" v-show="isactive1==0">
          <li style="display: flex; width: 100%">
            <!--选择订单内容-->
            <div class="server">
              <!--产品和服务-->
              <div class="server_chioce">
                <div class="server_center">
                  <el-radio-group
                    v-model="billingType"
                    size="medium"
                    @change="billingChangeType"
                  >
                    <el-radio-button label="0">服务</el-radio-button>
                    <el-radio-button label="1">产品</el-radio-button>
                    <el-radio-button
                      label="2"
                      v-if="memberInfo && memberInfo.id"
                    >
                      扣卡
                    </el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              <div class="search_menu" v-show="billingType==0">
                <div class="search_bor">
                  <div class="el-input el-input--suffix">
                    <!---->
                    <input
                      type="text"
                      autocomplete="off"
                      placeholder="请输入服务名称"
                      class="el-input__inner"
                      v-model.trim="search_keyword"
                      ref="search_keyword"
                      @keyup.enter.exact.stop="billingInquiryEnter"
                    />
                    <span class="el-input__suffix" @click="billingInquiryEnter">
                      <span class="el-input__suffix-inner">
                        <i class="el-input__icon el-icon-search"></i>
                      </span>
                    </span>
                  </div>
                </div>
                <!--选择标签-->
                <div class="chooseLabelWrap">
                  <ul
                    class="chooseLabel"
                    style="margin-right: 15px"
                    ref="chooseTag"
                  >
                    <li
                      v-for="(value,key) in cashier_open_order_service_label"
                      ref="chooseLiTag"
                      @click="bind_choice_server(value,key)"
                    >
                      <p
                        :class="isActiveServer == key ? 'bg_label' : '' "
                        class="open_order_search"
                      >
                        {{value.label_name}}
                      </p>
                    </li>
                  </ul>
                </div>
                <!--可选服务列表 uuai-->
                <div class="search_detail serverWrap" ref="serverWrap">
                  <div
                    v-infinite-scroll="loadMoreProduct"
                    infinite-scroll-disabled="isServerScroll"
                    infinite-scroll-distance="10"
                    infinite-scroll-immediate-check="isServerScroll"
                  >
                    <div
                      class="search_detail1"
                      v-for="(value,index) in cashier_open_order_service_name"
                      @click="bind_add_server(value,index)"
                    >
                      <div class="serach_detail_info">
                        <img
                          class="serach_detail_img"
                          :src="value.imgurl"
                          onerror="this.src='images/default.jpg'"
                        />
                      </div>
                      <div class="service_name-price">
                        <p class="serach_detail_info_font1 service_name">
                          {{value.service_name}}
                        </p>
                        <p
                          class="serach_detail_info_font2"
                          style="color: #999999"
                        >
                          ￥{{value.price}}
                        </p>
                      </div>
                    </div>

                    <!-- 加载效果 -->
                    <div v-if="busy" class="loadingtip">{{loadingtip}}</div>
                  </div>
                </div>
              </div>
              <!--产品和搜索-->
              <div class="search_menu" v-show="billingType==1">
                <div class="search_bor">
                  <div class="el-input el-input--suffix">
                    <input
                      type="text"
                      autocomplete="off"
                      placeholder="请输入产品名称"
                      class="el-input__inner"
                      v-model="search_product"
                      ref="search_product"
                      @input="setTime"
                      @keyup.enter.exact.stop="scanProductCode"
                    />
                    <span class="el-input__suffix" @click="billingInquiryEnter">
                      <span class="el-input__suffix-inner">
                        <i class="el-input__icon el-icon-search"></i>
                      </span>
                    </span>
                  </div>
                </div>
                <!--选择标签-->
                <div class="chooseLabelWrap">
                  <ul
                    class="chooseLabel"
                    style="margin-right: 15px"
                    ref="productUlTag"
                  >
                    <li
                      v-for="(value,key) in product_label"
                      @click="bind_choice_product(value,key)"
                      ref="productLiTag"
                    >
                      <p
                        :class="isActiveProduct == key ? 'bg_label' : '' "
                        class="open_order_search"
                      >
                        {{value.name}}
                      </p>
                    </li>
                  </ul>
                </div>
                <!--可选服务列表-->
                <div class="search_detail serverWrap" ref="productWrap">
                  <div
                    v-infinite-scroll="loadMoreCombo"
                    infinite-scroll-disabled="isComboScroll"
                    infinite-scroll-distance="10"
                    infinite-scroll-immediate-check="isComboScroll"
                  >
                    <div
                      class="search_detail1"
                      v-for="(value,index) in cashier_open_order_product_name"
                      @click="bind_add_product(value,index)"
                    >
                      <div class="serach_detail_info">
                        <img
                          class="serach_detail_img"
                          :src="value.imgarr[0]"
                          onerror="this.src='images/default.jpg'"
                        />
                      </div>
                      <div class="service_name-price">
                        <p class="serach_detail_info_font1 service_name">
                          {{value.product_name}}
                        </p>
                        <p
                          class="serach_detail_info_font2"
                          style="color: #999999"
                        >
                          {{value.s_price}}
                        </p>
                      </div>
                    </div>

                    <!-- 加载效果 -->
                    <div v-if="busyProduct" class="loadingtip">
                      {{loadingtip}}
                    </div>
                  </div>
                </div>
              </div>
              <!--扣卡-->
              <div class="search_menu-card" v-if="billingType==2">
                <div class="member-consumption">
                  <ul class="consumption-ul">
                    <li class="consumption-li">
                      <p class="consumption-info">会员余额</p>
                      <p>{{memberInfo.balance | filterMoney}}</p>
                    </li>
                    <li class="consumption-li">
                      <p class="consumption-info">累计消费金额</p>
                      <p>{{memberInfo.total | filterMoney}}</p>
                    </li>
                    <li class="consumption-li">
                      <p class="consumption-info">消费次数</p>
                      <p>{{memberInfo.count}}</p>
                    </li>
                  </ul>
                  <p class="secondary-card-equity">会员权益</p>
                </div>
                <!--可选服务列表-->
                <div class="search_detail" ref="cardWrap">
                  <!--<div class="search_detail1" v-for="(value,index) in memberSecondaryCard"-->
                  <!--@click="bind_add_card(value,index)">-->
                  <!--<div class="serach_detail_info">-->
                  <!--<img v-if="value.img" class="serach_detail_img" :src="value.img"-->
                  <!--onerror="this.src='images/default.jpg'">-->
                  <!--</div>-->
                  <!--<div class="service_name-price">-->
                  <!--<p v-if="value.service_name" class="serach_detail_info_font1 service_name">-->
                  <!--{{value.service_name}}</p>-->
                  <!--<p v-if="value.price" class="serach_detail_info_font2 card-price-num"-->
                  <!--style="color: #999999;">-->
                  <!--<span>￥{{value.price}}</span>-->
                  <!--<span>剩余{{value.num}}次</span>-->
                  <!--</p>-->
                  <!--</div>-->
                  <!--</div>-->
                  <el-collapse
                    class="newMemberCardInfo"
                    accordion
                    v-model="activeCollapseNames"
                    @change="bind_membercard_change"
                  >
                    <el-collapse-item
                      class="newMemberCardItem"
                      v-for="(newMemberCard,index) in newMemberCardInfo"
                      :key="index"
                      v-if="newMemberCard.cardInfo"
                      :name="index"
                    >
                      <template slot="title">
                        <p>{{newMemberCard.cardInfo.card_name}}</p>
                        <p
                          v-if="newMemberCard.cardInfo.cardSource>-1"
                          style="
                            padding-left: 10px;
                            color: #3363FF;
                            font-size: 12px;
                          "
                        >
                          {{newMemberCard.cardInfo.indateName}}
                        </p>
                      </template>
                      <div
                        class="newMemberCardGoods"
                        @click="bind_add_membercard(newMemberCardGoods,newMemberCard.cardInfo)"
                        v-for="newMemberCardGoods in newMemberCard.goodsInfo"
                      >
                        <div>
                          <img
                            class="serach_detail_img"
                            :src="newMemberCardGoods.img"
                            onerror="this.src='images/default.jpg'"
                          />
                        </div>
                        <div
                          :class="newMemberCardGoods.cardSource>-1?'newMemberCardGoodsSercice':''"
                        >
                          <p
                            :class="newMemberCardGoods.cardSource>-1?'service_name':'service_name1'"
                            style="font-weight: bold; width: 180px"
                          >
                            {{newMemberCardGoods.service_name}}
                            <span
                              style="color: #3363FF"
                              v-if="newMemberCardGoods.givenum!=0"
                            >
                              &nbsp;[赠]
                            </span>
                          </p>
                          <p>￥{{newMemberCardGoods.price}}</p>
                          <p v-if="newMemberCardGoods.cardSource<=-1">
                            {{newMemberCardGoods.indateName}}
                          </p>
                        </div>
                        <div
                          :class="newMemberCardGoods.cardSource>-1?'newMemberCardGoodsStatusName':''"
                        >
                          <p v-if="newMemberCardGoods.cardSource>-1">
                            {{newMemberCardGoods.statusName}}
                          </p>
                          <p
                            v-if="newMemberCardGoods.cardSource<=-1"
                            class="newMemberCardGoodsWhiteStatusName"
                          >
                            {{newMemberCardGoods.statusName}}
                          </p>
                          <p
                            v-if="newMemberCardGoods.equityTypes==3 && newMemberCardGoods.infinite==1"
                          >
                            无限次卡
                          </p>
                          <p
                            v-if="newMemberCardGoods.equityTypes==3 && newMemberCardGoods.infinite==2"
                          >
                            剩余{{newMemberCardGoods.num}}次
                          </p>
                          <p v-if="newMemberCardGoods.equityTypes==2">
                            {{newMemberCardGoods.discount}}折
                          </p>
                          <p v-if="newMemberCardGoods.cardSource<=-1">
                            {{newMemberCardGoods.statusName}}
                          </p>
                        </div>
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                  <div style="text-align: center; padding: 20px">
                    <span v-if="newMemberCardInfo.length==0">没有扣卡数据</span>
                  </div>
                </div>
              </div>
            </div>

            <!--开单内容-->
            <div class="open_details">
              <!--开单详情标题-->
              <div class="open_details_border">
                <div
                  v-if="canSeeCostMaterial"
                  @click="seeCostMaterial"
                  class="open_details_title_font2"
                  style="left: 20px"
                >
                  查看耗材
                </div>
                <div class="open_details_title">开单详情</div>
                <div class="open_details_title_font2" @click="over_open">
                  清空页面
                </div>
              </div>
              <!--搜索夹内容和支付-->
              <div class="main-body">
                <!--搜索-->
                <div class="search_open">
                  <ul
                    v-if="memberInfo && memberInfo.id"
                    class="queryMemberInfo"
                  >
                    <li class="member-pic">
                      <!-- <i class="iconfont icontouxiang" style="font-size: 80px;color: #ccc;"></i> -->
                      <img
                        :src="memberInfo.pic"
                        alt=""
                        onerror="this.src='images/touxoang.png'"
                      />
                      <!--<img src="./images/default.jpg" alt="">-->
                    </li>
                    <li class="member-info-wrap">
                      <div class="member-info">
                        <p>
                          <span class="member-name">
                            {{memberInfo.member_name}}
                          </span>
                          <span v-if="memberInfo.remarks_name">
                            ({{memberInfo.remarks_name}})
                          </span>
                        </p>
                        <p>
                          {{memberInfo.phone}}&nbsp;{{memberInfo.memberLevelName}}
                        </p>
                        <p>会员编号：{{memberInfo.member_number}}</p>
                      </div>
                      <div
                        class="member-consumption"
                        v-if="memberInfo.lastTimes"
                      >
                        <p style="margin-bottom: 10px">上次消费</p>
                        <p>{{memberInfo.lastTimes}}</p>
                      </div>
                    </li>
                    <li class="member-del">
                      <i
                        class="el-icon-delete cursor-pointer"
                        @click.stop="bindDelMemberInfo"
                      ></i>
                    </li>
                  </ul>
                  <div v-else class="el-input el-input--suffix">
                    <!-- <input type="text" autocomplete="off" placeholder="请输入手机号或刷实体卡"
                                       class="el-input__inner"
                                       @input="bindInquireMember(queryMember)"
                                       @keyup.enter="bindInquire(queryMember)" v-model.trim="queryMember"> -->
                    <el-autocomplete
                      style="width: 100%"
                      v-model="queryMember"
                      :fetch-suggestions="querySearchAsync"
                      placeholder="请输入会员姓名、备注名、会员编号或手机"
                      @select="handleSelect"
                      @keyup.enter.native="queryEnterMember(queryMember)"
                    >
                      <template
                        slot-scope="{ item }"
                        style="border-bottom: 1px solid #ccc"
                      >
                        <div
                          style="
                            display: flex;
                            flex-direction: row;
                            justify-content: space-between;
                            border-bottom: 1px solid #ccc;
                          "
                        >
                          <div>
                            <p style="font-weight: bold">
                              {{item.member_name}}
                            </p>
                            <p>会员编号：{{item.member_number}}</p>
                          </div>
                          <div>
                            <p style="line-height: 68px">
                              手机号：{{item.phone}}
                            </p>
                          </div>
                        </div>
                      </template>
                    </el-autocomplete>
                    <span class="el-input__suffix">
                      <span class="el-input__suffix-inner">
                        <i class="el-input__icon el-icon-search"></i>
                      </span>
                    </span>
                  </div>
                </div>
                <div
                  class="open_details_info"
                  :class="is_dynamic?'detailsHeight2':'detailsHeight'"
                >
                  <!--内容-->
                  <div class="open_server_name">
                    <div
                      style="border-bottom: 1px solid #ccc"
                      v-for="(value,index) in C_open_order_specifications_save"
                    >
                      <div
                        class="open_details_price"
                        :style="seen1 == index ? 'background: #EDF2FE;' : '' "
                      >
                        <div
                          class="open_shop"
                          style="display: flex"
                          @click="show_choice(index,value)"
                        >
                          <div style="min-width: 32px; text-align: center">
                            <span v-text="index+1"></span>
                          </div>
                          <div class="open_details_price_name">
                            <div
                              class="open_details_price_name_font1"
                              v-if="value.service_name"
                            >
                              {{value.service_name}}
                            </div>
                            <div
                              class="open_details_price_name_font1"
                              v-if="value.product_name"
                            >
                              {{value.product_name}}
                            </div>
                            <div class="open_details_price_name_font2">
                              {{value.sku}}
                            </div>
                            <div
                              class="open_details_price_name_font2"
                              v-if="value.technician_name"
                            >
                              理疗师：{{value.technician_name}}
                            </div>
                            <div
                              class="open_details_price_name_font2"
                              v-if="value.saler_name"
                            >
                              销售：{{value.saler_name}}
                            </div>
                          </div>

                          <!--<div class="open_details_price_name_font3" v-if="value.fail_power">-->
                          <!--<div class="open_shop open_details_price_name_font3" v-if="true">-->
                          <!--( *注意：{{font1}} )-->
                          <!--</div>-->

                          <div class="open_details_price_num">
                            <span v-if="value.zhonglei==3">
                              ￥{{value.price}}*{{value.buyNum}}
                            </span>
                            <span v-if="value.zhonglei==1 || value.zhonglei==2">
                              ￥{{value.price}}*{{value.num}}
                            </span>
                          </div>

                          <div
                            class="open_details_price_all"
                            @click="show_choice(index)"
                          >
                            <p style="margin-bottom: 8px">
                              小计:&nbsp;&nbsp;{{subtotal(index)}}
                            </p>
                            <!--1 无权益 2折扣 3抵扣 4优惠金额-->
                            <!--<input type="text" placeholder="小计" v-model="subtotal(index)"></br>-->
                            <p v-if="value.manualDiscount==4">
                              <span>优惠金额:</span>
                              <span v-if="value.subtotal>value.price">+</span>
                              <!--<input type="text" placeholder="优惠金额"v-model="value.subtotal-subtotal(index)">-->
                              <span>
                                ￥{{(value.subtotal-subtotal(index)).toFixed(2)}}
                              </span>
                            </p>
                            <p
                              v-if="(value.manualDiscount==3 || value.manualDiscount==2) && value.manualDiscountCard"
                            >
                              <span
                                v-if="value.manualDiscount==3 && value.manualDiscountCard.isgive==1"
                              >
                                (赠)
                              </span>
                              <span v-if="value.manualDiscountCard.cardName">
                                {{value.manualDiscountCard.cardName}}&emsp;
                              </span>
                              <span v-if="value.manualDiscountCard.discount>0">
                                {{value.manualDiscountCard.discount}}折
                              </span>
                              <span v-if="value.subtotal<value.price">-</span>
                              <span>
                                ￥{{(subtotal(index)-value.subtotal).toFixed(2)}}
                              </span>
                            </p>
                          </div>
                        </div>
                        <div class="open_details_price_del">
                          <el-button
                            type="text"
                            @click="buy_again(value,index)"
                            style="margin-right: 10px"
                          >
                            再点一个
                          </el-button>
                          <i
                            class="el-icon-delete cursor-pointer"
                            @click="open_details_price_del(index)"
                          ></i>
                        </div>
                      </div>
                      <div
                        class="open_details_price_name_font3"
                        v-if="value.failfont"
                        :style="seen1 == index ? 'background: #f4dae8' : 'background: #F5F5F5;' "
                      >
                        ( *注意：{{value.failfont}} )
                      </div>
                      <div v-if="(seen2 == index)">
                        <div class="change_all_price">
                          <div
                            class="useOffer"
                            v-if="memberInfo && memberInfo.id"
                          >
                            <div>
                              <span>使用权益</span>
                              <!--is_offer 0 不使用优惠  会员权益选中服务后带的权益的标志位是memberCardFlag-->
                              <span v-if="value.isActiveCostCard==1">
                                <span
                                  v-if="value.manualDiscount==1"
                                  class="offer-type"
                                  @click="bindUseOffer(value,index)"
                                >
                                  不使用优惠
                                </span>
                                <span
                                  v-else-if="value.manualDiscount==4"
                                  class="offer-type"
                                  @click="bindUseOffer(value,index)"
                                >
                                  优惠金额
                                </span>

                                <span
                                  v-else-if="(value.manualDiscount==2 || value.manualDiscount==3 )  && value.manualDiscountCard"
                                  class="offer-type"
                                  @click="bindUseOffer(value,index)"
                                >
                                  <span
                                    v-if="value.manualDiscount==3 && value.manualDiscountCard.isgive==1"
                                  >
                                    (赠)
                                  </span>
                                  <span
                                    v-if="value.manualDiscountCard.cardName"
                                  >
                                    {{value.manualDiscountCard.cardName}}&emsp;
                                  </span>
                                  <span v-if="value.manualDiscount==2">
                                    {{value.manualDiscountCard.discount}}折
                                  </span>
                                </span>
                              </span>
                              <span
                                v-if="value.isActiveCostCard==0 || value.isActiveCostCard==2"
                              >
                                <span
                                  class="offer-type"
                                  v-if="value.manualDiscount==4"
                                  @click="bindUseOffer(value,index)"
                                >
                                  优惠金额
                                </span>
                                <span
                                  class="offer-type"
                                  v-if="(value.isActiveCostCard==2 || value.isActiveCostCard==0) && value.manualDiscount!=4 && value.add_preferential_id!=0"
                                  @click="bindUseOffer(value,index)"
                                >
                                  <span
                                    v-if="value.manualDiscountCard.cardSource==-1"
                                  >
                                    其他权益折扣卡
                                    <span v-if="value.manualDiscount==2">
                                      {{value.manualDiscountCard.discount}}折
                                    </span>
                                  </span>
                                  <span
                                    v-if="value.manualDiscountCard.cardSource!=-1"
                                  >
                                    不使用优惠
                                  </span>
                                </span>
                                <!-- <span class="offer-type"
                                                            v-if="(value.isActiveCostCard==2 || value.isActiveCostCard==0) && value.manualDiscount==2 && value.add_preferential_id!=0"
                                                            @click="bindUseOffer(value,index)">不使用优惠</span> -->
                                <span
                                  class="offer-type"
                                  v-if="(value.isActiveCostCard==2 || value.isActiveCostCard==0) && value.manualDiscount==2 && value.add_preferential_id==0"
                                  @click="bindUseOffer(value,index)"
                                >
                                  {{value.manualDiscountCard.cardName}}
                                  <span v-if="value.manualDiscount==2">
                                    {{value.manualDiscountCard.discount}}折
                                  </span>
                                </span>
                              </span>
                            </div>
                            <div>
                              <span>耗卡</span>
                              <span
                                class="offer-type"
                                v-if="value.isActiveCostCard==1 && value.judgeCard==0"
                                @click="bindCostCard(1)"
                              >
                                不使用耗卡
                              </span>
                              <!-- <span  class="offer-type" v-if='value.isActiveCostCard==0'
                                                          @click="bindCostCard(0)">默认账户</span> -->

                              <span
                                class="offer-type"
                                v-if="value.judgeCard==2 || value.judgeCard==1"
                                @click="bindCostCard(2)"
                              >
                                <span v-if="value.costCard">
                                  {{value.costCard.card_info ||
                                  '不使用耗卡'}}&emsp;
                                </span>
                              </span>
                              <!-- <span class="offer-type" v-if='value.isActiveCostCard==1 && value.judgeCard==1'
                                                            @click="bindCostCard(2)">
                                                        <span v-if="value.costCard" >{{value.costCard.card_info}}&emsp;</span>
                                                    </span> -->
                            </div>
                            <i class="el-icon-arrow-right"></i>
                          </div>
                          <div class="subtotal-price">
                            <div class="change_all_price_font2">
                              <span>小计金额：￥</span>
                              <input
                                class="subtotal-price-inner"
                                type="text"
                                v-model.trim="value.subtotal"
                                @change="binsChangeSubtotal(value.subtotal)"
                                :readonly="value.manualDiscount!=4"
                                placeholder="0.00"
                              />
                            </div>
                            <!--<span type="hidden">{{offerPrice()}}</span>-->
                            <!--<input type="hidden" v-model.trim="offerPrice"-->
                            <!--placeholder="小计金额计算优惠,勿动">-->

                            <div
                              class="change_all_price_font3"
                              v-if="memberInfo.id"
                            >
                              可更改小计金额
                            </div>
                          </div>
                        </div>
                        <div class="chioce_technician">
                          <div
                            class="chioce_technician_name"
                            :style="value.is_open_product_jishi == true ? 'display: none;':'' "
                          >
                            <span
                              class="chioce_technician_name_font1"
                              @click="change_ji_shi(index)"
                            >
                              选择手艺人
                            </span>
                            <span class="chioce_technician_name_font2">
                              {{value.technician_name}}
                            </span>
                          </div>
                          <div class="selective_sales_volume">
                            <span
                              class="selective_sales_volume_font1"
                              @click="change_xiaoshou(index)"
                            >
                              选择销售
                            </span>
                            <span class="selective_sales_volume_font2">
                              {{value.saler_name}}
                            </span>
                          </div>
                          <div class="batch">
                            <span
                              class="batch_font1"
                              @click="batch_choice(index)"
                            >
                              批量
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="C_open_order_specifications_save.length>0"
                    class="order_remark"
                  >
                    <p class="order_remark_font">订单备注</p>
                    <div class="order_remark_input">
                      <el-input
                        type="text"
                        placeholder="请输入"
                        v-model="beizhu_info"
                      ></el-input>
                    </div>
                  </div>
                  <div style="display: none">
                    <div class="add_server1">
                      <div class="add_server1_font1">
                        <span
                          class="add_server1_font2"
                          @click="bk_tianjia_zengson"
                          style="cursor: pointer"
                        >
                          添加赠送&emsp;
                        </span>
                      </div>
                      <div class="add_server1_font3">
                        <i class="iconfont iconarrow"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <!--备注和支付-->
                <div class="open_details_pay">
                  <div style="background: #f1f1f1">
                    <div class="chioce_Discount">
                      <!--&lt;!&ndash;这个部分功能暂时不提供&ndash;&gt;-->
                      <div class="chioce_Discount_font1">
                        <!-- <span class="chioce_Discount_font0"
                          >选择优惠&emsp;</span
                        >
                        <span
                          class="chioce_Discount_font2"
                          v-if="!isCouponCard"
                          @click="kd_xuanze_youhui"
                          >不使用优惠券</span
                        > -->
                        <span
                          class="chioce_Discount_font2"
                          v-if="isCouponCard && C_open_order_specifications_save.couponCard"
                          @click="kd_xuanze_youhui"
                        >
                          {{C_open_order_specifications_save.couponCard.coupon_name}}
                        </span>
                      </div>
                      <div class="chioce_Discount_font1">
                        <span class="chioce_Discount_font0">
                          选择赠送&emsp;
                        </span>
                        <span
                          class="chioce_Discount_font2"
                          v-if="showGiftData.allPrice==0"
                          @click="chooseGift"
                        >
                          没有赠送的商品
                        </span>
                        <span
                          class="chioce_Discount_font2"
                          v-if="showGiftData.allPrice>0"
                          @click="chooseGift"
                        >
                          服务({{showGiftData.serverNum}})/产品({{showGiftData.productNum}})；价值{{showGiftData.allPrice
                          | filterMoney}}元
                        </span>
                      </div>
                      <!-- <div class="chioce_Discount_font3">
                                        <i class="iconfont iconarrow"></i>
                                    </div> -->
                    </div>
                    <div class="open_details_pay_choice">
                      <div class="open_details_pay_choice_font1">
                        <span>待支付&nbsp;￥{{payTotal.toFixed(2)}}</span>
                        <!--<span>实际支付&nbsp;&nbsp;</span>-->
                        <!--<span>￥{{total}} </span>-->
                      </div>
                      <div class="order_three">
                        <div
                          class="open_details_pay_choice_font2"
                          @click="fetchOrder('','','')"
                        >
                          取单
                        </div>
                        <div
                          class="open_details_pay_choice_font3"
                          @click="save_order('save')"
                        >
                          保存
                        </div>
                        <div
                          class="open_details_pay_choice_font4"
                          @click="save_order('receipt')"
                        >
                          收款
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>

        <!--收银台办卡-->
        <ul class="main-right" v-if="isactive1==1">
          <!---->
          <li style="display: flex; width: 100%">
            <!--选择订单内容-->
            <div class="server">
              <!--产品和服务-->
              <div class="server_chioce">
                <div class="server_center">
                  <el-radio-group
                    v-model="cardType"
                    size="medium"
                    @change="bindChangeCard"
                  >
                    <el-radio-button label="0">全部</el-radio-button>
                    <el-radio-button label="2">充值卡</el-radio-button>
                    <el-radio-button label="1">次卡</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              <!--服务和搜索-->
              <div class="search_menu">
                <!--搜索-->
                <div class="search_bor">
                  <el-input
                    placeholder="请输入卡项名称"
                    suffix-icon="el-icon-search"
                    autofocus
                    ref="cardKeyword"
                    v-model.trim="cardKeyword"
                    @keyup.enter.native.exact.stop="bindSearchCard"
                  ></el-input>
                </div>
                <!--选择标签-->
                <!-- <div > -->
                <div class="shop-body" ref="applyCard">
                  <div
                    v-infinite-scroll="loadMoreCard"
                    infinite-scroll-disabled="isCardScroll"
                    infinite-scroll-distance="10"
                    infinite-scroll-immediate-check="isCardScroll"
                  >
                    <div
                      v-for="(item,index) in cardArr"
                      :class="cardIndex==index?'cardActive':''"
                      class="card-shop"
                      @click="bindChooseCard(index,item)"
                    >
                      <div class="cardBg">
                        <img v-if="cardIndex!=index" src="./images/card1.png" />
                        <img v-if="cardIndex==index" src="./images/card2.png" />
                        <div class="card-info">
                          <p class="service_name">{{item.card_name}}</p>
                          <p class="validity_time">
                            <span v-if="item.permanent==2">
                              有效时间：{{item.validity_time}}天
                            </span>
                            <span v-if="item.permanent==1">永久有效</span>
                          </p>
                          <p class="giveAway-faceValue">
                            <span
                              class="giveAway"
                              :class="cardIndex!=index?'giveAwayActive':''"
                              v-if="item.give && item.give>0"
                            >
                              赠送￥{{item.give | filterMoney}}
                            </span>
                            <span v-else></span>
                            <span class="faceValue">
                              ￥
                              <span style="font-size: 24px">
                                {{item.realPrice}}
                              </span>
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 触底文字 -->
                  <div v-if="busyCard" class="loadingtip">{{loadingtip}}</div>
                </div>
                <!-- </div> -->
              </div>
            </div>
            <!--开单内容-->
            <div class="open_details">
              <!--开单详情标题-->
              <div class="open_details_border">
                <div class="open_details_title">办理会员卡</div>
                <div class="open_details_title_font2" @click="bk_over_open">
                  清空页面
                </div>
              </div>
              <!--搜索夹内容和支付-->

              <div>
                <div class="bk_open_details_info">
                  <!--搜索-->
                  <ul
                    v-if="isVipCardMember"
                    class="queryMemberInfo"
                    style="padding-top: 0"
                  >
                    <li class="member-pic">
                      <img
                        :src="memberObjData[0].pic"
                        alt=""
                        onerror="this.src='images/default.jpg'"
                      />
                    </li>
                    <li class="member-info-wrap">
                      <div class="member-info">
                        <p>
                          <span class="member-name">
                            {{memberObjData[0].member_name}}
                          </span>
                          <!-- <span v-if="memberInfo.remarks_name">({{memberObjData[0].remarks_name}})</span> -->
                        </p>
                        <p>{{memberObjData[0].phone}}</p>
                        <p>会员编号：{{memberObjData[0].member_number}}</p>
                      </div>
                      <!-- <div class="member-consumption" v-if="memberInfo.lastTimes">
                                        <p style="margin-bottom: 10px">上次消费</p>
                                        <p>{{memberObjData[0].lastTimes}}</p>
                                    </div> -->
                    </li>
                    <li class="member-del" style="cursor: pointer">
                      <i
                        class="el-icon-delete cursor-pointer"
                        @click.stop="delVipCardMemberInfo"
                      ></i>
                    </li>
                  </ul>
                  <div v-else class="presonal_info">
                    <div class="bk_presonal_touxiang">
                      <i
                        v-if="!memberObjData[0]"
                        class="iconfont icontouxiang"
                        style="font-size: 80px; color: #ccc"
                      ></i>
                      <img
                        v-if="memberObjData[0]"
                        class="bk_presonal_touxiang_img"
                        :src="memberObjData[0].pic"
                        alt=""
                        onerror="this.src='images/default.jpg'"
                      />
                      <!--<img class="bk_presonal_touxiang_img" src="./images/default.jpg">-->
                    </div>
                    <div class="bk_presonal_data">
                      <div class="presonal_tel">
                        <input
                          type="text"
                          placeholder="输入手机号或刷实体卡"
                          class="bk_tel_input"
                          v-model.trim="memberObj.phone"
                          ref="memberPhone"
                          @input="bindInquireMember(memberObj.phone)"
                          @keyup.enter="bindInquire(memberObj.phone)"
                        />
                      </div>
                      <div class="presonal_name">
                        <input
                          type="text"
                          placeholder="姓名"
                          class="bk_name_input"
                          v-model="memberObj.name"
                        />
                        <div style="display: none">
                          <span
                            @click="bindGender(0)"
                            class="iconfont iconnv gender"
                            :class="memberObj.is_sex==0 ? 'bk_sex_add':'' "
                          >
                            &emsp;女
                          </span>
                          <span
                            @click="bindGender(1)"
                            class="iconfont iconnan gender"
                            :class="memberObj.is_sex==1 ? 'bk_sex_add':'' "
                          >
                            &emsp;男
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="bk_open_server_name">
                    <div>
                      <div
                        style="display: flex"
                        class="ka_detail"
                        v-if="cardTemp && cardTemp.id"
                      >
                        <div style="flex: 0.95; position: relative">
                          <div class="ka_server_one1">
                            <div style="width: 350px">
                              <span class="ka_name_font1">
                                {{cardTemp.card_name}}
                              </span>
                            </div>
                            <div class="ka_name_font3" style="width: 200px">
                              <span>有效时间:</span>
                              <span
                                v-if="cardTemp.permanent==2 && cardTemp.validity_time"
                              >
                                {{cardTemp.validity_time}}天
                              </span>
                              <span v-if="cardTemp.permanent==1">永久有效</span>
                              <!--<span>共5次</span>-->
                            </div>
                          </div>
                          <div
                            class="ka_server_one"
                            v-for="(value ,index) in card_server_name"
                          >
                            <div class="ka_one_font1">
                              <span>{{value.goods_name}}</span>
                              <span v-if="value.isgive==1">[赠送]</span>
                            </div>
                            <div class="ka_one_font2">
                              <!--card_type 1 次卡  2 充值卡-->
                              <!--isgive  是否赠送  1 赠  2不-->
                              <!--数量（赠送的一定有效，次卡有效（有限次卡有效，无限次卡为-1，通卡可隐藏））数值为-1是表示无限-->
                              <!--次卡类型 1，有限次卡，2，无限次卡，3，通卡-->

                              <template v-if="value.card_type==1">
                                <p v-if="value.once_cardtype==1">
                                  <span v-if="value.isgive==1">
                                    {{value.num}}次
                                  </span>
                                  <span v-if="value.isgive==2">
                                    {{value.num}}次
                                  </span>
                                </p>
                                <span v-if="value.once_cardtype==2">
                                  无限次
                                </span>
                                <span v-if="value.once_cardtype==3">
                                  <span v-if="value.isgive==1">
                                    {{value.num}}次
                                  </span>
                                  <span
                                    style="
                                      position: absolute;
                                      top: 20px;
                                      right: 0;
                                    "
                                    v-if="value.isgive==2 && index==0"
                                  >
                                    总共{{value.totalNum}}次
                                  </span>
                                </span>
                              </template>

                              <template v-if="value.card_type==2">
                                <span v-if="value.discount>0">
                                  {{value.discount}}折
                                </span>
                                <span v-else="value.num==1">
                                  {{value.num}}次
                                </span>
                              </template>
                            </div>
                          </div>
                        </div>
                        <div
                          style="
                            flex: 0.05;
                            padding-left: 20px;
                            display: flex;
                            align-items: center;
                          "
                        >
                          <div
                            class="iconfont iconlajitong"
                            @click="bindDelCard"
                          ></div>
                        </div>
                      </div>
                      <div style="display: none" class="ka_zeng_jian">
                        <div class="ka_choice_num">选择数量</div>
                        <div class="open_details_num">
                          <span class="span1" @click="bk_ka_jian">-</span>
                          <span class="span2">{{bk_server_number_ka}}</span>
                          <span class="span3" @click="bk_ka_zeng">+</span>
                        </div>
                      </div>
                      <div>
                        <div class="dist_power">
                          <div class="dist_power_font1">
                            <span
                              class="dist_power_font2"
                              @click="bk_xuanze_youhui_power"
                              style="cursor: pointer"
                            >
                              优惠权益&emsp;
                            </span>
                            <span
                              class="dist_power_font3"
                              @click="bk_is_xuanze_youhui = true"
                              v-if="isBeneficial==4"
                            >
                              优惠金额
                            </span>
                            <span
                              class="dist_power_font3"
                              @click="bk_is_xuanze_youhui = true"
                              v-if="isBeneficial==1"
                            >
                              不使用优惠
                            </span>
                          </div>

                          <!-- <div class="dist_power_font1" v-if='cardTemp && cardTemp.card_type==1'>
                                                <span class="dist_power_font2"  @click="cardChooseCostCard"
                                                      style="cursor: pointer">选择耗卡&emsp;</span>
                                                <span class="dist_power_font3" @click="cardChooseCostCard"
                                                      v-if="isShowCostCard==0">默认账户</span>
                                                <span class="dist_power_font3" @click="cardChooseCostCard"
                                                      v-if="isShowCostCard==1">不使用耗卡</span>
                                                <span class="dist_power_font3" @click="cardChooseCostCard"
                                                      v-if="isShowCostCard==2 && cardTemp.costCard">{{cardTemp.costCard.costName}}</span>
                                            </div> -->
                          <div class="dist_power_font4">
                            <i class="iconfont iconarrow"></i>
                          </div>
                        </div>
                      </div>

                      <div class="xiaoji_price">
                        <div class="card-subtotal-money">
                          <span class="card-subtotal">小计金额</span>
                          <input
                            class="change-money"
                            placeholder="￥0.00"
                            type="text"
                            :readonly="isBeneficial==1"
                            @input="manualPriceInput"
                            v-model="manualPrice"
                          />
                        </div>
                        <p class="tip">可更改小计金额</p>
                      </div>

                      <!--选择销售-->
                      <div>
                        <div class="chioce_xiaoshou1">
                          <div class="chioce_xiaoshou1_font1">
                            <span
                              class="chioce_xiaoshou1_font2"
                              @click="bk_change_xiaoshou"
                              style="cursor: pointer"
                            >
                              选择销售&emsp;
                            </span>
                            <span
                              class="chioce_xiaoshou1_font3"
                              @click="bk_change_xiaoshou"
                            >
                              {{SalesShow || selesShows}}
                            </span>
                          </div>
                          <div class="chioce_xiaoshou1_font4">
                            <i class="iconfont iconarrow"></i>
                          </div>
                        </div>
                      </div>

                      <div class="order_remark" style="padding: 0">
                        <p class="order_remark_font">订单备注</p>
                        <div class="order_remark_input">
                          <el-input
                            type="text"
                            placeholder="请输入"
                            class="remark_input"
                            v-model="bk_beizhu_info"
                          ></el-input>
                        </div>
                      </div>
                      <div style="display: none">
                        <div class="add_server1">
                          <div class="add_server1_font1">
                            <span
                              class="add_server1_font2"
                              @click="bk_tianjia_zengson"
                              style="cursor: pointer"
                            >
                              添加赠送&emsp;
                            </span>
                          </div>
                          <div class="add_server1_font3">
                            <i class="iconfont iconarrow"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!--备注和支付-->
                <div class="open_details_pay">
                  <div style="background: #f1f1f1">
                    <div class="chioce_Discount">
                      <!-- <div class="chioce_Discount_font1">
                                        <span class="chioce_Discount_font0" @click="bk_xuanze_youhui">选择优惠&emsp;</span>
                                        <span class="chioce_Discount_font2" @click="bk_xuanze_youhui">请选择</span>
                                    </div> -->
                      <div class="chioce_Discount_font1">
                        <!-- <span class="chioce_Discount_font0"
                          >选择优惠&emsp;</span
                        >
                        <span
                          class="chioce_Discount_font2"
                          v-if="!isVipCouponCard"
                          @click="handleVipCoupon"
                          >不使用优惠券</span
                        > -->
                        <span
                          class="chioce_Discount_font2"
                          v-if="isVipCouponCard && cardTemp.couponCard"
                          @click="handleVipCoupon"
                        >
                          {{cardTemp.couponCard.coupon_name}}
                        </span>
                      </div>
                      <div class="chioce_Discount_font1">
                        <span class="chioce_Discount_font0">
                          选择赠送&emsp;
                        </span>
                        <span
                          class="chioce_Discount_font2"
                          v-if="showGiftData.allPrice==0"
                          @click="chooseGift"
                        >
                          没有赠送的商品
                        </span>
                        <span
                          class="chioce_Discount_font2"
                          v-if="showGiftData.allPrice>0"
                          @click="chooseGift"
                        >
                          服务({{showGiftData.serverNum}})/产品({{showGiftData.productNum}})；价值{{showGiftData.allPrice
                          | filterMoney}}元
                        </span>
                      </div>
                      <div class="chioce_Discount_font3">
                        <i class="iconfont iconarrow"></i>
                      </div>
                    </div>
                    <div class="open_details_pay_choice">
                      <div class="open_details_pay_choice_font1">
                        <span>待支付&nbsp;&nbsp;</span>
                        <span>￥{{paymentMoney?paymentMoney:0}}</span>
                      </div>
                      <div class="order_three">
                        <div
                          class="open_details_pay_choice_font4"
                          @click="bindOpenCard"
                        >
                          收款
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>

          <!---->
        </ul>

        <!--收银台充值-->
        <ul class="main-right" v-show="isactive1==2">
          <!---->
          <li style="display: flex; width: 100%">
            <!--选择订单内容-->
            <div class="server">
              <!--产品和服务-->
              <div>
                <div
                  style="
                    display: flex;
                    justify-items: center;
                    border-bottom: 2px solid rgba(238, 238, 238, 1);
                  "
                  class="saoma_chongzhi"
                >
                  <div class="chongzhi_view">选择充值账户</div>
                </div>
              </div>
              <!--服务和搜索-->
              <div class="search_menu" style="padding: 0">
                <!--搜索-->
                <div
                  class="search_bor"
                  style="box-sizing: border-box; padding: 15px 15px 0 15px"
                >
                  <div class="el-input el-input--suffix">
                    <!--<input type="text" autocomplete="off" placeholder="请输入服务名称" class="el-input__inner">-->
                    <!--<span class="el-input__suffix">-->
                    <!--<span class="el-input__suffix-inner">-->
                    <!--<i class="el-input__icon el-icon-search"></i>-->
                    <!--</span>-->
                    <!--</span>-->
                  </div>
                </div>
                <!--选择标签-->
                <div class="cz_server_list">
                  <div
                    class="cz_server_list_czk"
                    v-for="(item,index) in cz_chongzhika"
                    @click="cz_list_getPhoneInfo(item)"
                  >
                    <div class="cz-bg-img">
                      <img
                        class="cz-img"
                        v-if="item.id!=0"
                        src="./images/card1.png"
                        alt=""
                      />
                      <img
                        class="cz-img"
                        v-else
                        src="./images/card2.png"
                        alt=""
                      />
                      <div class="cz-main-wrap">
                        <p class="cz-card_info">{{item.card_info}}</p>
                        <div class="cz-capitalbalance">
                          ￥{{item.residuebalance | filterMoney}}
                        </div>
                        <div class="principal_gift">
                          <span style="font-size: 14px">
                            本金￥{{item.capitalbalance | filterMoney}}
                          </span>
                          <span class="czk_zengsong">
                            赠送￥{{item.presentbalance | filterMoney}}元
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!--分割线-->
            <div class="menu_line"></div>
            <!--开单内容-->
            <div class="open_details">
              <!--开单详情标题-->
              <div class="open_details_bordercz">
                <div class="open_details_title">会员充值</div>
                <div class="open_details_title_font2" @click="cz_over_open">
                  清空页面
                </div>
              </div>
              <!--搜索夹内容和支付-->

              <div class="cz_open_details_info">
                <!-- 会员头部信息 -->
                <div class="">
                  <!---->
                  <ul
                    v-if="is_sechargeMember"
                    class="queryMemberInfo"
                    style="padding-top: 0"
                  >
                    <li class="member-pic">
                      <img
                        :src="memberInformation.pic"
                        alt=""
                        onerror="this.src='images/default.jpg'"
                      />
                    </li>
                    <li class="member-info-wrap">
                      <div class="member-info">
                        <p>
                          <span class="member-name">
                            {{cz_huiyuanxinxi.member_name}}
                          </span>
                          <span v-if="memberInfo.remarks_name">
                            ({{cz_huiyuanxinxi.remarks_name}})
                          </span>
                        </p>
                        <p>{{cz_huiyuanxinxi.phone}}</p>
                        <p>会员编号：{{cz_huiyuanxinxi.member_number}}</p>
                      </div>
                      <div
                        class="member-consumption"
                        v-if="memberInfo.lastTimes"
                      >
                        <p style="margin-bottom: 10px">上次消费</p>
                        <p>{{cz_huiyuanxinxi.lastTimes}}</p>
                      </div>
                    </li>
                    <li class="member-del" style="cursor: pointer">
                      <i
                        class="el-icon-delete cursor-pointer"
                        @click.stop="bindDelMemberInfo"
                      ></i>
                    </li>
                  </ul>
                  <div
                    v-else
                    class="presonal_info"
                    style="padding-bottom: 25px"
                  >
                    <div class="bk_presonal_touxiang">
                      <i
                        class="iconfont icontouxiang"
                        style="font-size: 80px; color: #ccc"
                      ></i>
                      <!--<img class="bk_presonal_touxiang_img" src="./images/default.jpg">-->
                    </div>
                    <div class="bk_presonal_data">
                      <div class="presonal_tel">
                        <input
                          type="text"
                          placeholder="手机号或刷实体卡"
                          class="bk_tel_input"
                          v-model.trim="cz_search_keyword"
                          ref="cz_search_keyword"
                          @input="bindInquireMember(cz_search_keyword)"
                          @keyup.enter="bindInquire(cz_search_keyword)"
                        />
                      </div>
                      <div class="presonal_name">
                        <input
                          type="text"
                          placeholder="姓名"
                          class="bk_name_input"
                        />
                        <div style="display: none">
                          <span
                            @click="bindGender(0)"
                            class="iconfont iconnv gender"
                            :class="is_cz_sex==0 ? 'bk_sex_add':'' "
                          >
                            &emsp;女
                          </span>
                          <span
                            @click="bindGender(1)"
                            class="iconfont iconnan gender"
                            :class="is_cz_sex==1 ? 'bk_sex_add':'' "
                          >
                            &emsp;男
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="chongzhika_info"
                  :class="is_sechargeMember?'cz_height2':'cz_height'"
                >
                  <!-- 充值卡信息，当显示会员信息时显示 -->
                  <!--v-if="is_sechargeMember"-->
                  <div class="chaongzhika_name_money" v-if="is_recharge_card">
                    <div class="chongzhika_name_font">
                      {{cz_single_czk.card_info}}
                    </div>
                    <div class="chongzhika_money_font0">
                      <div class="chongzhika_money_font2">
                        <div class="chongzhika_money_font3">
                          ￥{{cz_single_czk.totalbalance | filterMoney}}
                        </div>
                        <div class="chongzhika_money_font1">
                          <span>
                            本金￥{{cz_single_czk.capitalbalance |
                            filterMoney}}&emsp;
                          </span>
                          <span>
                            赠送￥{{cz_single_czk.presentbalance | filterMoney}}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="chongzhika_money_font5">
                      <i
                        class="iconfont iconlajitong"
                        @click="cz_right_chongzhikaInfo_close"
                      ></i>
                    </div>
                  </div>
                  <div class="chioce_paynum">
                    <span @click="cz_xuanze_chongzhi_money">选择充值规格</span>
                  </div>
                  <div class="chongzhi_pay_num">
                    <span class="chioce_paynum_font1">
                      充值金额&emsp;&emsp;
                      <label>￥</label>
                    </span>
                    <!-- <span class="chioce_paynum_font2">￥{{cz_chongzhi.benjin}}</span> -->
                    <input
                      type="text"
                      v-model.trim="cz_chongzhi.benjin"
                      :readonly="cz_customize!=1"
                      placeholder="0.00"
                      class="cz_input"
                      @input="RechargeCardPrincipal"
                    />
                  </div>
                  <div class="vip_pay_cengson">
                    <span class="vip_pay_cengson_font1">
                      赠送金额&emsp;&emsp;
                      <label>￥</label>
                    </span>
                    <!-- <span class="vip_pay_cengson_font2">{{cz_chongzhi.zengsong}}</span> -->
                    <input
                      type="text"
                      v-model.trim="cz_chongzhi.zengsong"
                      placeholder="0.00"
                      :readonly="cz_customize!=1"
                      class="cz_input"
                      @input="ComplimentaryCard"
                    />
                  </div>
                  <div class="vip_xuanze_xiaoshou">
                    <span
                      class="vip_xuanze_xiaoshou1"
                      @click="cz_chongzhi_xuanze_xiaoshou"
                    >
                      选择销售&emsp;&emsp;
                    </span>
                    <span
                      class="vip_xuanze_xiaoshou2"
                      @click="cz_chongzhi_xuanze_xiaoshou"
                    >
                      {{rechangeSalesShow}}
                    </span>
                    <span
                      class="vip_xuanze_xiaoshou2"
                      @click="cz_chongzhi_xuanze_xiaoshou"
                      v-if="rechangeSalesShow==''"
                    >
                      请选择
                    </span>
                    <span class="selective_sales_volume_font2"></span>
                    <!-- <span class="iconfont vip_xuanze_xiaoshou3"></span> -->
                  </div>
                  <!-- <div class="vip_add_zengson">
                                <span class="add_zengson" @click="cz_chongzhi_tianjia_zengson">添加赠送</span>
                                <span class="iconfont"></span>
                            </div> -->
                  <div class="chioce_Discount_font1">
                    <span class="vip_xuanze_xiaoshou1">
                      选择赠送&emsp;&emsp;
                    </span>
                    <span
                      class="vip_xuanze_xiaoshou2"
                      v-if="showGiftData.allPrice==0"
                      @click="chooseGift"
                    >
                      没有赠送的商品
                    </span>
                    <span
                      class="vip_xuanze_xiaoshou2"
                      v-if="showGiftData.allPrice>0"
                      @click="chooseGift"
                    >
                      服务({{showGiftData.serverNum}})/产品({{showGiftData.productNum}})；价值{{showGiftData.allPrice
                      | filterMoney}}元
                    </span>
                  </div>
                  <div class="order_remark" style="padding: 0">
                    <p class="order_remark_font">订单备注</p>
                    <el-input
                      type="textarea"
                      :rows="2"
                      placeholder="请输入内容"
                      v-model="cz_chongzhi.beizhu"
                    ></el-input>
                  </div>
                </div>
              </div>
              <!--备注和支付-->
              <div class="open_details_pay">
                <div style="background: #f1f1f1">
                  <div class="open_details_pay_choice">
                    <div class="open_details_pay_choice_font1">
                      <span>待支付&nbsp;&nbsp;</span>
                      <span>￥{{cz_chongzhi.benjin?cz_chongzhi.benjin:0}}</span>
                    </div>
                    <div class="chongzhi_shoukuan">
                      <div class="chongzhi_shoukuan_font" @click="cz_shoukuan">
                        充值收款
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>

        <!-- 收银台核销 -->
        <ul class="main-right" v-show="isactive1==5">
          <!---->
          <li style="width: 100%">
            <!--开单详情标题-->
            <div class="open_details_border">
              <div class="open_details_title">核销详情</div>
              <div class="open_details_title_font2" @click="verify_over_open">
                清空页面
              </div>
            </div>
            <div>
              <div class="verify_search">
                <div class="verify_input">
                  <input
                    type="text"
                    placeholder="请输入核销码或使用扫码枪"
                    @keyup.enter.exact.stop="getVerifyOrder"
                    v-model="verifyCode"
                    ref="verifyCode"
                  />
                </div>
                <div class="verify_btn">
                  <button @click="getVerifyOrder">查询</button>
                </div>
              </div>
              <div class="verify_display" v-if="!isVerify">
                <div class="verify_img">
                  <img
                    width="370px"
                    height="310px"
                    src="images/scangun.png"
                    alt=""
                  />
                </div>
                <div class="verify_span">
                  <span>请顾客出示核销码，可使用扫码枪扫描核销</span>
                </div>
              </div>
              <div class="verify_card" v-if="isVerify">
                <div class="verify_server">
                  <div>
                    <img
                      class="serach_detail_img"
                      :src="verifyData.orderDetailsData[0].imgUrl"
                      onerror="this.src='images/default.jpg'"
                    />
                  </div>
                  <div
                    class="verify_server_name"
                    v-if="verifyData.orderDetailsData[0].skuId!=0"
                  >
                    <p>{{verifyData.orderDetailsData[0].name}}</p>
                    <p style="margin-top: 44px">
                      {{verifyData.orderDetailsData[0].sku_name}}
                    </p>
                  </div>
                  <div
                    class="verify_server_name"
                    v-if="verifyData.orderDetailsData[0].skuId==0"
                  >
                    <p style="margin-top: 35px">
                      {{verifyData.orderDetailsData[0].name}}
                    </p>
                  </div>
                </div>
                <ul class="verify_ul" v-if="verifyData">
                  <li class="verify_appointment" v-if="verifyData.buyer">
                    预约人
                    <span style="float: right">
                      {{verifyData.buyer.member_name}}
                    </span>
                  </li>
                  <li class="verify_appointment" v-if="verifyData.buyer">
                    联系方式
                    <span style="float: right">{{verifyData.buyer.phone}}</span>
                  </li>
                  <li
                    class="verify_appointment"
                    v-if="verifyData.orderDetailsData"
                  >
                    <span
                      style="color: #3363FF; cursor: pointer"
                      @click="verifyChooseStaff"
                    >
                      选择手艺人
                    </span>
                    <span
                      style="float: right"
                      v-if="verifyData.techniciansName"
                    >
                      {{verifyData.techniciansName}}
                    </span>
                    <span style="float: right" v-else>暂无</span>
                  </li>
                  <!-- <li class="verify_ul_remark" >
                                <p style="line-height: 22px;">
                                    留言：  <span >留言留言留言留言留言留言留言留言留言留言留言留言留言留言留言留言留言留言留言留言</span>
                                </p>
                            </li> -->
                  <li class="verify_ul_btn">
                    <button @click="seeCostMaterialPrint(verifyData)">
                      查看耗材
                    </button>
                    <button @click="checkVerify">立即核销</button>
                  </li>
                </ul>
              </div>
            </div>
          </li>
        </ul>

        <!-- 核销成功页面 -->
        <el-dialog
          :visible.sync="isVerifySuccess"
          fullscreen
          :show-close="false"
          class="cz_kaidan"
        >
          <div class="kaidan">
            <div slot="title">
              <div
                class="cz_qudan_top"
                style="background: #f6f6f6; color: #333"
              >
                <div class="title-left" @click="verify_close">关闭</div>
                <div style="flex: 1; text-align: center">收银台</div>
              </div>
            </div>
            <div class="cash-register">
              <div class="left_main">
                <div class="left_mid">
                  <app-heading title="订单信息"></app-heading>
                  <ul class="order_info_ul">
                    <li class="order_info_li">
                      <span>订单编号</span>
                      <span>{{verifyOrderDetails.order_number}}</span>
                    </li>
                    <li class="order_info_li">
                      <span>下单时间</span>
                      <span>{{verifyOrderDetails.order_time}}</span>
                    </li>
                    <li class="order_info_li">
                      <span>会员姓名</span>
                      <span v-if="verifyOrderDetails.vip">
                        {{verifyOrderDetails.vip.member_name}}
                      </span>
                    </li>
                    <li class="order_info_li">
                      <span>会员编号</span>
                      <span v-if="verifyOrderDetails.vip">
                        {{verifyOrderDetails.vip.member_number}}
                      </span>
                    </li>
                    <li class="order_info_li">
                      <span>会员手机号</span>
                      <span v-if="verifyOrderDetails.vip">
                        {{verifyOrderDetails.vip.phone}}
                      </span>
                    </li>
                    <li class="order_info_li">
                      <span>收银员</span>
                      <span v-if="loginInfo.nickname">
                        {{loginInfo.nickname}}
                      </span>
                    </li>
                    <li class="order_info_li">
                      <span>收款时间</span>
                      <span>{{verifyOrderDetails.time}}</span>
                    </li>
                  </ul>

                  <app-heading title="消费明细"></app-heading>
                  <ul>
                    <li
                      v-for="(item,index) in verifyOrderDetails.orderInfo"
                      class="orderDetails-li"
                    >
                      <p class="serialNumber">{{index + 1}}、</p>
                      <div class="details-list">
                        <div class="order-date-li">
                          <span class="order-label">
                            <span>{{item.name}}</span>
                            <span v-if="item.sku_name">
                              | {{item.sku_name}}
                            </span>
                          </span>
                          <span class="order-label2">￥{{item.price}}</span>
                        </div>
                        <div class="order-date-li">
                          <span class="order-label">数量</span>
                          <span class="order-label2">*{{item.num}}</span>
                        </div>
                        <div class="order-date-li" v-if="item.equity_type!=1">
                          <span class="order-label" v-if="item.equity_type==3">
                            次卡抵扣
                          </span>
                          <span class="order-label" v-if="item.equity_type==4">
                            优惠金额
                          </span>
                          <span class="order-label" v-if="item.equity_type==2">
                            充值卡折扣
                          </span>
                          <span class="order-label2">
                            ￥{{item.reduceprice}}
                          </span>
                        </div>
                        <div class="order-date-li">
                          <span class="order-label">小计</span>
                          <span class="order-label2">￥{{item.Subtotal}}</span>
                        </div>
                        <!--预约订单显示-->
                        <!-- <div class="order-date-li">
                                        <span class="order-label">选择技师</span>
                                        <span class="order-label2">{{item.Craftsman}}</span>
                                    </div> -->
                      </div>
                    </li>
                  </ul>

                  <ul>
                    <li class="orderDetails-li">
                      <div style="width: 100%" v-if="verifyDetailsData">
                        <div class="order-date-li">
                          <span class="order-label">合计</span>
                          <span class="order-label2">
                            {{verifyDetailsData.price}}
                          </span>
                        </div>
                        <div
                          class="order-date-li"
                          v-if="verifyDetailsData.equity_type!=1"
                        >
                          <span
                            class="order-label"
                            v-if="verifyDetailsData.equity_type==3"
                          >
                            次卡抵扣
                          </span>
                          <span
                            class="order-label"
                            v-if="verifyDetailsData.equity_type==2"
                          >
                            充值卡折扣
                          </span>
                          <span class="order-label2">
                            ￥{{verifyDetailsData.reduceprice}}
                          </span>
                        </div>
                        <div
                          class="order-date-li"
                          v-if="verifyDetailsData.equity_type==2"
                        >
                          <span
                            class="order-label"
                            v-if="verifyPayment.payTypeName"
                          >
                            付款：{{verifyPayment.payTypeName}}
                          </span>
                          <span class="order-label2">
                            ￥{{verifyPayment.trade_amount}}
                          </span>
                        </div>
                        <div class="order-date-li">
                          <span class="order-label">合计收款</span>
                          <span class="order-label2">
                            ￥{{verifyOrderDetails.receivable}}
                          </span>
                        </div>
                        <!--预约订单显示-->
                        <!-- <div class="order-date-li">
                                        <span class="order-label">选择技师</span>
                                        <span class="order-label2">{{item.Craftsman}}</span>
                                    </div> -->
                      </div>
                    </li>
                  </ul>
                  <!-- <div class="code-attention">
                            <div class="shoucang_img">
                                <img src="../images/dianpu_shoucang.png" alt="">
                            </div>
                            <div class="shoucang_font">扫码收藏店铺，享更多优惠哦！</div>

                        </div> -->
                </div>
                <div class="left_but" v-if="false">
                  <div class="wait_pay">
                    <!-- <span v-show="endPay==0">待支付</span> -->
                    <span>已支付</span>
                  </div>
                  <div class="wait_money">
                    ￥{{verifyOrderDetails.toBePay | filterMoney}}
                  </div>
                </div>
              </div>
              <div class="right_main">
                <div class="pay-success">
                  <i class="el-icon-success successIcon"></i>
                  <h2>核销完成</h2>
                  <div class="success-btn printBtn" @click="verifyPrint">
                    打印小票
                  </div>
                  <div class="success-btn newBilling" @click="newVerify">
                    继续核销
                  </div>
                  <div
                    class="success-btn newBilling"
                    v-if="modifyAchievement"
                    @click="modifyEmployeePerformance(verifyDataOrderNo)"
                  >
                    修改员工业绩
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--默认打印-->
          <app-print
            style="display: none"
            :store="loginInfo"
            :order-info="verifyOrderDetails"
            :goods="verifyOrderDetails.orderInfo"
            :member="verifyOrderDetails.vip"
            :gift="verifyOrderDetails.presentData"
            :print-type="2"
          ></app-print>

          <!--打印样式-->
          <div
            class="printWrap"
            :style="{width:paperwidth +'px'}"
            ref="printorderstr"
            style="display: none"
          >
            <template v-for="(item,index) in printSet.set">
              <template v-if="item.name=='store'">
                <app-store :store-set="item.set" :store="loginInfo"></app-store>
              </template>
              <template v-if="item.name=='header'">
                <app-header
                  :header-set="item.set"
                  :order-header="verifyOrderDetails"
                ></app-header>
              </template>
              <template v-if="item.name=='goods'">
                <app-goods
                  :goods-set="item.set"
                  :goods="verifyOrderDetails.orderInfo"
                ></app-goods>
              </template>
              <template v-if="item.name=='goods'">
                <app-gift
                  :goods-set="item.set"
                  :gift="verifyOrderDetails.presentData"
                ></app-gift>
              </template>
              <template v-if="item.name=='vip'">
                <app-vip
                  :vip-set="item.set"
                  :member="verifyOrderDetails.vip"
                  :print-type="2"
                ></app-vip>
              </template>
              <!-- <template v-if="item.name=='takegoods'">
                <app-address
                  :address-set="item.set"
                  :order-info="verifyOrderDetails"
                ></app-address>
              </template> -->
              <template v-if="item.name=='footer'">
                <app-footer
                  :footer-set="item.set"
                  :order-footer="verifyOrderDetails"
                  :print-type="2"
                ></app-footer>
              </template>
              <template v-if="item.name=='line'">
                <app-line :line-set="item.set"></app-line>
              </template>
              <template v-if="item.name=='info'">
                <app-info :info-set="item.set"></app-info>
              </template>
              <template v-if="item.name=='text'">
                <app-text :text-set="item.set"></app-text>
              </template>
            </template>
          </div>
        </el-dialog>

        <!--服务充卡内容-->
        <app-zuheka
          v-show="isactive1==4"
          :handle_foucs_input="handleFoucsInput"
          :login="loginInfo"
        ></app-zuheka>

        <!--收银台直接收款-->
        <app-receipt
          v-show="isactive1==3"
          :receipt-member="receiptMember"
          :handle_foucs_input="handleDirectFoucsInput"
          @receipt-phone="directInputMember"
          @receipt-enter="directEnterMember"
          :login="loginInfo"
          @send="openPay"
        ></app-receipt>

        <!-- 核销选择手艺人 -->
        <!--选择手艺人模态框-->
        <el-dialog
          title="选择手艺人"
          :visible.sync="verify_ji_shi"
          top="7vh"
          :show-close="false"
          width="35%"
        >
          <div class="xuanze_jishi_search">
            <!--<el-input placeholder="输入手艺人名称" suffix-icon="el-icon-search"></el-input>-->
          </div>
          <div style="max-height: 300px; overflow: auto">
            <ul v-for="(value,index) in ji_shis">
              <li class="xuanze_jishi_name">
                <div style="display: flex">
                  <div class="xuanze_jishi_name_check">
                    <el-checkbox
                      v-model="value.is_choice_jishi"
                      @change="verify_chioce_ji_shi_name(index,value.is_choice_jishi,value.id)"
                    >
                      {{value.nickname}}
                    </el-checkbox>
                  </div>
                </div>
                <div style="display: flex">
                  <div class="xuanze_jishi_server_font">点客</div>
                  <el-switch
                    v-model="value.is_guest"
                    active-color="#3363FF"
                    @change="verify_chioce_ji_shi_guest(index,value.is_guest,value.id)"
                    inactive-color="#999999"
                  ></el-switch>
                </div>
              </li>
            </ul>
            <p style="height: 50px" v-if="ji_shis.length==0">暂无分配</p>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="verify_open_over_technician">
              取消
            </el-button>
            <el-button type="primary" @click="verify_open_save_technician">
              确定
            </el-button>
          </span>
        </el-dialog>

        <!--保存成功弹框-->
        <div class="save_ok" v-if="save_seen">保存成功</div>

        <!--充卡保存成功弹框-->
        <div class="save_ok" v-if="zhk_save_seen">保存成功</div>

        <!--选择手艺人模态框-->
        <el-dialog
          title="选择手艺人"
          :visible.sync="ji_shi"
          top="7vh"
          :show-close="false"
          width="35%"
        >
          <div class="xuanze_jishi_search">
            <!--<el-input placeholder="输入手艺人名称" suffix-icon="el-icon-search"></el-input>-->
          </div>
          <div style="max-height: 300px; overflow: auto">
            <ul v-for="(value,index) in ji_shis">
              <li class="xuanze_jishi_name">
                <div style="display: flex">
                  <div class="xuanze_jishi_name_check">
                    <el-checkbox
                      v-model="value.is_choice_jishi"
                      @change="chioce_ji_shi_name(index,value.is_choice_jishi,value.id)"
                    >
                      {{value.nickname}}
                    </el-checkbox>
                  </div>
                </div>
                <div style="display: flex">
                  <div class="xuanze_jishi_server_font">点客</div>
                  <el-switch
                    v-model="value.is_guest"
                    active-color="#3363FF"
                    @change="chioce_ji_shi_guest(index,value.is_guest,value.id)"
                    inactive-color="#999999"
                  ></el-switch>
                </div>
              </li>
            </ul>
            <p style="height: 50px" v-if="ji_shis.length==0">暂无分配</p>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="open_over_technician">
              取消
            </el-button>
            <el-button type="primary" @click="open_save_technician">
              确定
            </el-button>
          </span>
        </el-dialog>

        <!--选择销售模态框-->
        <el-dialog
          title="选择销售"
          :visible.sync="xiao_shou"
          :show-close="false"
          width="35%"
          top="7vh"
        >
          <!--<div class="xuanze_jishi_search">-->
          <!--<el-input placeholder="输入销售名称" suffix-icon="el-icon-search"></el-input>-->
          <!--</div>-->
          <div style="max-height: 300px; overflow: auto">
            <div class="xuazne_xiaoshou" v-for="(value , index) in xiaoshous ">
              <el-checkbox
                v-model="value.is_choice_xiaoshou"
                style="height: 25px; width: 25px"
                @change="chioce_xiaoshou(index,value.is_choice_xiaoshou,value.id)"
              >
                {{value.nickname}}
              </el-checkbox>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="xiaoshou_over">
              取消
            </el-button>
            <el-button type="primary" @click="xiaoshou_save">确定</el-button>
          </span>
        </el-dialog>

        <!--选择批量模态框-->
        <el-dialog
          title="选择批量"
          :visible.sync="pi_liang"
          width="35%"
          top="7vh"
        >
          <div class="xuanze_piliang_font">
            <div
              class="piliang_jishi"
              @click="piliang_jishi"
              v-show="order_is_show_jishi"
            >
              应用手艺人到其他项目
            </div>
            <div class="piliang_xiaoshou" @click="piliang_xiaoshou">
              应用销售到其他项目
            </div>
            <div
              class="piliang_both"
              @click="piliang_both"
              v-show="order_is_show_both"
            >
              应用手艺人和销售到其他项目
            </div>
          </div>
          <span slot="footer" class="dialog-footer" style="visibility: hidden">
            <el-button class="cancel-btn" @click="piliang_over">取消</el-button>
            <el-button type="primary" @click="piliang_save">确定</el-button>
          </span>
        </el-dialog>

        <!--选择服务规格-->
        <el-dialog
          title="选择规格"
          :visible.sync="cashier_open_order_Specifications"
          :show-close="false"
          top="7vh"
          width="600px"
          top="30px"
          :before-close="handleCloseSpecification"
        >
          <div class="C_open_order_Specifications">
            <div class="C_open_order_Specifications1">
              <div style="display: flex; align-items: center">
                <img
                  :src="C_open_order_Specifications.imgurl"
                  onerror="this.src='images/default.jpg'"
                  class="cashier_open_order_Specifications_img"
                />
              </div>
              <div class="C_open_order_Specifications_font">
                <div class="C_open_order_Specifications_font1">
                  {{C_open_order_Specifications.service_name}}
                </div>
                <div
                  class="C_open_order_Specifications_font2"
                  v-if="C_open_order_Specifications.price && C_open_order_Specifications.price>0"
                >
                  {{C_open_order_Specifications.price}}
                </div>
                <div
                  class="C_open_order_Specifications_font2"
                  v-else="C_open_order_Specifications.realPrice"
                >
                  {{C_open_order_Specifications.realPrice}}
                </div>
              </div>
            </div>
            <div class="C_open_order_Specification2">
              <div
                class="C_open_order_Specifications2"
                :id="value.id"
                v-for="(value,index1) in C_open_order_specifications_name"
              >
                <div class="C_open_order_Specifications2_font1">
                  <span v-if="value.name">{{value.name}}</span>
                  <span v-if="value.title">{{value.title}}</span>
                </div>
                <div class="C_open_order_Specifications2_font2">
                  <div
                    v-if="value.sku_val"
                    class="C_open_order_Specifications2_font3"
                    v-for="(todo,index2) in value.sku_val"
                    :id="todo.id"
                    @click="specificationBtn_server(value.name,todo.name,index1,index2)"
                    :class="{C_Specifications_choice1_add:todo.is_show}"
                  >
                    <span v-if="todo.name">{{todo.name}}</span>
                  </div>
                  <div
                    v-if="value.item"
                    class="C_open_order_Specifications2_font3"
                    v-for="(todo,index2) in value.item"
                    :id="todo.id"
                    @click="specificationBtn_product(value.name,todo.name,index1,index2)"
                    :class="{C_Specifications_choice1_add:todo.is_show}"
                  >
                    <span v-if="todo.text">{{todo.text}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button
              class="cancel-btn"
              @click="over_specifications(C_open_order_specifications_name)"
            >
              取消
            </el-button>
            <el-button type="primary" @click="save_specifications">
              确定
            </el-button>
          </span>
        </el-dialog>
        <!--以上是收银台开单页面的模态框-->

        <!--二维码-->
        <el-dialog
          title="扫码充值"
          :visible.sync="cz_iserweima"
          width="35%"
          top="7vh"
        >
          <div>
            <div class="zhifu_weweima">
              <img
                src="./images/zhifuerweima.png"
                alt=""
                class="zhifu_weweima_img"
              />
            </div>
          </div>
        </el-dialog>

        <!--选择充值金额-->
        <el-dialog
          title="充值金额"
          :visible.sync="cz_ischongzhijine"
          width="50%"
        >
          <div class="chongzhi_jine_mian">
            <div
              v-if="userDefined==1"
              class="chongzhi_danka customize"
              @click="bindCustomize(1)"
            >
              自定义充值
            </div>
            <div
              class="chongzhi_danka"
              v-for="(value,index) in cz_chongzhijine"
              @click="cz_choice_chongzhijine(value)"
            >
              <div class="danka_get">{{value.recharge_name}}</div>
              <div class="danka_post" style="margin-bottom: 6px">
                充值￥{{value.recharge_money}}元
              </div>
              <div class="danka_post">赠￥{{value.present_money}}</div>
            </div>
          </div>
        </el-dialog>

        <!--充值页面添加服务-->
        <el-dialog
          title="添加服务"
          :visible.sync="cz_chong_add_server"
          width="40%"
          top="7vh"
        >
          <div class="xuanze_jishi_search">
            <el-input
              placeholder="请输入服务名称"
              suffix-icon="el-icon-search"
            ></el-input>
          </div>
          <div class="tianjia_fuwu_mian">
            <div class="fuwu_biaoti">
              <ul v-for="(value,index) in bk_server_biaoti">
                <li
                  :class="cz_ischong_Addfuwu == index ? 'tianjia_fuwu_font0' : 'tianjia_fuwu_font' "
                  @click="cz_xuanze_tianjia_fuwu(index)"
                >
                  {{value.name}}
                </li>
              </ul>
            </div>
            <div class="fuwu_biaoti_line"></div>
            <div class="fuwu_biaoti_chioce">
              <ul
                v-for="(value,index) in cz_server_biaoti_name"
                class="fuwu_biaoti_chioce_bottom"
              >
                <li class="server_biaoti_name_font">
                  <div>
                    <img
                      src="./images/yangtu.jpg"
                      alt="图片"
                      class="server_biaoti_name_font1"
                    />
                    <span class="server_biaoti_name_font2">{{value.name}}</span>
                  </div>
                  <div>
                    <span class="server_biaoti_name_font3">
                      ￥{{value.price}}
                    </span>
                    <el-checkbox
                      v-model="chongzhi_checked"
                      style="text-align: right"
                    ></el-checkbox>
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="cz_chongzhi_tianjia_over">
              取消
            </el-button>
            <el-button type="primary" @click="cz_chongzhi_tianjia_save">
              确定
            </el-button>
          </span>
        </el-dialog>

        <!--充值页面选择销售模态框-->
        <el-dialog
          title="选择销售"
          :visible.sync="cz_ischongxiao"
          width="35%"
          top="7vh"
        >
          <!-- <div class="xuanze_jishi_search">
                <el-input placeholder="输入销售名称" suffix-icon="el-icon-search"></el-input>
            </div> -->
          <div style="height: calc(100vh - 500px); overflow: auto">
            <div
              class="xuazne_xiaoshou"
              v-for="(value,index) in changexiaoshous "
            >
              <el-checkbox
                v-model="value.is_choice_xiaoshou"
                @change="cz_chioce_xiaoshou(index,value.is_choice_xiaoshou,value.id)"
                style="height: 25px; width: 25px"
              >
                {{value.nickname}}
              </el-checkbox>
              <!-- <span style="cursor: pointer" ></span> -->
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="cz_chongxiao_over">
              取消
            </el-button>
            <el-button type="primary" @click="cz_xiaoshou_save">确定</el-button>
          </span>
        </el-dialog>
        <!--以上是收银台充值模态框未完善-->

        <!--选择销售模态框-->
        <el-dialog
          title="选择销售"
          :visible.sync="bk_xiao_shou"
          width="35%"
          top="7vh"
        >
          <!-- <div class="xuanze_jishi_search">
                <el-input placeholder="输入销售名称" suffix-icon="el-icon-search"></el-input>
            </div> -->
          <div style="height: calc(100vh - 500px); overflow: auto">
            <div class="xuazne_xiaoshou" v-for="(value , index) in xiaoshous ">
              <el-checkbox
                v-model="value.is_choice_xiaoshou"
                style="height: 25px; width: 25px"
                @change="chioce_xiaoshou(index,value.is_choice_xiaoshou,value.id)"
              >
                {{value.nickname}}
              </el-checkbox>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="bk_xiaoshou_over">
              取消
            </el-button>
            <el-button type="primary" @click="bk_xiaoshou_save">确定</el-button>
          </span>
        </el-dialog>

        <!--添加服务-->
        <el-dialog
          title="添加服务"
          :visible.sync="bk_tianjia_fuwu"
          width="40%"
          top="7vh"
        >
          <div class="xuanze_jishi_search">
            <el-input
              placeholder="请输入服务名称"
              suffix-icon="el-icon-search"
            ></el-input>
          </div>
          <div class="tianjia_fuwu_mian">
            <div class="fuwu_biaoti">
              <ul v-for="(value,index) in bk_server_biaoti">
                <li
                  :class="bk_isAddfuwu == index ? 'tianjia_fuwu_font0' : 'tianjia_fuwu_font' "
                  @click="bk_xuanze_tianjia_fuwu(index)"
                >
                  {{value.name}}
                </li>
              </ul>
            </div>
            <div class="fuwu_biaoti_line"></div>
            <div class="fuwu_biaoti_chioce">
              <ul
                v-for="(value,index) in bk_server_biaoti_name"
                class="fuwu_biaoti_chioce_bottom"
              >
                <li class="server_biaoti_name_font">
                  <div>
                    <img
                      src="./images/yangtu.jpg"
                      alt="图片"
                      class="server_biaoti_name_font1"
                    />
                    <span class="server_biaoti_name_font2">{{value.name}}</span>
                  </div>
                  <div>
                    <span class="server_biaoti_name_font3">
                      ￥{{value.price}}
                    </span>
                    <el-checkbox
                      v-model="bk_checked"
                      style="text-align: right"
                    ></el-checkbox>
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="bk_tianjia_over">
              取消
            </el-button>
            <el-button type="primary" @click="bk_tianjia_save">确定</el-button>
          </span>
        </el-dialog>

        <!--优惠权益-->
        <el-dialog
          title="选择可用的用户权益"
          class="select-offer"
          :visible.sync="bk_youhui_power"
          width="500px"
          top="7vh"
          :close-on-click-modal="false"
        >
          <div class="xuanze_qunayi_font0" @click="bindOffer(1)">
            不使用优惠
          </div>
          <div class="xuanze_qunayi_font1" @click="bindOffer(4)">
            <div class="xuanze_qunayi_font2">优惠金额</div>
            <div class="xuanze_qunayi_font3">修改小计金额</div>
          </div>
          <ul v-if="isactive1==0" class="use-offer-ul">
            <!-- isgive赠送？1，是，2，不是-->
            <!-- card_type卡项类型，1，次卡，2，充值卡-->
            <!-- once_cardtype次卡类型，3，通卡，2无限次卡，1，有限次卡-->
            <li
              class="use-offer-li"
              v-for="(item,index) in offerCardOnce"
              @click="bindOfferCard(1,item)"
              v-if="item.card_type==2"
            >
              <p class="offer-cardName">
                <span v-show="item.isgive==1">(赠)</span>
                <span>{{item.cardName}}</span>
              </p>
              <p style="flex: 1; display: flex; justify-content: space-between">
                <span v-if="item.once_cardtype==1">{{item.maxnum}}次</span>
                <span v-if="item.once_cardtype==2">
                  <span v-if="item.isgive==1">{{item.maxnum}}次</span>
                  <span v-if="item.isgive==2">不限次</span>
                </span>
                <span v-if="item.once_cardtype==3">{{item.maxnum}}次</span>
                <span>{{item.indate}}</span>
              </p>
            </li>
            <li
              class="use-offer-li"
              v-for="(item,index) in offerCardOnce"
              @click="bindOfferCard(1,item)"
              v-if="item.card_type==1"
            >
              <p class="offer-cardName">
                <span v-show="item.isgive==1">(赠)</span>
                <span>{{item.cardName}}</span>
              </p>
              <p style="flex: 1; display: flex; justify-content: space-between">
                <span v-if="item.once_cardtype==1">{{item.maxnum}}次</span>
                <span v-if="item.once_cardtype==2">
                  <span v-if="item.isgive==1">{{item.maxnum}}次</span>
                  <span v-if="item.isgive==2">不限次</span>
                </span>
                <span v-if="item.once_cardtype==3">{{item.maxnum}}次</span>
                <span>{{item.indate}}</span>
              </p>
            </li>
            <li
              class="use-offer-li"
              v-for="(item,index) in offerCarddis"
              @click="bindOfferCard(2,item)"
            >
              <span class="offer-cardName">{{item.cardName}}</span>
              <p style="flex: 1; display: flex; justify-content: space-between">
                <span>{{item.discount}}折</span>
                <span>{{item.indate}}</span>
              </p>
            </li>
          </ul>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="bk_youhui_power = false">
              取消
            </el-button>
            <el-button type="primary" @click="bk_youhui_power = false">
              确定
            </el-button>
          </span>
        </el-dialog>

        <!-- 开单选择耗卡 -->
        <el-dialog
          title="选择耗卡"
          class="select-offer"
          :visible.sync="isChooseCostCard"
          width="500px"
          top="7vh"
          :close-on-click-modal="false"
        >
          <div class="xuanze_qunayi_font0" @click="bindChooseCostCard(1,1,1)">
            不使用耗卡
          </div>
          <div class="xuanze_qunayi_font1" @click="bindChooseCostCard(0,1,1)">
            <div class="xuanze_qunayi_font2">默认账户</div>
          </div>
          <ul class="use-offer-ul">
            <li
              class="use-offer-li"
              v-for="(item,index) of balanceCard"
              v-if="item.cardid>0"
              @click="bindChooseCostCard(2,item,index)"
            >
              <p class="offer-cardName">
                <span>{{item.card_info}}</span>
              </p>
              <p style="flex: 1; display: flex; justify-content: space-between">
                <span>￥{{item.residuebalance/100}}</span>
                <span>{{item.indate}}</span>
              </p>
            </li>
          </ul>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="cancelCostCard">
              取消
            </el-button>
            <el-button type="primary" @click="cancelCostCard">确定</el-button>
          </span>
        </el-dialog>

        <!-- 现金券 -->
        <el-dialog
          title="选择优惠券"
          class="select-offer"
          :visible.sync="kd_is_xuanze_youhui"
          width="500px"
          top="7vh"
          :close-on-click-modal="false"
        >
          <el-input
            type="text"
            style="height: 34px; width: 92%; margin: 0 8px; padding-left: 10px"
            placeholder="请输入要核销的优惠券码"
            v-model="couponCardCode"
            @input="scanCouponCard(couponCardCode)"
          >
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
          </el-input>
          <ul class="use-offer-ul">
            <li class="use-offer-li-coupon" @click="bindCouponCard(0,0,true)">
              不使用现金券
            </li>
            <li
              class="use-offer-li-coupon"
              v-for="(item,index) of couponInfo"
              @click="bindCouponCard(item,index,item.isAvailable)"
            >
              <p
                style="margin-bottom: 8px; font-weight: bold"
                v-if="item.isAvailable"
              >
                {{item.coupon_name}}
                <span style="margin-left: 4px">(面值{{item.faceValue}}元)</span>
              </p>
              <p
                style="margin-bottom: 8px; font-weight: bold"
                v-if="!item.isAvailable"
              >
                {{item.coupon_name}}
                <span style="margin-left: 4px">(面值{{item.faceValue}}元)</span>
                <span style="color: #3363FF">不可用</span>
              </p>
              <p style="margin-bottom: 8px">
                {{item.costInfo}}
                <span style="color: #3363FF" v-if="item.applyGoods=='1'">
                  (全部商品可用)
                </span>
                <span style="color: #3363FF" v-if="item.applyGoods=='2'">
                  (指定商品可用)
                </span>
              </p>
              <p style="margin-bottom: 8px">
                {{item.expireTime.split(" 至 ")[1]}}前有效
              </p>
              <!-- <p><span style="display: block;">适用门店:</span> <br/>
                            <el-tag style="margin:0px 10px 10px 0;color:#409eff;"v-for="store of item.storeInfo">{{store.storetag}}</el-tag>
                        </p> -->
            </li>
          </ul>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="cancelCostCard">
              取消
            </el-button>
            <el-button type="primary" @click="cancelCostCard">确定</el-button>
          </span>
        </el-dialog>
        <!-- 办理会员卡 现金券 -->
        <el-dialog
          title="选择优惠券"
          class="select-offer"
          :visible.sync="vipChooseCouponCard"
          width="500px"
          top="7vh"
          :close-on-click-modal="false"
        >
          <el-input
            type="text"
            style="height: 34px; width: 92%; margin: 0 8px; padding-left: 10px"
            placeholder="请输入要核销的优惠券码"
            v-model="couponVipCardCode"
            @input="scanVipCouponCard(couponVipCardCode)"
          >
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
          </el-input>
          <ul class="use-offer-ul">
            <li
              class="use-offer-li-coupon"
              @click="bindVipCouponCard(0,0,true)"
            >
              不使用现金券
            </li>
            <li
              class="use-offer-li-coupon"
              v-for="(item,index) of couponInfo"
              @click="bindVipCouponCard(item,index,item.isAvailable)"
            >
              <p
                style="margin-bottom: 8px; font-weight: bold"
                v-if="item.isAvailable"
              >
                {{item.coupon_name}}
                <span style="margin-left: 4px">(面值{{item.faceValue}}元)</span>
              </p>
              <p
                style="margin-bottom: 8px; font-weight: bold"
                v-if="!item.isAvailable"
              >
                {{item.coupon_name}}
                <span style="margin-left: 4px">(面值{{item.faceValue}}元)</span>
                <span style="color: #3363FF">不可用</span>
              </p>
              <p style="margin-bottom: 8px">
                {{item.costInfo}}
                <span style="color: #3363FF" v-if="item.applyGoods=='1'">
                  (全部商品可用)
                </span>
                <span style="color: #3363FF" v-if="item.applyGoods=='2'">
                  (指定商品可用)
                </span>
              </p>
              <p style="margin-bottom: 8px">
                {{item.expireTime.split(" 至 ")[1]}}前有效
              </p>
              <!-- <p><span style="display: block;">适用门店:</span> <br/>
                            <el-tag style="margin:0px 10px 10px 0;color:#409eff;"v-for="store of item.storeInfo">{{store.storetag}}</el-tag>
                        </p> -->
            </li>
          </ul>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="cancelVipCoupon">
              取消
            </el-button>
            <el-button type="primary" @click="cancelVipCoupon">确定</el-button>
          </span>
        </el-dialog>

        <!--选择优惠-->
        <el-dialog
          title="选择优惠券"
          :visible.sync="bk_is_xuanze_youhui"
          width="35%"
          top="7vh"
        >
          <div class="xuanze_qunayi_font0" @click="bindOffer(8)">
            不使用优惠
          </div>
          <div class="xuanze_qunayi_font1" @click="bindOffer(9)">
            <div class="xuanze_qunayi_font2">优惠金额</div>
            <div class="xuanze_qunayi_font3">修改小计金额</div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="bk_quxiao_xuanze_youhui">
              取消
            </el-button>
            <el-button type="primary" @click="bk_xuanze_youhui_save">
              确定
            </el-button>
          </span>
        </el-dialog>
        <!--以上是收银台办卡页面的模态框-->

        <!--选择销售模态框-->
        <el-dialog
          title="选择销售"
          :visible.sync="zhk_xiao_shou"
          width="35%"
          top="7vh"
        >
          <div class="xuanze_jishi_search">
            <el-input
              placeholder="输入销售名称"
              suffix-icon="el-icon-search"
            ></el-input>
          </div>
          <div style="height: calc(100vh - 500px); overflow: auto">
            <div class="xuazne_xiaoshou" v-for="(value , index) in xiaoshous ">
              <el-radio
                v-model="zhk_check1"
                style="height: 25px; width: 25px"
              ></el-radio>
              <span style="cursor: pointer" @click="zhk_chioce_xiaoshou(index)">
                {{value.name}}
              </span>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="zhk_xiaoshou_over">
              取消
            </el-button>
            <el-button type="primary" @click="zhk_xiaoshou_save">
              确定
            </el-button>
          </span>
        </el-dialog>

        <!--取单页面-->
        <el-dialog
          title="取单(待付款)"
          :visible.sync="isTakeOrder"
          width="840px"
          top="7vh"
          custom-class="fetchOrderDialog"
        >
          <div slot="title" class="fetchOrderClass">
            <div class="block">
              <el-date-picker
                v-model="fetchOrderTime"
                type="datetimerange"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="['08:00:00']"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                @change="queryFetchOrder"
              ></el-date-picker>
            </div>
            <div style="line-height: 40px; margin-left: 140px; font-size: 18px">
              取单(待付款)
            </div>
          </div>
          <div style="width: 100%; margin-bottom: 16px">
            <el-input
              placeholder="请输入订单号、会员昵称、备注名、会员编号或手机号"
              v-model="fetchOrderKeyword"
              @input="inputQueryFetchOrder"
              @keyup.enter.native="inputQueryEnterFetchOrder"
              suffix-icon="el-icon-search"
            ></el-input>
          </div>
          <el-table
            :data="takeOrder"
            :header-cell-style="headerClass"
            height="300"
            size="mini"
            :row-class-name="takeOrderName"
          >
            <el-table-column label="订单号/开单时间" width="210">
              <template slot-scope="scope">
                <p>{{scope.row.order_number}}</p>
                <p>{{scope.row.order_time}}</p>
              </template>
            </el-table-column>
            <el-table-column label="客户">
              <template slot-scope="scope">
                <p v-if="scope.row.buyer.member_name">
                  {{scope.row.buyer.member_name}}
                </p>
                <p v-else>无</p>
              </template>
            </el-table-column>
            <el-table-column
              prop="address"
              label="商品"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <p v-for="(item,index) in scope.row.orderItems">
                  {{item.name}}
                </p>
              </template>
            </el-table-column>
            <el-table-column prop="address" label="理疗师">
              <template slot-scope="scope">
                <div v-for="(item,index) in scope.row.orderItems">
                  <p v-for="(name,i) in item.staffname">
                    <span>{{name.nickname}}</span>
                  </p>
                  <span v-if="item.staffname && item.staffname.length==0">
                    无
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="receivable" label="金额">
              <template slot-scope="scope">
                <p>{{scope.row.receivable | filterMoney}}</p>
              </template>
            </el-table-column>
            <el-table-column prop="address" label="操作" width="140">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  size="mini"
                  @click="bindBillingReceipt(scope.row)"
                >
                  开单
                </el-button>
                <!-- <el-button
                  type="primary"
                  style="margin-left: 0px"
                  size="mini"
                  @click="seeCostMaterialPrint(scope.row)"
                >
                  耗材
                </el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>

        <!-- 点击充值收款后出现开单框 -->
        <template v-if="buy_receipt">
          <app-pay
            :buy-receipt="buy_receipt"
            :login-info="loginInfo"
            :use-card="isRechargeCard"
            :order-no="orderNo"
            :is-pay-status="isPayStatus"
            :is-debt-flag="isDebtFlag"
            @close-pay="bindClosePay"
          ></app-pay>
        </template>

        <!-- 收银台选择赠送弹框 -->
        <template v-if="isChooseGift">
          <app-gift
            :login="loginInfo"
            :gift-data-array="billGiftData"
            :member="isactive1==0?memberInfo:isactive1==1?memberObjData[0]:cz_huiyuanxinxi"
            @show-choose-gift="showChooseGift"
            @choose-gift-data="chooseGiftData"
          ></app-gift>
        </template>

        <!-- 修改员工业绩 -->
        <el-dialog
          :visible.sync="isModifyPerformance"
          fullscreen
          :show-close="true"
          class="cz_kaidan"
          append-to-body
        >
          <div class="kaidan">
            <div slot="title">
              <div
                class="cz_qudan_top"
                style="background: #f6f6f6; color: #333"
              >
                <div style="flex: 1; text-align: center">修改员工业绩</div>
              </div>
            </div>
            <ul class="performance_list">
              <li
                v-for="(performanceList,inde) in performanceList"
                class="performance_list_item"
              >
                <div class="performance_card">
                  <div>{{inde+1}}</div>
                  <div>{{performanceList.name}}</div>
                  <div>单价：{{performanceList.price}}</div>
                  <div>数量：{{performanceList.num}}</div>
                  <div>
                    优惠权益：
                    <span v-if="performanceList.equity_type == 1">
                      未使用权益
                    </span>
                    <span v-else-if="performanceList.equity_type == 2">
                      折扣
                    </span>
                    <span v-else-if="performanceList.equity_type == 3">
                      抵价
                    </span>
                    <span v-else="performanceList.equity_type == 4">
                      优惠金额
                    </span>
                  </div>
                  <div>实际收款：{{performanceList.Subtotal}}</div>
                </div>
                <div
                  v-show="zuhekaPerformance !=6||performanceList.salesmen.length != 0"
                >
                  <ul class="performance_title">
                    <li style="width: 15%; text-align: center">角色</li>
                    <li style="width: 6%">#</li>
                    <li style="width: 16%">人员</li>
                    <li style="width: 15%">提成方式</li>
                    <li style="width: 25%">业绩金额</li>
                    <li style="width: 25%">提成金额</li>
                    <li style="width: 10%; text-align: center">操作</li>
                  </ul>
                  <div class="performance_row">
                    <div>
                      <el-button
                        type="text"
                        @click="chooseSales(performanceList,inde)"
                      >
                        选择销售
                      </el-button>
                    </div>
                    <ul>
                      <li
                        v-for="(salesmen,index) in performanceList.salesmen"
                        style="height: 40px; line-height: 40px"
                      >
                        <div style="width: 4%">{{index+1}}</div>
                        <div style="width: 14%">{{salesmen.staffName}}</div>
                        <div v-if="salesmen.deduct_way == 1" style="width: 10%">
                          比例提成
                        </div>
                        <div v-if="salesmen.deduct_way == 2" style="width: 10%">
                          固定提成
                        </div>
                        <div class="performance_group_input" style="width: 25%">
                          <el-input
                            v-model="salesmen.performance"
                            @keyup.native="limitInputMoney($event),limitInput($event)"
                            type="number"
                            step=".01"
                          >
                            <template slot="append">元</template>
                          </el-input>
                          <el-input
                            v-model="salesmen.performance_proportion"
                            @keyup.native="limitInputPer($event),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </div>
                        <div class="performance_group_input" style="width: 25%">
                          <el-input
                            v-model="salesmen.commission"
                            @keyup.native="limitInputMoney1($event),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">元</template>
                          </el-input>
                          <el-input
                            v-model="salesmen.commission_proportion"
                            @keyup.native="limitInputPer1($event),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </div>
                        <div style="width: 9%; text-align: center">
                          <span
                            @click="delectsalesmen(salesmen,index,inde)"
                            style="cursor: pointer"
                          >
                            删除
                          </span>
                        </div>
                      </li>
                      <li
                        style="height: 40px; line-height: 40px"
                        v-for="(items,index) in performanceList.addSalesmen"
                      >
                        <div style="width: 4%">{{items.lengthh}}</div>
                        <div style="width: 14%">{{items.staffName}}</div>
                        <div style="width: 10%">
                          <el-select
                            v-model="items.deduct_way"
                            placeholder="请选择"
                            @change="chooseDeductType($event,index,inde)"
                            class="choose_deduct"
                          >
                            <el-option
                              v-for="item in deductType"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </div>
                        <div class="performance_group_input" style="width: 25%">
                          <el-input
                            v-model="items.performance"
                            @keyup.native="limitInputMoneyAdd($event,items,index),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">元</template>
                          </el-input>
                          <el-input
                            v-model="items.performance_proportion"
                            @keyup.native="limitInputPerAdd($event,items,index),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </div>
                        <div class="performance_group_input" style="width: 25%">
                          <el-input
                            v-model="items.commission"
                            @keyup.native="limitInputMoneyAdd1($event,items,index),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">元</template>
                          </el-input>
                          <el-input
                            v-model="items.commission_proportion"
                            @keyup.native="limitInputPerAdd1($event,items,index),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </div>
                        <div style="width: 9%"></div>
                      </li>
                    </ul>
                  </div>
                  <div
                    class="performance_row"
                    v-show="performanceList.type ==1"
                  >
                    <div>
                      <el-button
                        type="text"
                        @click="chooseCrafts(performanceList,inde)"
                      >
                        选择手艺人
                      </el-button>
                    </div>
                    <ul>
                      <li
                        v-for="(technicians,index) in performanceList.technicians"
                        style="height: 40px; line-height: 40px"
                      >
                        <div style="width: 4%">{{index+1}}</div>
                        <div style="width: 14%">
                          {{technicians.staffName}}
                          <el-button
                            type="info"
                            round
                            v-if="technicians.assign == 1"
                            size="mini"
                            style="padding: 4px 6px"
                          >
                            点客
                          </el-button>
                        </div>
                        <div
                          v-if="technicians.deduct_way == 1"
                          style="width: 10%"
                        >
                          比例提成
                        </div>
                        <div
                          v-if="technicians.deduct_way == 2"
                          style="width: 10%"
                        >
                          固定提成
                        </div>
                        <div class="performance_group_input" style="width: 25%">
                          <el-input
                            v-model="technicians.performance"
                            @keyup.native="limitInputMoney($event),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">元</template>
                          </el-input>
                          <el-input
                            v-model="technicians.performance_proportion"
                            @keyup.native="limitInputPer($event),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </div>
                        <div class="performance_group_input" style="width: 25%">
                          <el-input
                            v-model="technicians.commission"
                            @keyup.native="limitInputMoney1($event),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">元</template>
                          </el-input>
                          <el-input
                            v-model="technicians.commission_proportion"
                            @keyup.native="limitInputPer1($event),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </div>
                        <div style="width: 9%; text-align: center">
                          <span
                            @click="delectCrafts(technicians,index,inde)"
                            style="cursor: pointer"
                          >
                            删除
                          </span>
                        </div>
                      </li>
                      <li
                        style="height: 40px; line-height: 40px"
                        v-for="(items,index) in performanceList.addCrafts"
                      >
                        <div style="width: 4%">{{items.lengthh}}</div>
                        <div style="width: 14%">{{items.staffName}}</div>
                        <div style="width: 10%">
                          <el-select
                            v-model="items.deduct_way"
                            placeholder="请选择"
                            @change="chooseDeductType($event,index,inde)"
                            class="choose_deduct"
                          >
                            <el-option
                              v-for="item in deductType"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </div>
                        <div class="performance_group_input" style="width: 25%">
                          <el-input
                            v-model="items.performance"
                            @keyup.native="limitInputMoneyAdd($event,items,index),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">元</template>
                          </el-input>
                          <el-input
                            v-model="items.performance_proportion"
                            @keyup.native="limitInputPerAdd($event,items,index),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </div>
                        <div class="performance_group_input" style="width: 25%">
                          <el-input
                            v-model="items.commission"
                            @keyup.native="limitInputMoneyAdd1($event,items,index),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">元</template>
                          </el-input>
                          <el-input
                            v-model="items.commission_proportion"
                            @keyup.native="limitInputPerAdd1($event,items,index),limitInput($event)"
                            type="number"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </div>
                        <div style="width: 9%"></div>
                      </li>
                    </ul>
                  </div>
                </div>
              </li>
            </ul>
            <div
              slot="footer"
              class="dialog-footer"
              style="display: block; text-align: center"
            >
              <el-button @click="isModifyPerformance = false">取 消</el-button>
              <el-button type="primary" @click="saveModify">保 存</el-button>
            </div>
          </div>
        </el-dialog>

        <!-- 选择销售弹框 -->
        <el-dialog
          title="选择人员"
          :visible.sync="isSales"
          width="40%"
          class="kd_add_box"
        >
          <div class="sales_content">
            <el-checkbox-group
              v-for="(items,index) in AllSales"
              :key="index"
              v-model="salesChecked"
            >
              <el-checkbox
                :label="index"
                border
                :disabled="items.isDisabled?true:false"
              >
                {{items.nickname}}
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="isSales = false">取 消</el-button>
            <el-button type="primary" @click="addSalesmen">确 定</el-button>
          </span>
        </el-dialog>

        <!-- 选择手艺人弹框 -->
        <el-dialog
          title="选择人员"
          :visible.sync="isCrafts"
          width="40%"
          class="kd_add_box"
        >
          <div class="sales_content">
            <el-checkbox-group
              v-for="(items,index) in AllCrafts"
              :key="index"
              v-model="craftsChecked"
            >
              <el-checkbox
                :label="index"
                border
                :disabled="items.isDisabled?true:false"
              >
                {{items.nickname}}
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="isCrafts = false">取 消</el-button>
            <el-button type="primary" @click="addCrafts">确 定</el-button>
          </span>
        </el-dialog>

        <!-- 查看服务耗材 -->
        <el-dialog title="查看耗材" :visible.sync="isCostMaterial" width="50%">
          <div style="margin-bottom: 20px; font-size: 20px">
            <span v-if="costMaterialData && costMaterialData.orderNo">
              订单号：{{costMaterialData.orderNo}}
            </span>
            <div
              v-if="costMaterialData && costMaterialData.data && costMaterialData.hasOutData && costMaterialData.hasOutData.length==0 && costMaterialData.data.length==0"
            >
              订单内无服务
            </div>
          </div>
          <div class="costMaterial">
            <div
              v-if="costMaterialData.data"
              v-for="item in costMaterialData.data"
            >
              <div style="font-size: 16px; margin-bottom: 8px">
                <span v-if="item.service_name">
                  服务名称：{{item.service_name}}
                </span>
              </div>
              <el-table
                border
                empty-text="未关联耗材"
                :data="item.consumablesData"
                style="width: 100%; margin-bottom: 8px"
              >
                <el-table-column
                  align="center"
                  label="耗材名称"
                  :show-overflow-tooltip="true"
                >
                  <template slot-scope="scope">
                    <span>{{scope.row.productinfo.product_name}}</span>
                  </template>
                </el-table-column>
                <el-table-column label="单位" align="center">
                  <template slot-scope="scope">
                    <span>{{scope.row.productinfo.unit}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="num" label="数量" align="center">
                  <template slot-scope="scope">
                    <span v-if="scope.row.consumableStatus==1">
                      {{scope.row.num}}
                    </span>
                    <span v-if="scope.row.consumableStatus==2">
                      {{scope.row.num}}(本店无)
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div
              v-if="costMaterialData.hasOutData"
              v-for="item in costMaterialData.hasOutData"
            >
              <div style="font-size: 16px; margin-bottom: 8px">
                <span v-if="item.service_name">
                  服务名称：{{item.service_name}}
                </span>
              </div>
              <el-table
                border
                empty-text="未关联耗材"
                :data="item.consumablesData"
                style="width: 100%; margin-bottom: 8px"
              >
                <el-table-column
                  align="center"
                  label="耗材名称"
                  :show-overflow-tooltip="true"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.type==2" style="color: #3363FF">
                      (退)
                    </span>
                    <span>{{scope.row.productinfo.product_name}}</span>
                  </template>
                </el-table-column>
                <el-table-column label="单位" align="center">
                  <template slot-scope="scope">
                    <span>{{scope.row.productinfo.unit}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="num" label="数量" align="center">
                  <template slot-scope="scope">
                    <span v-if="scope.row.consumableStatus==1">
                      {{scope.row.num}}
                    </span>
                    <span v-if="scope.row.consumableStatus==2">
                      {{scope.row.num}}(本店无)
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <span slot="footer">
            <el-button
              style="width: 100%"
              v-if="isTakeOrder || isVerify"
              type="primary"
              @click="takeOrderPrintMaterial"
            >
              打印耗材
            </el-button>
          </span>
        </el-dialog>

        <!--默认打印耗材-->
        <app-cost
          style="display: none"
          :store="loginInfo"
          :cost="costMaterialData"
        ></app-cost>

        <!-- 办卡选择耗卡 -->
        <!-- <el-dialog title="选择耗卡" class="select-offer" :visible.sync="isCardChooseCostCard" width="500px" top="7vh"
                   :close-on-click-modal='false'>
            <div class="xuanze_qunayi_font0" @click="cardCostCard(1,1,1)">不使用耗卡</div>
            <div class="xuanze_qunayi_font1" @click="cardCostCard(0,1,1)">
                <div class="xuanze_qunayi_font2">默认账户</div>
            </div>
            <ul  class="use-offer-ul">
                <li class="use-offer-li" v-for="(item,index) of balanceCard"  v-if='item.cardid>0' @click="cardCostCard(2,item,index)">
                    <p class="offer-cardName">
                        <span>{{item.card_info}}</span>
                    </p>
                    <p style="flex: 1;display: flex;justify-content: space-between">
                        <span >￥{{item.residuebalance/100}}</span>
                        <span>{{item.indate}}</span>
                    </p>
                </li>
            </ul>
            <span slot="footer" class="dialog-footer">
                <el-button class="cancel-btn" @click="cancelCardCostCard">取消</el-button>
                <el-button type="primary" @click="cancelCardCostCard">确定</el-button>
            </span>
        </el-dialog> -->
      </div>
    </div>
  </body>
  <script src="vue/vue2.5.16.js"></script>
  <script src="vue/element/<EMAIL>"></script>
  <script src="js/plugin/jquery-3.2.1.min.js"></script>
  <script src="js/unocss.theme.js"></script>
  <script src="js/plugin/<EMAIL>"></script>
  <script src="component/components.js"></script>
  <script src="print/print.js"></script>
  <script type="text/javascript" src="js/plugin/LodopFuncs.js"></script>
  <script src="js/plugin/Qrcode.js"></script>
  <script src="js/plugin/JsBarcode.js"></script>
  <script src="js/billing.js"></script>
</html>
