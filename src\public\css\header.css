.headerBg {
  background-color: #ebeef7;
}

.icon_color {
  cursor: pointer;
  height: 60px;
  margin-left: 1px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icons_box {
  height: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.icons_box .iconfont {
  width: 60px;
}

.icons_box .iconfont:hover,
.cashier .li2:hover,
.menu_font .menu_font_nav:hover {
  background: rgba(92, 54, 255, 0.10);
}

.icons_box .iconfont.iconclose:hover {
  color: #fff;
  background: rgb(232, 17, 35);
}

.iframe {
  width: 100vw;
  height: calc(100vh - 60px);
  overflow: hidden;
  position: relative;
}

iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.menu {
  height: 60px;
  background-color: #ebeef7;
  display: flex;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

.menu_font {
  display: flex;
  box-sizing: border-box;
}

.menu_font .menu_font_nav {
  margin-top: 4px;
  width: 100px;
  height: 52px;
  color: #1c2024;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.f-nav-bg {
  top: 4px;
  width: 100px;
  height: 52px;
  box-shadow: 8px 8px 20px -5px #1042e8 inset;
}

.menu_font .menu_font_nav.addclass {
  color: #fff;
}

.cashier {
  background: #ebeef7;
  height: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  box-sizing: border-box;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

.cashier .li1 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.li1_img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
}

.cashier .li2 {
  font-size: 16px;
  max-width: 100px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0 10px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.item_title {
  font-size: 14px;
  color: #999;
  margin-left: 3%;
  line-height: 60px;
}

.item_cont {
  font-size: 18px;
  color: #333;
  margin-left: 3%;
}

.sale_box {
  width: 100%;
  box-sizing: border-box;
}

.sale_cont {
  width: 100%;
  display: flex;
  flex-flow: row wrap;
}

.sale_list {
  width: 19%;
  padding: 1% 3%;
}

.sale_name {
  font-size: 14px;
  color: #999;
  line-height: 30px;
}

.sale_price {
  font-size: 14px;
  color: #333;
  line-height: 30px;
}

.handOver-list {
  display: flex;
  box-sizing: border-box;
  padding: 15px 0;
  border-bottom: 1px solid #f6f6f6;
  font-size: 14px;
}

.handOver-lebel {
  width: 80px;
}

.handOver-info {
  flex: 1;
}

.bindPrint.el-button:hover,
.bindPrint.el-button:active,
.bindPrint.el-button:focus {
  color: #606266;
  border-color: #dcdfe6;
  background-color: transparent;
}
