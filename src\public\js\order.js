var baseUrl = localStorage.getItem("fdb-domainName") + "/index.php?s=";

// 0 待付款
const orderTable = [];

// 1：品项（产品服务开单），2：品项（产品服务开单）3：购买卡项，4：充值 5：充卡6直接收款
const labelArr = ff_util.OrderType.labelOptions;

const orderDetails = [
  {
    id: 0,
    name: "美白",
    price: "12.5",
    num: 1,
  },
  {
    id: 0,
    name: "美白",
    price: "12.5",
    num: 1,
  },
];

var app = new Vue({
  el: "#order",
  data: {
    istbz: top.app.istbz,
    loadingOrderTable: false,
    loadingRefundList: false,
    loading3: false,
    url: baseUrl,
    //跳到Pay
    billToPay: 0,
    loginInfo: {},
    tabCur: 4,
    orderTable: orderTable,
    labelArr: labelArr,
    typePopover: false,
    curLabel: "",
    typeIndex: 0,
    limit: 10,
    currentPage: 1,
    allCount: 0,
    orderNo: 0,
    buy_receipt: false,
    isRechargeCard: false,

    //详情
    isDetails: false,
    orderDetails: {},
    //打印
    isPrint: false,
    key_num: "",
    paperwidth: 0, //打印纸宽度
    printSet: [], // 设置打印

    //发货
    isdeliverBox: false,
    value: true,
    logisticCompanys: [], //所有物流公司
    companyName: "", //公司名称
    logisticsNum: "", //物流单号
    addressInfo: {}, //地址信息
    orderId: "",

    //自提
    isStoreBox: false,

    //修改员工业绩
    isModifyPerformance: false,
    zuhekaPerformance: 1, //所有业绩提成信息
    totalPerformance: {},
    performanceList: [], //业绩提成信息列表
    salesmenList: [], //销售业绩列表
    techniciansList: [], //服务人员业绩列表
    isSales: false, //选择销售弹框
    isCrafts: false, //选择服务人员弹框
    isHandelIndex: 0, //每条服务在列表中的下标
    AllSales: [], //所有销售和服务人员
    AllCrafts: [], //所有服务人员
    deductType: [
      {
        name: "比例提成",
        id: 1,
      },
      {
        name: "固定提成",
        id: 2,
      },
    ], //提成方式下拉框
    isDeductType: "", //选择的提成方式
    salesChecked: [], //选中的销售存储
    craftsChecked: [], //存储选中的服务人员
    allDelect: [],
    saveModifyArr: [], //存储修改的数据
    addArr: [], //存储增添的数据
    delArr: [], //存储删除的数据
    loginModify: undefined,

    isStaffCollect: false, //服务人员代收
    staffCollectData: {},
    orderInfo: {},

    //address_info,判断是否为空
    addressInfoFlag: true,

    //还款
    isDebt: false,

    leftMenu: [{ word: "订单列表" }, { word: "退款申请" }],
    isActive: 0,

    //退款
    refundList: [], //退款数组
    refundListCount: 0, //退款数组长度
    currentRefundPage: 1,
    refundOptions: [
      { label: "全部状态", value: "" },
      { label: "已通过", value: 1 },
      { label: "未审核", value: 2 },
      { label: "已拒绝", value: 3 },
    ],
    refundOptionsValue: "",
    refundDateRange: "",
    refundKeyNum: "",
    isApply: false,
    refundApplyData: false,

    //退款按钮
    isRefundGoods: false, //退款商品弹框
    isRefundCard: false, //查看退款卡项弹框
    refundCardData: {}, //退款卡项弹框中的数据
    isRefundMoney: false, //主动退款的弹框，终于到退款了
    refundMoneyForm: {
      maxMoney: 0,
      money: 0,
      // refundType:1,
      remark: "",
    },
    deductionArr: [],
    deductionNum: 0,
    amountArr: [],
    refundPayment: [],
    refundPayMode: 0,
    // Refund:[
    //     {id:1,name:'现金退还'},
    //     {id:0,name:'原路返回'}
    // ]
    refundCoupon: 0, //是否退优惠券：0 否 1 是
    repayTotalMoney: 0,
    presentRefund: 1, //默认是否退回
    refundRemarks: "", //订单备注
    isRefundPassword: false, //退款确认密码弹框
    refundPassword: "", //退款确认密码
    refundData: {},
  },

  mounted: function () {
    this.getLoginInfo();
    this.getInit();
    this.getReceiptSet();
    this.handleUrl();
    this.loginModify = global.login;
  },

  methods: {
    getOrderTypeName(id) {
      return ff_util.OrderType.getOrderTypeName(id);
    },
    getOrderTypeColor(id) {
      return ff_util.OrderType.getOrderTypeColor(id);
    },
    getmenu: function (index) {
      this.isActive = index;
      if (this.isActive == 1) {
        this.getRefundList();
      } else {
        this.isFlag = true;
      }
    },

    toCustomerPage(id) {
      let href = "huiyuan.html?id=" + id;
      top.app.toPage(href);
    },

    formatMoney(money) {
      return ff_util.formatMoney(money);
    },

    handleRowClick(row) {
      this.$refs.orderTable.toggleRowExpansion(row);
    },
    // 计算table高度
    updateTableHeight(lenght) {
      const headerHeight = 36 + 1;
      const rowHeight = 36;
      return lenght
        ? headerHeight + rowHeight * lenght
        : headerHeight + rowHeight;
    },

    //处理url
    handleUrl: function () {
      var url = location.search;
      if (url.indexOf("=") != -1) {
        let row = {};
        row.id = url.split("=")[1];
        this.bindSeeDetails(row);
        //解决跳转到预约页面时，header中的选中样式还在会员的问题
        //0:收银台 1:商品 2:订单 3:会员 4:预约 5:通知
        try {
          parent.window.activeHeader(2);
        } catch (e) {}
      }
    },

    //搜索框输入
    handleSearch: function () {
      this.currentPage = 1;
      this.getInit();
    },

    getLoginInfo: function () {
      if (localStorage.getItem("loginInfo")) {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
      } else {
        this.$message({
          type: "error",
          message: "请登录",
          duration: 1500,
        });
      }
    },

    getInit: function () {
      let _self = this;
      _self.loadingOrderTable = true;
      $.ajax({
        url: _self.url + "/android/Orderlist/OrderList",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid, //  商户id
          limit: _self.limit, // 美业条数
          order_type: _self.curLabel, // 订单类型 1服务 2产品 3售卡 4充值
          page: _self.currentPage, // 页数
          storeid: _self.loginInfo.storeid, // 门店id
          switchs: _self.tabCur, // 状态 0全部 1代付款 2代发货 3已发货 4已完成 5已取消 6代收款 7待核销
          key_num: _self.key_num,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res && res.code == 0) {
            _self.orderTable = res.data;
            _self.allCount = res.count;
          }
        },
        error: function (err) {},
        complete: () => {
          _self.loadingOrderTable = false;
        },
      });
    },

    //获取退款列表接口
    getRefundList: function () {
      let _self = this;
      _self.loadingRefundList = true;
      $.ajax({
        url: _self.url + "/android/Refund/getRefundList",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid, //  商户id
          storeid: _self.loginInfo.storeid, // 门店id
          starttime: _self.refundDateRange ? _self.refundDateRange[0] : "", // 	开始时间（筛选用，默认空）
          endtime: _self.refundDateRange ? _self.refundDateRange[1] : "", // 		结束时间（筛选，默认空）
          limit: _self.limit, // 每页条数
          page: _self.currentRefundPage, // 页数
          status: _self.refundOptionsValue, // 审核状态：1 审核通过 2 审核中/未审核 3 已拒绝通过 （帅选用，默认空）
          order_number: _self.refundKeyNum,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res && res.code == 0) {
            _self.refundList = res.data;
            _self.refundListCount = res.count;
          }
        },
        error: function (err) {},
        complete: () => {
          _self.loadingRefundList = false;
        },
      });
    },

    bindTabCur: function () {
      this.key_num = "";
      this.currentPage = 1;
      this.getInit();
      this.$nextTick(() => {
        this.$refs.orderTable.bodyWrapper.scrollTop = 0;
      });
    },

    //设置table颜色
    headerClass: function ({ row, rowIndex }) {
      return "background:#f6f6f6;color:#333;fontSize:16px;font-weight:600";
    },

    bindLabel: function (data, index) {
      this.curLabel = data.id;
      this.currentPage = 1;
      this.getInit();
      this.typeIndex = index;
      this.typePopover = false;
    },

    bindIsTypeCancel: function () {
      this.typePopover = false;
    },

    //分页
    handleSizeChange(val) {},
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getInit();
      this.$nextTick(() => {
        this.$refs.orderTable.bodyWrapper.scrollTop = 0;
      });
    },

    //退款列表查询订单号
    handleRefundSearch: function () {
      this.currentRefundPage = 1;
      this.getRefundList();
    },

    //筛选退款列表的状态
    changeRefundOptions: function (val) {
      this.getRefundList();
    },

    //筛选退款列表的申请时间
    cahngeRefundDateRange: function (val) {
      this.getRefundList();
    },

    //退款列表改变页数
    handleRefundCurrentChange: function (val) {
      this.currentRefundPage = val;
      this.getRefundList();
    },

    //查看和审核的退款列表详情
    getRefundDetail: function (row) {
      let _self = this;
      let id = row.id;
      $.ajax({
        url: _self.url + "/android/Refund//getRefundFind",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          id: id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.refundApplyData = res.data;
            _self.isApply = true;
          }
        },
        error: function (err) {},
      });
    },

    //退款列表查看订单详情
    seeRefundDetail: function (row) {
      this.isDetails = true;
      let id = row.order_id;
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Orderlist/OrderDetails",
        type: "POST",
        data: {
          id: id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.orderDetails = res.data;

            if (JSON.stringify(res.data.address_info) == "{}") {
              _self.addressInfoFlag = false;
            }
            if (_self.orderDetails.orderInfo.length > 0) {
              let total = 0;
              _self.orderDetails.orderInfo.forEach((item) => {
                let price = Number(item.price) * 100 * item.num;
                total = total + price;
              });
              _self.orderDetails.summation = total;
            }
          }
        },
        error: function (err) {},
      });
    },

    //拒绝退款
    refusedRefund: function () {
      let _self = this;
      $.ajax({
        url: _self.url + "/android/Refund/rejectAudit",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid, //  商户id
          storeid: _self.loginInfo.storeid, // 门店id
          id: _self.refundApplyData.id, // 申请信息id
          order_id: _self.refundApplyData.order_id, // 订单id
          refuse_reason: _self.refundApplyData.refuse_reason, // 审
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message({
              type: "success",
              message: res.msg,
              duration: 1500,
            });
            _self.isApply = false;
            _self.getRefundList();
          }
        },
        error: function (err) {},
      });
    },

    /**
     *  详情
     *
     * */

    bindSeeDetails: function (row) {
      this.loading3 = true;
      this.isDetails = true;
      var id = row.id;
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Orderlist/OrderDetails",
        type: "POST",
        data: {
          id: id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.orderDetails = res.data;

            if (JSON.stringify(res.data.address_info) == "{}") {
              _self.addressInfoFlag = false;
            }
            if (_self.orderDetails.orderInfo.length > 0) {
              let total = 0;
              _self.orderDetails.orderInfo.forEach((item) => {
                let price = Number(item.price) * 100 * item.num;
                total = total + price;
              });
              _self.orderDetails.summation = total;
            }
          }
        },
        error: function (err) {},
        complete: () => {
          _self.loading3 = false;
        },
      });
    },

    bindCancelDetails: function (obj) {
      this.isDetails = obj;
      //console.log(obj);
    },

    bindPrint: function () {
      console.log("bindPrint", this.orderDetails);
      this.isPrint = true;
    },

    /**小票**/
    bindPrintCancen: function () {
      this.isPrint = false;
    },

    bindPrintConfirm: function () {
      // this.isPrint = false;
      if (!LODOPbol) {
        this.noPrint();
        return;
      }
      var vm = this;
      if (this.printSet.length == 0) {
        Preview1();
      } else {
        // vm.printorderinfo = res.info;
        var str = $(vm.$refs.printorderstr).html();

        Preview2(str);
      }
    },

    // 没有安装打印机
    noPrint: function () {
      let self = this;
      self.$message({
        type: "error",
        message: "打印机未准备好,无法打印",
        duration: 1500,
        onClose: function () {
          LODOPbol = false;
        },
      });
    },

    // 获取小票样式
    getReceiptSet: function () {
      var _self = this;
      // console.log(this.loginInfo);
      $.ajax({
        url: _self.url + "/android/order/getReceiptSet",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.printSet = res.data;
            _self.paperwidth = res.data.width;
          } else {
            let data =
              '{"id":1,"storeid":1,"merchantid":1,"name":"默认小票","status":1,"addtime":"2020-01-01 08:00","set":[{"name":"store","set":{"fontSize":20}},{"name":"header","set":{"fontSize":12,"text":"收银小票","headerfontSize":18,"ordertype":1,"ordernum":1,"ordertime":1,"num":1,"cashier":1}},{"name":"goods","set":{"fontSize":12,"goodsname":1,"price":1,"num":1,"allmoney":1,"sku":1}},{"name":"vip","set":{"fontSize":12,"name":1,"cardnum":1,"money":1,"score":1}},{"name":"takegoods","set":{"fontSize":12,"name":1,"phone":1,"address":1,"remark":1,"fee":1}},{"name":"footer","set":{"fontSize":12,"paytime":1,"ordernum":1,"barcode":1,"needpay":1,"getmoney":1,"paytype":1,"coupon":1,"smallchange":1,"returnmoney":1}},{"name":"text","set":{"text":"谢谢惠顾，欢迎再次光临！","fontSize":20}}],"type":1,"width":180,"paperwidth":58,"setInfo":[{"name":"store","set":{"fontSize":20}},{"name":"header","set":{"fontSize":12,"text":"收银小票","headerfontSize":18,"ordertype":1,"ordernum":1,"ordertime":1,"num":1,"cashier":1}},{"name":"goods","set":{"fontSize":12,"goodsname":1,"price":1,"num":1,"allmoney":1,"sku":1}},{"name":"vip","set":{"fontSize":12,"name":1,"cardnum":1,"money":1,"score":1}},{"name":"takegoods","set":{"fontSize":12,"name":1,"phone":1,"address":1,"remark":1,"fee":1}},{"name":"footer","set":{"fontSize":12,"paytime":1,"ordernum":1,"barcode":1,"needpay":1,"getmoney":1,"paytype":1,"coupon":1,"smallchange":1,"returnmoney":1}},{"name":"text","set":{"text":"谢谢惠顾，欢迎再次光临！","fontSize":20}}]}';
            data = JSON.parse(data);
            _self.printSet = data;
            _self.paperwidth = data.width;
          }
        },
      });
    },

    //还款
    rePayOrder: function () {
      let orderNo = this.orderDetails.order_number;
      this.orderNo = orderNo;
      this.billToPay = 2;
      this.isDebt = true;
      this.buy_receipt = true;
    },

    //主动退款
    activeRefund: function () {
      let _self = this;
      _self.deductionNum = 0;
      _self.deductionArr = [];
      _self.amountArr = [];
      _self.refundPayment = JSON.parse(
        JSON.stringify(_self.orderDetails.payment)
      );
      if (_self.refundPayment.length > 0) {
        _self.refundPayment.forEach((item) => {
          item.refundMode = 0;
          item.repayMoney = item.trade_amount;
        });
      }
      if (_self.orderDetails && _self.orderDetails.orderInfo) {
        for (var i = 0; i < _self.orderDetails.orderInfo.length; i++) {
          let item = _self.orderDetails.orderInfo[i];
          if (item["equity_type"] == 3) {
            _self.deductionArr.push(JSON.parse(JSON.stringify(item)));
            _self.deductionNum += item["num"];
          } else {
            _self.amountArr.push(JSON.parse(JSON.stringify(item)));
          }
        }
      }
      _self.isRefundGoods = true;
    },

    //改变退款方式
    changeRefundType: function (val) {
      this.$forceUpdate();
      // this.refundPayment.forEach(item=>{
      //     console.log(item.refundType);
      // })
    },

    //改变输入框的值
    changeRefundMoney: function (index, val) {
      // console.log(index);
      // console.log(val);
      let money =
        Math.round(Number(val) * 100) <=
        Math.round(Number(this.refundPayment[index].trade_amount) * 100)
          ? val
          : this.refundPayment[index].trade_amount;
      let reg = /(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?/g;
      if (money == "0.0" || money == "00") {
        if (money == "00") {
          money = "0";
        } else {
          money = "0.";
        }
      } else if (money && money != "0" && money != "0.") {
        let result = money.match(reg);
        money = result[0];
      }

      this.refundPayment[index].repayMoney = money;
      let total = 0;
      this.refundPayment.forEach((item) => {
        // console.log(item.repayMoney)
        total = total + Math.round(Number(item.repayMoney) * 100);
      });
      this.repayTotalMoney = total;
      this.$forceUpdate();
    },

    focusMoney: function () {},

    //主动退款-下一步
    nextRefund: function () {
      this.isRefundMoney = true;
    },

    cancelRefundMoney: function () {
      this.isRefundMoney = false;
      this.isRefundGoods = false;
      this.isRefundCard = false;
      this.refundRemarks = "";
    },

    cancelComfirmRefund: function () {
      this.isRefundPassword = false;
      this.refundData = {};
      this.refundPassword = "";
    },

    //查看退款卡项详情
    activeRefundCard: function () {
      let _self = this;
      $.ajax({
        url: _self.url + "/android/Orderlist/getVipCardData",
        type: "POST",
        data: {
          merchantid: _self.loginInfo.merchantid, //商户id
          storeid: _self.loginInfo.storeid, //门店id
          memberId: _self.orderDetails.vip_id, //会员id
          order_id: _self.orderDetails.id, //订单id
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.refundCardData = res.data;

            _self.isRefundCard = true;
          }
        },
        error: function (err) {
          consle.log(err);
        },
      });
    },

    //退款
    checkRefund: function () {
      let _self = this;
      let refundData1 = {};
      refundData1["cashier_id"] = _self.loginInfo.id;
      refundData1["merchantid"] = _self.loginInfo.merchantid;
      refundData1["storeid"] = _self.loginInfo.storeid;
      refundData1["shift_no"] = _self.loginInfo.shift_no;
      refundData1["orderNo"] = _self.orderDetails.order_number;
      refundData1["orderType"] = 1;
      refundData1["presentRefund"] = _self.presentRefund;
      refundData1["presentRefund"] = _self.presentRefund;
      refundData1["refundCoupon"] = _self.refundCoupon;
      refundData1["remarks"] = _self.refundRemarks;
      let refunds = [];
      _self.refundPayment.forEach((item) => {
        refunds.push({
          amount: Math.round(Number(item.repayMoney) * 100),
          orderNo: _self.orderDetails.order_number,
          payNo: item.out_trade_no,
          payType: item.payType,
          refundMode: item.refundMode,
        });
      });
      refundData1["refunds"] = JSON.stringify(refunds);
      _self.refundData = refundData1;

      _self.isRefundPassword = true;
      _self.$nextTick(() => {
        _self.$refs.refundMoneyPass.focus();
      });
    },

    //确认退款
    comfirmRefund: function () {
      let _self = this;

      if (_self.refundPassword) {
        _self.refundData.password = _self.refundPassword;
        $.ajax({
          url: _self.url + "/android/Orderlist/orderRefund",
          type: "POST",
          data: _self.refundData,
          success: function (res) {
            // var res = JSON.parse(res);
            if (res.code == 1) {
              _self.$message({
                type: "success",
                message: res.msg,
                duration: 1500,
              });
              _self.isRefundPassword = false;
              _self.refundPassword = "";
              _self.cancelRefundMoney();
              _self.bindSeeDetails(_self.orderDetails);
              _self.getInit();
              _self.refundData = JSON.parse(JSON.stringify({}));
            } else {
              _self.$message({
                type: "error",
                message: res.msg,
                duration: 1500,
              });
            }
          },
          error: function (err) {},
        });
      } else {
        _self.$message({
          type: "warning",
          message: "密码不能为空",
          duration: 1500,
        });
      }
    },

    // 取消订单
    bindCancelOrder: function () {
      var _self = this;
      this.$confirm("是否取消订单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _self.cancelOrder();
        })
        .catch(() => {});
    },

    //直接收款
    bindDirectPay: function () {
      let orderNo = this.orderDetails.order_number;
      this.orderNo = orderNo;
      this.billToPay = 2;
      this.buy_receipt = true;
    },

    //关闭收款
    bindClosePay: function (flag) {
      this.bindSeeDetails(this.orderDetails);
      this.buy_receipt = flag;
    },

    //修改订单
    bindModifyOrder: function () {
      var _self = this;
      _self.loading = true;
      var orderNo = _self.orderDetails.order_number;
      var vip_id = _self.orderDetails.vip_id;
      // console.log('会员id', vip_id);
      // console.log('订单号', orderNo);
      _self.loading = true;
      // window.location.href = "cashier_system.html?orderNO="+orderNo+'&vip_id=' + vip_id;
      //window.location.href = "cashier_system.html?orderNO=" + orderNo + '&vip_id=' + vip_id;
      let href = "cashier_system.html?orderNO=" + orderNo + "&vip_id=" + vip_id;
      top.app.toPage(href);
      // localStorage.setItem('headerAtive', 0);
      // if (vip_id==0){
      //     window.location.href = "cashier_system.html?orderNO="+orderNo;
      // }else{
      //
      // }
      // location.href = "cashier_system.html"
      // window.location.href = "cashier_system.html?orderNO="+orderNo+'&vip_id=' + vip_id;
    },

    cancelOrder: function () {
      var _self = this;
      var vip_id = _self.orderDetails.vip_id;
      $.ajax({
        url: _self.url + "/api/Orderlist/newCancelOrder",
        type: "post",
        data: {
          id: _self.orderDetails.id,
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
          vipId: vip_id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.isDetails = false;
            _self.currentPage = 1;
            _self.getInit();
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (res) {},
      });
    },
    //自提
    StoreGoods: function () {
      var _self = this;
      this.isStoreBox = true;
      _self.addressInfo = _self.orderDetails.address_info;
    },
    //获取所有物流公司、发货
    deliverGoods: function () {
      var _self = this;
      this.isdeliverBox = true;
      _self.addressInfo = _self.orderDetails.address_info;
      $.ajax({
        url: _self.url + "/android/Orderlist/getLogistics",
        type: "post",
        success: function (res) {
          // var res = JSON.parse(res);
          _self.logisticCompanys = res.data;
        },
        error: function (e) {},
      });
    },
    chooseCompany: function (value) {
      var _self = this;
      _self.orderId = value;
    },
    //确认发货方法
    sendGoods: function () {
      var _self = this,
        delivery = 1;

      if (_self.value) {
        delivery = 1;
      } else {
        delivery = 2;
      }
      console.log(
        this.logisticsNum,
        this.orderId,
        this.orderDetails.dispatch_type,
        this.orderDetails.id,
        delivery
      );
      $.ajax({
        url: _self.url + "/android/Orderlist/deliverGoods",
        type: "post",
        data: {
          courier_num: _self.logisticsNum,
          delivery: delivery,
          dispatch_type: _self.orderDetails.dispatch_type,
          logistics: this.orderId,
          orderid: this.orderDetails.id,
        },
        success: function (res) {
          // var res = JSON.parse(res);

          _self.$message({
            type: "success",
            message: res.msg,
            duration: 1500,
          });
          _self.tabCur = 2;
          _self.bindTabCur();
          _self.isdeliverBox = false;
          _self.isStoreBox = false;
          _self.isDetails = false;
        },
        error: function (e) {},
      });
    },
    //开关控制物流信息
    changeStatus: function () {
      var _self = this;
      if (_self.value == false) {
        //清空输入信息
        _self.logisticsNum = "";
        _self.companyName = "";
        _self.orderId = "";
      }
    },
    //发货弹框关闭执行方法
    closeReceiptBox: function () {
      //清空输入信息
      var _self = this;
      _self.logisticsNum = "";
      _self.companyName = "";
      _self.value = true;
      _self.orderId = "";
    },

    //获取所有销售和服务人员
    getAllSales: function () {
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Deduct/getStaff",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.loading = false;
            _self.AllSales = res.data;
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
            _self.loading = false;
          }
        },
        error: function (error) {},
      });
    },

    //获取单据业绩提成记录
    modifyEmployeePerformance: function () {
      var _self = this;
      this.isModifyPerformance = true;
      // console.log(_self.loginInfo.merchantid,_self.loginInfo.storeid,_self.kd_xinxi_list.order_number)
      this.loading = true;
      $.ajax({
        url: _self.url + "/android/Deduct/getOrderDeductData",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderNo: _self.orderDetails.order_number,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.loading = false;
            _self.zuhekaPerformance = res.data.type;
            _self.totalPerformance = res.data;
            _self.performanceList = res.data.performance;
            console.log("🚀 ~ _self.performanceList:", _self.performanceList);
            let length = _self.performanceList.length - 1;
            for (let i = 0; i < _self.performanceList.length; i++) {
              _self.performanceList[i].salesChecked = [];
              _self.performanceList[i].craftsChecked = [];
              if (_self.performanceList.base_amount) {
              } else {
                _self.performanceList[i].base_amount =
                  _self.performanceList[length].base_amount;
              }
            }
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
            _self.loading = false;
          }
        },
        error: function (error) {},
      });
      _self.getAllSales();
    },

    //选择销售
    chooseSales: function (index) {
      var _self = this;
      this.isSales = true;
      this.isHandelIndex = index;
      _self.salesChecked =
        _self.performanceList[_self.isHandelIndex].salesChecked;
      for (let j = 0; j < _self.AllSales.length; j++) {
        Vue.delete(_self.AllSales[j], "isDisabled");
        for (
          let i = 0;
          i < _self.performanceList[_self.isHandelIndex].salesmen.length;
          i++
        ) {
          if (
            _self.performanceList[_self.isHandelIndex].salesmen[i].staff_id ==
            _self.AllSales[j].id
          ) {
            _self.AllSales[j].isDisabled = 1;
          }
        }
      }
    },
    //添加销售
    addSalesmen: function () {
      var _self = this;
      this.isSales = false;
      let salesmenLength =
        _self.performanceList[_self.isHandelIndex].salesmen.length;
      // console.log(_self.salesChecked,"下标")
      _self.performanceList[_self.isHandelIndex].addSalesmen = []; //存储添加的销售
      _self.performanceList[_self.isHandelIndex].salesChecked =
        _self.salesChecked;
      for (let i = 0; i < _self.salesChecked.length; i++) {
        salesmenLength += 1;
        _self.performanceList[_self.isHandelIndex].addSalesmen[i] = {
          staffName: _self.AllSales[_self.salesChecked[i]].nickname,
          lengthh: salesmenLength,
          assign: 2,
          base_amount: _self.performanceList[_self.isHandelIndex].base_amount,
          commission: 0.0,
          commission_proportion: 0.0,
          performance: 0.0,
          performance_proportion: 0.0,
          deduct_type: 1,
          deduct_way: 1,
          order_time: _self.totalPerformance.order_time,
          staff_id: _self.AllSales[_self.salesChecked[i]].id,
          storeid: _self.totalPerformance.storeid,
          merchantid: _self.totalPerformance.merchantid,
          id: 0,
          order_id: _self.performanceList[_self.isHandelIndex].order_id,
          order_detail_id: _self.performanceList[_self.isHandelIndex].id,
          goods_type:
            _self.zuhekaPerformance == 5 ? 2 : _self.zuhekaPerformance,
        };
      }
    },

    //选择服务人员
    chooseCrafts: function (performanceList, index) {
      var _self = this;
      this.isCrafts = true;
      this.isHandelIndex = index;
      _self.AllCrafts = _self.AllSales.filter(function (items, index, ar) {
        if (items.isTech == 2) {
          return items;
        }
      });
      _self.craftsChecked =
        _self.performanceList[_self.isHandelIndex].craftsChecked;
      for (let j = 0; j < _self.AllCrafts.length; j++) {
        Vue.delete(_self.AllCrafts[j], "isDisabled");
        for (
          let i = 0;
          i < _self.performanceList[_self.isHandelIndex].technicians.length;
          i++
        ) {
          if (
            _self.performanceList[_self.isHandelIndex].technicians[i]
              .staff_id == _self.AllCrafts[j].id
          ) {
            _self.AllCrafts[j].isDisabled = 1;
          }
        }
      }
    },
    //添加服务人员
    addCrafts: function () {
      var _self = this;
      this.isCrafts = false;
      let craftsLength =
        _self.performanceList[_self.isHandelIndex].technicians.length;
      _self.performanceList[_self.isHandelIndex].addCrafts = [];
      _self.performanceList[_self.isHandelIndex].craftsChecked =
        _self.craftsChecked;
      for (let i = 0; i < _self.craftsChecked.length; i++) {
        craftsLength += 1;
        _self.performanceList[_self.isHandelIndex].addCrafts[i] = {
          staffName: _self.AllCrafts[_self.craftsChecked[i]].nickname,
          lengthh: craftsLength,
          assign: 2,
          base_amount: _self.performanceList[_self.isHandelIndex].base_amount,
          commission: 0.0,
          commission_proportion: 0.0,
          performance: 0.0,
          performance_proportion: 0.0,
          deduct_type: 2,
          deduct_way: 1,
          order_time: _self.totalPerformance.order_time,
          staff_id: _self.AllCrafts[_self.craftsChecked[i]].id,
          storeid: _self.totalPerformance.storeid,
          merchantid: _self.totalPerformance.merchantid,
          id: 0,
          order_id: _self.performanceList[_self.isHandelIndex].order_id,
          order_detail_id: _self.performanceList[_self.isHandelIndex].id,
          goods_type:
            _self.zuhekaPerformance == 5 ? 2 : _self.zuhekaPerformance,
        };
      }
    },

    //选择提成方式
    chooseDeductType: function (e, sindex, lindex) {
      this.$forceUpdate();
    },

    //输入金额呈现百分比
    limitInputMoney: function (e, performanceIndex, salesmenIndex, isSalesmen) {
      let per = (
        (e.target.value * 10000) /
        this.performanceList[performanceIndex].base_amount
      ).toFixed(2);
      // $(e.path[2]).find("input")[1].value = per;
      if (isSalesmen) {
        // 销售
        this.performanceList[performanceIndex].salesmen[
          salesmenIndex
        ].performance_proportion = per.toString();
      } else {
        // 理疗师
        this.performanceList[performanceIndex].technicians[
          salesmenIndex
        ].performance_proportion = per.toString();
      }
      this.$forceUpdate();
    },
    limitInputMoneyAdd: function (
      e,
      performanceIndex,
      newSalesmenIndex,
      isSalesmen
    ) {
      /* $(e.path[2]).find("input")[1].value = (
        (parseInt(newSalesmen.performance) * 10000) /
        newSalesmen.base_amount
      ).toFixed(2); */
      // console.log("🚀 ~ newSalesmen.base_amount:", newSalesmen.base_amount);
      // console.log("🚀 ~ e.target.value:", e.target.value);
      let per = (
        (e.target.value * 10000) /
        this.performanceList[performanceIndex].base_amount
      ).toFixed(2);
      console.log("🚀 ~ per:", per);
      if (isSalesmen) {
        this.performanceList[performanceIndex].addSalesmen[
          newSalesmenIndex
        ].performance_proportion = per.toString();
      } else {
        this.performanceList[performanceIndex].addCrafts[
          newSalesmenIndex
        ].performance_proportion = per.toString();
      }
      this.$forceUpdate();
    },
    limitInputMoneyAdd1: function (
      e,
      performanceIndex,
      newSalesmenIndex,
      isSalesmen
    ) {
      let per = (
        (e.target.value * 10000) /
        this.performanceList[performanceIndex].base_amount
      ).toFixed(2);
      if (isSalesmen) {
        this.performanceList[performanceIndex].addSalesmen[
          newSalesmenIndex
        ].commission_proportion = per.toString();
      } else {
        this.performanceList[performanceIndex].addCrafts[
          newSalesmenIndex
        ].commission_proportion = per.toString();
      }
      this.$forceUpdate();
    },
    limitInputMoney1: function (
      e,
      performanceIndex,
      salesmenIndex,
      isSalesmen
    ) {
      let per = (
        (e.target.value * 10000) /
        this.performanceList[performanceIndex].base_amount
      ).toFixed(2);
      if (isSalesmen) {
        // 销售
        this.performanceList[performanceIndex].salesmen[
          salesmenIndex
        ].commission_proportion = per.toString();
      } else {
        // 理疗师
        this.performanceList[performanceIndex].technicians[
          salesmenIndex
        ].commission_proportion = per.toString();
      }
    },
    //输入百分比呈现金额
    limitInputPer: function (e, performanceIndex, salesmenIndex, isSalesmen) {
      let money = (
        (e.target.value * this.performanceList[performanceIndex].base_amount) /
        10000
      ).toFixed(2);
      if (isSalesmen) {
        // 销售
        this.performanceList[performanceIndex].salesmen[
          salesmenIndex
        ].performance = money.toString();
      } else {
        // 理疗师
        this.performanceList[performanceIndex].technicians[
          salesmenIndex
        ].performance = money.toString();
      }
    },
    limitInputPer1: function (e, performanceIndex, salesmenIndex, isSalesmen) {
      let money = (
        (Number(e.target.value) *
          this.performanceList[performanceIndex].base_amount) /
        10000
      ).toFixed(2);
      if (isSalesmen) {
        // 销售
        this.performanceList[performanceIndex].salesmen[
          salesmenIndex
        ].commission = money.toString();
      } else {
        // 理疗师
        this.performanceList[performanceIndex].technicians[
          salesmenIndex
        ].commission = money.toString();
      }
      this.$forceUpdate();
    },
    limitInputPerAdd: function (
      e,
      performanceIndex,
      salesmenIndex,
      isSalesmen
    ) {
      let money = (
        (Number(e.target.value) *
          this.performanceList[performanceIndex].base_amount) /
        10000
      ).toFixed(2);
      if (isSalesmen) {
        this.performanceList[performanceIndex].addSalesmen[
          salesmenIndex
        ].performance = money.toString();
      } else {
        this.performanceList[performanceIndex].addCrafts[
          salesmenIndex
        ].performance = money.toString();
      }
      this.$forceUpdate();
    },
    limitInputPerAdd1: function (
      e,
      performanceIndex,
      salesmenIndex,
      isSalesmen
    ) {
      let money = (
        (Number(e.target.value) *
          this.performanceList[performanceIndex].base_amount) /
        10000
      ).toFixed(2);
      if (isSalesmen) {
        this.performanceList[performanceIndex].addSalesmen[
          salesmenIndex
        ].commission = money.toString();
      } else {
        this.performanceList[performanceIndex].addCrafts[
          salesmenIndex
        ].commission = money.toString();
      }
      this.$forceUpdate();
    },

    //删除销售、服务人员
    delectsalesmen: function (info, index, inde) {
      var _self = this;
      _self.allDelect.push(_self.performanceList[inde].salesmen[index]);
      _self.performanceList[inde].salesmen.splice(index, 1);
      this.$forceUpdate();
    },
    delectCrafts: function (info, index, inde) {
      var _self = this;
      _self.allDelect.push(_self.performanceList[inde].technicians[index]);
      _self.performanceList[inde].technicians.splice(index, 1);
      this.$forceUpdate();
    },

    //提交保存修改的数据
    saveModify: function () {
      var _self = this;
      _self.loading = true;
      _self.saveModifyArr = [];
      _self.delArr = [];
      _self.addArr = [];
      //遍历拼接数组
      for (let i = 0; i < _self.performanceList.length; i++) {
        _self.saveModifyArr = _self.saveModifyArr.concat(
          _self.performanceList[i].salesmen,
          _self.performanceList[i].technicians
        );
        if (_self.performanceList[i].addSalesmen) {
          if (_self.performanceList[i].addCrafts) {
            _self.addArr = _self.addArr.concat(
              _self.performanceList[i].addSalesmen,
              _self.performanceList[i].addCrafts
            );
          } else {
            _self.addArr = _self.addArr.concat(
              _self.performanceList[i].addSalesmen
            );
          }
        } else {
          if (_self.performanceList[i].addCrafts) {
            _self.addArr = _self.addArr.concat(
              _self.performanceList[i].addCrafts
            );
          } else {
            _self.addArr = _self.addArr;
          }
        }
      }
      //删除多余的属性
      for (let i = 0; i < _self.addArr.length; i++) {
        Vue.delete(_self.addArr[i], "lengthh");
      }
      _self.delArr = _self.allDelect;
      _self.saveModifyArr = JSON.stringify(_self.saveModifyArr);
      _self.delArr = JSON.stringify(_self.delArr);
      _self.addArr = JSON.stringify(_self.addArr);
      $.ajax({
        url: _self.url + "/android/Deduct/saveDeductData",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderNo: _self.orderDetails.order_number,
          storeid: _self.loginInfo.storeid,
          addArr: _self.addArr,
          delArr: _self.delArr,
          saveArr: _self.saveModifyArr,
          nickname: _self.loginInfo.nickname,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.loading = false;
            _self.$message({
              message: res.msg,
              type: "success",
              duration: 1500,
            });
            _self.isModifyPerformance = false;
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
            _self.loading = false;
          }
        },
        error: function (error) {},
      });

      // console.log(_self.addArr)
    },

    //服务人员代收弹框
    staffCollection: function (id, name, orderInfo) {
      let _self = this;

      _self.orderInfo = orderInfo;

      $.ajax({
        url: _self.url + "/android/Orderlist/getOrderPaymentData",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderId: id,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            if (res.data.length == 0) {
              _self.staffCollectData = {};
              _self.staffCollectData.staffName = name;
              _self.staffCollectData.trade_amount = orderInfo.receivable;
              _self.isStaffCollect = true;
            } else {
              _self.staffCollectData = res.data[0];
              _self.staffCollectData.staffName = name;
              _self.staffCollectData.trade_amount =
                Number(_self.staffCollectData.trade_amount) / 100;

              _self.isStaffCollect = true;
            }
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
          }
        },
        error: function (error) {},
      });
    },
    //收银员确认收到钱
    confirmReceipt: function () {
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Orderlist/confirmReceipt",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          orderId: _self.orderInfo.id,
          storeid: _self.loginInfo.storeid,
          cashierId: _self.orderInfo.cashier_id,
          shiftNo: _self.orderInfo.shift_no,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.tabCur = 6;
            _self.bindTabCur();
            _self.isStaffCollect = false;
            _self.$message({
              message: res.msg,
              type: "success",
              duration: 1500,
            });
          } else {
            _self.$message({
              message: res.msg,
              type: "warning",
              duration: 1500,
            });
          }
        },
        error: function (error) {},
      });
    },

    //取消服务人员代收
    cancelStaffCollect() {
      let _self = this;
      _self.isStaffCollect = false;
    },

    //服务人员代收
    saveStaffCollect: function () {
      let _self = this;
      _self.isStaffCollect = false;
    },
    handleRowClick(row) {
      this.$refs.orderTable.toggleRowExpansion(row);
    },
  },

  filters: {
    // 格式化充值金额/100
    filterMoney: function (money) {
      return (money / 100).toFixed(2);
    },
    //格式化正负号
    formatMark: function (money) {
      if (money.indexOf("-") != -1) {
        money = money.split("-")[1];
      }
      return money;
    },
  },
  computed: {},
  watch: {
    key_num: function (n, o) {
      if (!n && o) {
        this.currentPage = 1;
        this.getInit();
      }
    },
    refundPayment: {
      handler: function (n) {
        let total = 0;
        n.forEach((item) => {
          // console.log(item.repayMoney)
          total = total + Math.round(Number(item.repayMoney) * 100);
        });
        this.repayTotalMoney = total;
      },
      deep: true,
    },
  },
});
