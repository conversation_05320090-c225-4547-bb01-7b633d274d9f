<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <link rel="stylesheet" href="../vue/element/<EMAIL>" />
    <link rel="stylesheet" href="../css/print.css" />
  </head>
  <body>
    <div id="app">
      <app-print :print-arr="printArr"></app-print>
      <el-button @click="bindPrint">打印</el-button>

      <div
        class="printWrap"
        :style="{width:paperwidth +'px'}"
        ref="printorderstr"
      >
        <template v-for="(item,index) in printSet.set">
          <template v-if="item.name=='store'">
            <app-store :store-set="item.set"></app-store>
          </template>
          <template v-if="item.name=='header'">
            <app-header :header-set="item.set"></app-header>
          </template>
          <template v-if="item.name=='goods'">
            <app-goods :goods-set="item.set" :goods="printArr.list"></app-goods>
          </template>
          <template v-if="item.name=='vip'">
            <app-vip :vip-set="item.set"></app-vip>
          </template>
          <!-- <template v-if="item.name=='takegoods'">
            <app-address :address-set="item.set"></app-address>
          </template> -->
          <template v-if="item.name=='footer'">
            <app-footer :footer-set="item.set"></app-footer>
          </template>
          <template v-if="item.name=='line'">
            <app-line :line-set="item.set"></app-line>
          </template>
          <template v-if="item.name=='info'">
            <app-info :info-set="item.set"></app-info>
          </template>
          <template v-if="item.name=='text'">
            <app-text :text-set="item.set"></app-text>
          </template>
        </template>
      </div>
    </div>
  </body>
  <script src="../vue/vue2.5.16.js"></script>
  <script src="../vue/element/<EMAIL>"></script>
  <script type="text/javascript" src="../js/plugin/LodopFuncs.js"></script>
  <script src="../js/plugin/Qrcode.js"></script>
  <script src="../js/plugin/JsBarcode.js"></script>
  <script src="../js/plugin/jquery-3.2.1.min.js"></script>
  <script src="print.js"></script>
  <script>
    var baseUrl = localStorage.getItem("fdb-domainName") + "/index.php?s=";

    var printArr = {
      name: "商品名称",
      time: new Date(),
      otder_id: new Date(),
      list: [
        {
          name: "美甲",
          print: "20.00",
          num: "1",
          sku: "红色",
          allMoney: "20.00",
          offer: "会员卡8折优惠",
          offerPrint: "4.00",
        },
        {
          name: "美甲",
          print: "20.00",
          num: "1",
          sku: "红色",
          allMoney: "20.00",
          offer: "会员卡8折优惠",
          offerPrint: "4.00",
        },
      ],
    };

    new Vue({
      el: "#app",
      data: {
        printArr: printArr,
        loginInfo: {},
        paperwidth: 0, //打印纸宽度
        printSet: [], // 设置打印
        url: baseUrl,
      },

      mounted() {
        this.getLoginInfo();
        this.getReceiptSet();
      },

      methods: {
        getLoginInfo: function () {
          if (localStorage.getItem("loginInfo")) {
            this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
          }
        },

        getReceiptSet: function () {
          var _self = this;

          $.ajax({
            url: _self.url + "/android/order/getReceiptSet",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.printSet = res.data;
                _self.paperwidth = res.data.width;
              }
            },
          });
        },

        bindPrint: function () {
          if (!LODOPbol) {
            return;
          }
          var vm = this;
          if (this.printSet.length == 0) {

            Preview1();
          } else {

            // vm.printorderinfo = res.info;
            var str = $(vm.$refs.printorderstr).html();
            Preview2(str);
          }
        },
      },
    });
  </script>
</html>
