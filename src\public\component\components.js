// 订单--详情
let tName = localStorage.getItem("globalTechnicianName");
const globalTechnicianName = tName ? tName : "";
var baseUrl = localStorage.getItem("fdb-domainName") + "/index.php?s=";

// SVG图标组件 - 支持通过class控制颜色和大小
Vue.component("ficon", {
  props: {
    icon: {
      type: String,
      required: true
    }
  },
  /*html*/
  template: `
  <span class="svg-icon" v-html="svgContent"></span>
  `,
  data() {
    return {
      svgContent: ""
    };
  },
  mounted() {
    this.loadSvgIcon();
  },
  methods: {
    loadSvgIcon() {
      // 获取SVG文件路径
      const iconPath = `./images/icon/${this.icon}.svg`;

      // 使用fetch获取SVG文件内容
      fetch(iconPath)
        .then(response => {
          if (!response.ok) {
            throw new Error(`无法加载SVG图标: ${this.icon}`);
          }
          return response.text();
        })
        .then(svgText => {
          this.svgContent = svgText;
        })
        .catch(error => {
          console.error(`加载图标失败: ${error}`);
        });
    }
  },
  watch: {
    icon() {
      this.loadSvgIcon();
    }
  }
});

Vue.component("technician-name", {
  props: {},
  /*html*/
  template: `<span>{{name}}</span>`,
  data() {
    return { name: globalTechnicianName };
  },
});
Vue.component("app-caption", {
  props: {
    title: {
      type: String,
    },
  },
  /*html*/
  template: `
  <div class="uuai_title">
    <p class="caption">{{content}}</p>
  </div>
  `,
  data() {
    return {
      content: this.title,
    };
  },
});

Vue.component("app-heading", {
  props: {
    title: {
      type: String,
    },
  },
  /*html*/
  template: `
  <div class="title-label">
    <span class="text">{{content}}</span>
  </div>
  `,
  data() {
    return {
      content: this.title,
    };
  },
});

Vue.component("app-subject", {
  props: {
    title: {
      type: String,
    },
  },
  /*html*/
  template: `
  <div class="order-container">
    <div class="return-container">
      <div class="return-title">
        <p class="return-text" @click="bindReturn">
          <i class="el-icon-arrow-left"></i>
          <span>返回</span>
        </p>
        <p class="details-text">{{content}}</p>
      </div>
    </div>
  </div>`,
  data() {
    return {
      content: this.title,
    };
  },
  methods: {
    bindReturn: function () {
      this.$emit("return-title", false);
    },
  },
});

// <!--<div class="xuanze_jishi_search">-->
//     <!--<el-input placeholder="请输入服务名称" suffix-icon="el-icon-search"></el-input>-->
//     <!--</div>-->

// 添加服务  and  选择  <technician-name></technician-name>
Vue.component("add-server", {
  props: {
    serverList: {
      type: Array,
    },
    num: {
      type: Number,
    },
    memberInfo: {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div>
    <div class="tianjia_fuwu_mian">
      <div class="fuwu_biaoti">
        <ul v-if="serverListArr" v-for="(item,index) in serverListArr">
          <li
            class="tianjia_fuwu_font"
            :class="curIndex == index ? 'serverActive':'' "
            @click="bindServerTab(index,item)"
          >
            <span v-if="item.label_name">{{item.label_name}}</span>
            <span v-if="item.group_name">{{item.group_name}}</span>
          </li>
        </ul>
      </div>
      <div
        class="fuwu_biaoti_chioce"
        v-if="serverListArr.length>0 && serverListArr[curIndex].serMess"
      >
        <ul
          v-for="(item,index) in serverListArr[curIndex].serMess"
          class="fuwu_biaoti_chioce_bottom"
        >
          <li class="server_biaoti_name_font" @click="bindSelectService(item)">
            <div v-if="item.service_name" class="server_biaoti_name_font1">
              <img
                :src="item.img_arr[0]"
                :title="item.service_name"
                onerror="this.src='./images/default.jpg'"
              />
            </div>
            <div class="add-server-shop">
              <p class="server_biaoti_name_font2" v-if="item.service_name">
                <span>{{item.service_name}}</span>
              </p>
              <p
                class="server_biaoti_name_font2"
                style="box-sizing: border-box; padding: 8px 15px"
                v-if="item.nickname"
              >
                <span>{{item.nickname}}</span>
              </p>
              <div>
                <p v-if="item.price" class="server_biaoti_name_font3 itemPrint">
                  ￥{{item.price}}
                </p>
                <!--<p v-if="item.minPrice && isMember" class="server_biaoti_name_font3 minPrice">会员价：￥{{item.minPrice}}</p>-->
                <!--<el-checkbox v-model="item.isChecked" style="text-align: right"></el-checkbox>-->
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div slot="footer" class="server-footer" style="display: none">
      <el-button class="cancel_server" @click="bindCancel_server">取消</el-button>
      <el-button type="primary" @click="bindrSave_sverve">确定</el-button>
    </div>
  </div>
  `,
  data() {
    return {
      serverListArr: [],
      curIndex: 0,
      isChecked: 0,
      curTab: {},
      member: {},
      isMember: false,
    };
  },
  watch: {
    serverList: {
      handler: function (n, o) {
        this.serverListArr = n;
      },
      deep: true,
      immediate: true,
    },
    memberInfo: {
      handler: function (n, o) {
        this.member = n;
        if (JSON.stringify(n) == "{}") {
          this.isMember = false;
        } else {
          this.isMember = true;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    bindServerTab: function (index, item) {
      this.curIndex = index;
      this.curTab = item;
    },
    bindCancel_server: function () {
      this.$emit("is-server", false);
    },
    bindrSave_sverve: function () {

    },
    bindSelectService: function (data) {
      var obj = {
        is: false,
        data: data,
        curTab: this.curTab,
      };
      this.$emit("server-determine", obj);
    },
  },
});

//  服务详情
//预约详情
Vue.component("app-notice", {
  props: {
    details: {
      type: Object,
    },
    loginInfo: {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div>
    <div class="dialog-title">
      <i
        class="el-dialog__close el-icon el-icon-close"
        @click.stop="bindCloseisDetails"
      ></i>
      <p style="flex: 1; text-align: center">
        <span>预约详情</span>
        <!--<span>订单详情</span>-->
      </p>

      <!--支付状态 1.待支付 2.已支付 -->
      <p class="cancelReservation">
        <!-- status 预约状态 1.待服务2.已开单3.已取消4.已评价-->
        <span @click="bindCancelReservation" v-if="detailsTable.status==1">
          取消预约
        </span>
        <span v-else>已开单</span>
      </p>
    </div>
    <ul class="detailsTable-ul">
      <li class="detailsTable-li">
        <p class="margin-bottom shoperName" :title="detailsTable.shoper">
          {{detailsTable.shoper}}
        </p>
        <p class="margin-bottom">
          <span>{{detailsTable.phone}}</span>
          <span class="shoper-tip">预约人</span>
        </p>
      </li>
      <li class="detailsTable-li">
        <p class="margin-bottom shoperName" :title="detailsTable.place_shoper">
          {{detailsTable.place_shoper}}
        </p>
        <p class="margin-bottom">
          <span>{{detailsTable.place_phone}}</span>
          <span class="shoper-tip">下单人</span>
        </p>
        <p>会员编号:{{detailsTable.member_number}}</p>
      </li>
      <li class="detailsTable-li" style="text-align: right">
        <p
          v-if="detailsTable.arrdate && (detailsTable.status==3 || detailsTable.status==4)"
        >
          {{detailsTable.arrdate}}
        </p>
        <el-date-picker
          v-else
          style="margin-bottom: 5px"
          v-model="detailsTable.arrdate"
          type="datetime"
          size="small"
          placeholder="选择日期时间"
          format="yyyy-MM-dd hh:mm"
          value-format="yyyy-MM-dd hh:mm"
          align="right"
          class="arrdate"
          prefix-icon="el"
          :picker-options="pickerOptions"
        ></el-date-picker>
        <ul>
          <li v-if="detailsTable.status==2 && detailsTable.billtime">
            {{detailsTable.billtime}}开单
          </li>
          <li v-if="detailsTable.status==2 && detailsTable.billtime==null">
            已开单
          </li>
          <li v-if="detailsTable.status==3 && detailsTable.canceltime">
            {{detailsTable.canceltime}}取消订单
          </li>
          <li v-if="detailsTable.status==3 && detailsTable.canceltime==null">
            已取消
          </li>
          <li v-if="detailsTable.moretime!=''">
            已超时{{detailsTable.moretime}}
          </li>
        </ul>
      </li>
    </ul>

    <div class="details-server">
      <div class="details-wrap">
        <label class="details-label">服务</label>
        <div class="details-server-wrap">
          <div
            class="details-server-info"
            v-for="(item,index) in detailsTable.servermess"
          >
            <div class="details-product">
              <div>
                <p class="details-product-list">
                  <span>{{item.name}}</span>
                  <span v-if="item.price">￥{{item.price}}*1</span>
                  <span v-if="item.sertime">(约{{item.sertime}}分钟)</span>
                </p>
              </div>
            </div>
            <div class="details-technician">
              <p>
                <span>
                  <technician-name></technician-name>
                  ：
                </span>
                <span
                  v-if="(detailsTable.status==1 && detailsTable.moretime=='') || detailsTable.status==2"
                  class="changeActive"
                  @click="bindChangeTechnician(index)"
                >
                  {{item.nickname}}
                </span>
                <span v-else class="changeActive" style="color: #333">
                  {{item.nickname}}
                </span>
                <span v-if="!item.nickname || item.nickname==''">无</span>
              </p>
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
          <p class="details-order-status">
            <span class="seeDetails">{{detailsTable.paytype}}</span>
          </p>
        </div>
      </div>
      <div class="details-wrap" v-if="detailsTable.state==2">
        <label class="details-label">地址</label>
        <p class="details-server-info">{{detailsTable.address}}</p>
      </div>
      <div class="details-wrap">
        <label class="details-label">备注</label>
        <p class="details-server-info">{{detailsTable.remarks}}</p>
      </div>
    </div>

    <div class="dialog-footer">
      <div>
        <p class="reservation-price">合计 ￥{{detailsTable.payment}}</p>
        <p
          class="reservation-all"
          v-if="detailsTable.servermess !== undefined && detailsTable.servermess.length >0"
        >
          <span style="display: block; margin-bottom: 5px">
            共{{detailsTable.servermess.length}}项
          </span>
        </p>
      </div>
      <div class="reservation-group" v-if="detailsTable.status==1">
        <p class="save-btn reservationBtn" style="color: #333" @click="bindSave">
          保存修改
        </p>
        <p class="billing-btn reservationBtn" @click="serviceBilling">服务开单</p>
      </div>
    </div>
  </div>`,
  data: function () {
    return {
      url: baseUrl,
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      },
      detailsTable: {},
      storeInfo: {},
    };
  },
  watch: {
    details: {
      handler: function (n) {
        this.detailsTable = n;
      },
      deep: true,
      immediate: true,
    },
    loginInfo: {
      handler: function (n) {
        this.storeInfo = n;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 详情--修改 预约时间 <technician-name></technician-name>
    bindSave: function () {
      var _self = this;
      var obj = {
        address: _self.detailsTable.address,
        arrdate: _self.detailsTable.arrdate,
        id: _self.detailsTable.id,
        paystate: _self.detailsTable.paystate == "待支付" ? "1" : "2",
        remarks: _self.detailsTable.remarks,
        server: _self.detailsTable.servermess,
        state: _self.detailsTable.state,
        storeid: _self.storeInfo.storeid,
        merchantid: _self.storeInfo.merchantid,
      };
      var updateData = JSON.stringify(obj);
      $.ajax({
        url: _self.url + "/android/Booker/updateBooker",
        type: "POST",
        data: {
          serverdata: updateData,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message({
              type: "success",
              message: res.data,
              duration: 1500,
            });
            // _self.isDetails = false;
            _self.$emit("modify", false);
          } else {
            _self.$message({
              type: "error",
              message: res.data,
              duration: 1500,
            });
          }
        },
      });
    },

    // 服务开单
    serviceBilling: function () {
      var _self = this;

      var orderNo = _self.detailsTable["pre_order"];
      // window.location.href = "cashier_system.html?orderNo=" + orderNo;
      let href = "cashier_system.html?orderNo=" + orderNo;
      top.app.toPage(href);
    },

    // 取消预约
    bindCancelReservation: function () {
      this.$emit("send-cancel", true);
    },

    // 关闭详情
    bindCloseisDetails: function () {
      this.$emit("send-close", false);
    },

    // 获取信息
    bindChangeTechnician: function (index) {
      // console.log(this.detailsTable);
      var _self = this;
      var skuId = _self.detailsTable.servermess[0].skuid;
      var serverId = _self.detailsTable.servermess[0].serverid;
      $.ajax({
        url: _self.url + "/android/Booker/getAllstaff",
        type: "post",
        data: {
          merchantid: _self.storeInfo.merchantid, // 商户id
          storeid: _self.storeInfo.storeid,
          search: "", // 搜索值 默认空
          skuid: skuId,
          serverid: serverId,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            var obj = {
              // curReservation: item,
              curIndex: index,
              is: true,
              detailsTable: _self.detailsTable,
              technicianInfo: res.data,
            };
            _self.$emit("send-change", obj);
          }
        },
      });
    },
  },
});

// 取消预约
Vue.component("app-cancel", {
  props: {
    details: {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div class="appCancel">
    <el-select
      v-model="reasonIndex"
      style="width: 100%"
      placeholder="请选择"
      @change="bindChooseReason"
    >
      <el-option
        v-for="item in cancelReason"
        :key="item.id"
        :label="item.label"
        :value="item.id"
      ></el-option>
    </el-select>
    <div class="dialog-footer">
      <el-button class="cancelDefault" @click="bind_cancel">取 消</el-button>
      <el-button type="primary" @click="bindConfirmReservation">确 定</el-button>
    </div>
  </div>
  `,

  data() {
    return {
      cancelReason: [
        {
          label: "客户想换个时间",
          id: 0,
        },
        {
          label: "客户暂时不想要了",
          id: 1,
        },
        {
          label: "门店暂时无法提供该服务",
          id: 2,
        },
        {
          label: "没有合适的" + globalTechnicianName + "提供服务",
          id: 3,
        },
        {
          label: "其他原因",
          id: 4,
        },
      ],
      detailsTable: "",
      reasonIndex: "",
      curReason: {},
    };
  },
  watch: {
    details: {
      handler: function (n) {
        this.detailsTable = n;
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    // 选择取消原因
    bindChooseReason: function (val) {
      this.curReason = this.cancelReason.find(function (item) {
        return item.id == val;
      });
    },

    bindConfirmReservation: function () {
      var _self = this;
      const loading = this.$loading({
        lock: true,
        text: "加载中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (JSON.stringify(_self.curReason) == "{}") {
        _self.$message({
          type: "error",
          message: "选择取消原因",
          duration: 1500,
        });
        return false;
      }
      $.ajax({
        url: _self.url + "/android/Booker/cancelBooker",
        type: "POST",
        data: {
          id: _self.detailsTable.id,
          paystate: _self.detailsTable.paystate,
          reason: _self.curReason.label, // 取消原因
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.$message({
              type: "success",
              message: res.data,
              duration: 1500,
              onClose: function () {
                _self.$emit("send-confirm", false);
              },
            });
            loading.close();
          } else {
            _self.$message({
              type: "error",
              message: res.data,
              duration: 1500,
            });
            loading.close();
          }
        },
      });
    },

    // 取消--关闭弹框
    bind_cancel: function () {
      this.$emit("send-close", false);
    },
  },
});

// 通知--选择  <technician-name></technician-name>
Vue.component("choose-technician", {
  props: {
    technicianList: {
      type: Array,
    },
  },
  /*html*/
  template: `
  <div>
    <div class="xuanze_jishi_search">
      <!--<el-input placeholder="请输入服务名称" suffix-icon="el-icon-search"></el-input>-->
    </div>
    <div class="tianjia_fuwu_mian">
      <div class="fuwu_biaoti">
        <ul v-if="serverListArr" v-for="(item,index) in serverListArr">
          <li
            class="tianjia_fuwu_font"
            :class="curIndex == index ? 'serverActive':'' "
            @click="bindServerTab(index,item)"
          >
            <span v-if="item.label_name">{{item.label_name}}</span>
            <span v-if="item.group_name">{{item.group_name}}</span>
          </li>
        </ul>
      </div>
      <div
        class="fuwu_biaoti_chioce"
        v-if="serverListArr.length>0 && serverListArr[curIndex].serMess"
      >
        <ul
          v-for="(item,index) in serverListArr[curIndex].serMess"
          class="fuwu_biaoti_chioce_bottom"
        >
          <li class="server_biaoti_name_font" @click="bindSelectService(item)">
            <div v-if="item.service_name" class="server_biaoti_name_font1">
              <img
                :src="item.img_arr[0]"
                :title="item.service_name"
                onerror="this.src='./images/default.jpg'"
              />
            </div>
            <div class="add-server-shop">
              <p class="server_biaoti_name_font2" v-if="item.service_name">
                <span>{{item.service_name}}</span>
              </p>
              <p
                class="server_biaoti_name_font2"
                style="box-sizing: border-box; padding: 8px 15px"
                v-if="item.nickname"
              >
                <span>{{item.nickname}}</span>
              </p>
              <div>
                <p
                  v-if="item.price"
                  class="server_biaoti_name_font3 itemPrint"
                  :class="{cancelPrint:isMember}"
                >
                  ￥{{item.price}}
                </p>
                <p
                  v-if="item.minPrice && isMember"
                  class="server_biaoti_name_font3 minPrice"
                >
                  会员价：￥{{item.minPrice}}
                </p>
                <!--<el-checkbox v-model="item.isChecked" style="text-align: right"></el-checkbox>-->
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!--<div slot="footer" class="server-footer" style="display: none;">-->
    <!--<el-button class="cancel_server" @click="bindCancel_server">取消</el-button>-->
    <!--<el-button type="primary" @click="bindrSave_sverve">确定</el-button>-->
    <!--</div>-->
  </div>
  `,
  data() {
    return {
      serverListArr: [],
      curIndex: 0,
      curTab: "",
    };
  },
  watch: {
    technicianList: {
      handler: function (n, o) {
        this.serverListArr = n;
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    bindServerTab: function (index, item) {
      this.curIndex = index;
      this.curTab = item;
    },

    bindCancel_server: function () {
      this.$emit("is-server", false);
    },

    bindrSave_sverve: function () {

    },

    bindSelectService: function (data) {

      var obj = {
        is: false,
        data: data,
        curTab: this.curTab,
      };
      this.$emit("send-change", obj);
    },
  },
});

// 订单--详情
Vue.component("order-details", {
  props: {
    details: {
      type: Object,
    },
  },
  /*html*/
  template: `
  <div>
    <div class="order-container">
      <div class="return-container">
        <div class="return-title">
          <p class="return-text" @click="close_orderPage">
            <i class="el-icon-arrow-left"></i>
            <span>返回</span>
          </p>
          <p class="details-text">订单详情</p>
        </div>
      </div>
    </div>
    <div class="order-content">
      <!--@return-title="bindCancelDetails"-->
      <div class="contner-userInfo">
        <div class="order-info">
          <app-heading title="订单信息"></app-heading>
          <ul class="order-status">
            <li class="status-li" v-if="orderDetails.state==1">待付款</li>
            <li class="status-li" v-if="orderDetails.state==2">待收货</li>
            <li class="status-li" v-if="orderDetails.state==3">已发货</li>
            <li class="status-li" v-if="orderDetails.state==4">已完成</li>
            <li class="status-li" v-if="orderDetails.state==5">已取消</li>
          </ul>
          <!-- 到店 订单编号 下单 收款 其他属于 预约 -->
          <ol class="order-date">
            <!--<li class="order-date-li" v-if="reservation.number">-->
            <!--<span class="order-label">预约单号</span>-->
            <!--<span class="order-label2">{{reservation.number}}</span>-->
            <!--</li>-->
            <!--<li class="order-date-li" v-if="reservation.time">-->
            <!--<span class="order-label">预约时间</span>-->
            <!--<span class="order-label2">{{reservation.time}}</span>-->
            <!--</li>-->
            <!--<li class="order-date-li" v-if="reservation.people">-->
            <!--<span class="order-label">到店人</span>-->
            <!--<span class="order-label2">{{reservation.people}}</span>-->
            <!--</li>-->
            <li class="order-date-li">
              <span class="order-label">订单编号</span>
              <span class="order-label2">{{orderDetails.order_number}}</span>
            </li>
            <li class="order-date-li">
              <span class="order-label">下单时间</span>
              <span class="order-label2">{{orderDetails.order_time}}</span>
            </li>
            <li class="order-date-li">
              <span class="order-label">收银员</span>
              <span class="order-label2">{{orderDetails.cashier}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.state==4">
              <span class="order-label">收款时间</span>
              <span class="order-label2">{{orderDetails.collection_time}}</span>
            </li>
          </ol>
        </div>
        <div class="client-info" v-if="orderDetails.vip_id!=0">
          <app-heading title="客户信息"></app-heading>
          <!--<i class="iconfont icontouxiang"></i>-->
          <!-- !=0  -->
          <div class="details-vipImg" v-if="orderDetails.vip && orderDetails.vip && orderDetails.vip.pic">
            <img :src="orderDetails.vip.pic">
          </div>
          <p class="client-name" v-if="orderDetails.vip && orderDetails.vip.member_name">
            {{orderDetails.vip.member_name}}</p>
          <p class="client-phone" v-if="orderDetails.vip && orderDetails.vip.phone">
            {{orderDetails.vip.phone}}</p>
          <ul>
            <li class="order-date-li">
              <span class="order-label">会员编号</span>
              <span class="order-label2" v-if="orderDetails.vip && orderDetails.vip.member_number">{{orderDetails.vip.member_number}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.vip && orderDetails.vip.Grade">
              <span class="order-label">会员等级</span>
              <span class="order-label2">{{orderDetails.vip.Grade}}</span>
            </li>
          </ul>
        </div>
      </div>

      <div class="content-product">
        <app-heading title="消费明细"></app-heading>
        <div class="product-list">
          <ul>
            <li v-for="(item,index) in orderDetails.orderInfo" class="orderDetails-li">
              <p class="serialNumber">{{index + 1}}、</p>
              <div class="details-list">
                <div class="order-date-li">
                  <span class="order-label">{{item.name}}</span>
                  <span class="order-label2">￥{{item.price}}</span>
                </div>
                <div class="order-date-li">
                  <span class="order-label">数量</span>
                  <span class="order-label2">￥{{item.num}}</span>
                </div>
                <div class="order-date-li" v-if="item.equity_type!=1">
                  <span class="order-label" v-if="item.equity_type==3">会员抵扣</span>
                  <span class="order-label" v-if="item.equity_type==4">优惠金额</span>
                  <span class="order-label" v-if="item.equity_type==2">折扣</span>
                  <span class="order-label2">￥{{item.reduceprice}}</span>
                </div>
                <div class="order-date-li">
                  <span class="order-label">小计</span>
                  <span class="order-label2">￥{{item.Subtotal}}</span>
                </div>
                <!--预约订单显示-->
                <div class="order-date-li">
                  <span class="order-label">选择<technician-name></technician-name></span>
                  <span class="order-label2">{{item.Craftsman}}</span>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="product-userInfo">
          <!--预约 只显示 合计 付款 合计收款-->
          <ul>
            <!--<li class="order-date-li">-->
            <!--<span class="order-label">合计</span>-->
            <!--<span class="order-label2">暂无</span>-->
            <!--</li>-->
            <li class="order-date-li">
              <p class="order-label" style="width: 100%;">
                <span style="width: 50%;display: inline-block">优惠</span>
              <ul style="width: 100%;text-align: right">
                <li style="margin-bottom: 8px">
                  <span>抵扣</span>
                  <span class="order-label2">￥{{orderDetails.deduction}}</span>
                </li>
                <li style="margin-bottom: 8px">
                  <span>优惠金额</span>
                  <span class="order-label2">￥{{orderDetails.manuallys}}</span>
                </li>
                <li style="margin-bottom: 8px">
                  <span>折扣</span>
                  <span class="order-label2">￥{{orderDetails.dismoney}}</span>
                </li>
              </ul>
              </p>
              <!--dismoney 折扣权益优惠-->

            </li>
            <li class="order-date-li" v-if="orderDetails.dispatch_type==2">
              <span class="order-label">运费</span>
              <span class="order-label2">￥{{orderDetails.dispatch_fee}}</span>
            </li>
            <li class="order-date-li">
              <span class="order-label">付款</span>

              <span class="order-label2">￥{{orderDetails.receivable}}</span>

            </li>
            <!--<li class="order-date-li">-->
            <!--<span class="order-label">合计收款</span>-->
            <!--<span class="order-label2">暂无</span>-->
            <!--</li>-->
            <li class="order-date-li" v-if="orderDetails.address_info && orderDetails.address_info.name">
              <span class="order-label">收货人</span>
              <span class="order-label2">{{orderDetails.address_info.name}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.address_info && orderDetails.address_info.tel">
              <span class="order-label">收货电话</span>
              <span class="order-label2">{{orderDetails.address_info.tel}}</span>
            </li>
            <li class="order-date-li" v-if="orderDetails.address_info && orderDetails.address_info.address">
              <span class="order-label">收货地址</span>
              <span class="order-label2">{{orderDetails.address_info.address}}</span>
            </li>
          </ul>
        </div>
        <!--已取消  none -->
      </div>
      <div class="details-btn">
        <p style="box-sizing: border-box;padding: 20px 0;">合计收款：￥{{orderDetails.receivable}}</p>
        <!--1待付款 2待收货 3 已发货 4 已完成  5 已取消-->
        <div style="display: flex;">
          <div v-if="orderDetails.state!=5" class="orderBtn print" @click="bindPrint"
              style="background: #ddd;cursor: pointer">打印小票
            <!--<span>{{orderDetails.state}}</span>-->
          </div>
          <div class="orderBtn"
              style="background: #3363FF;color: #FFFFFF;cursor: pointer"
              v-if="orderDetails.state == 2&&orderDetails.dispatch_type == 2"
              @click="deliverGoods">发货
          </div>
          <div class="orderBtn"
              style="background: #3363FF;color: #FFFFFF;cursor: pointer"
              v-if="orderDetails.state == 2&&orderDetails.dispatch_type == 1"
              @click="StoreGoods">自提
          </div>
          <!--已完成block -->
          <!--<p class="orderBtn change">修改员工业绩</p>-->
          <div v-if="orderDetails.state==1" class="orderBtn"
              style="background: #999;color: #FFF;cursor: pointer"
              @click="bindCancelOrder">取消订单
          </div>
          <div v-if="orderDetails.state==1" class="orderBtn"
              style="background: #3363FF;color: #FFFFFF;cursor: pointer"
              @click="bindModifyOrder">修改订单
          </div>
          <!--<span>修改订单</span>-->
        </div>
      </div>
    </div>
    <!-- 发货弹框 -->
    <el-dialog title="发货"
              :visible.sync="isdeliverBox"
              append-to-body width="600px"
              @close="closeReceiptBox"
              class="deliver_box">
      <el-table :data="orderDetails.orderInfo">
        <el-table-column label="图片" width="150">
          <template slot-scope="scope">
            <div>
              <img :src="scope.row.imgUrl" alt="" style="width: 60px">
            </div>
          </template>
        </el-table-column>
        <el-table-column property="name" label="商品"></el-table-column>
        <el-table-column property="price" label="单价"></el-table-column>
        <el-table-column property="num" label="数量"></el-table-column>
      </el-table>
      <div class="receipt_information">
        <ul>
          <li>
            <div class="receipt_before">收货人</div>
            <div>{{addressInfo.name}}</div>
          </li>
          <li>
            <div class="receipt_before">收货电话</div>
            <div>{{addressInfo.tel}}</div>
          </li>
          <li>
            <div class="receipt_before">收货地址</div>
            <div>{{addressInfo.address}}</div>
          </li>
          <li class="nologisyic">
            <div class="receipt_before">无需物流</div>
            <el-switch
              v-model="value"
              active-color="gray"
              inactive-color="black"
              @change="changeStatus">
            </el-switch>
          </li>
        </ul>
        <div class="choose_logisyic" v-if="value">
          <div>
            <div class="receipt_before">物流单号</div>
            <el-input v-model="logisticsNum" placeholder="请输入物流单号" class="logistic_input"></el-input>
          </div>
          <div>
            <div class="receipt_before">物流公司</div>
            <el-select v-model="companyName" placeholder="请选择" class="logistic_input" @change="chooseCompany">
              <el-option
                v-for="item in logisticCompanys"
                :key="item.id"
                :value="item.id"
                :label="item.name">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isdeliverBox = false">取 消</el-button>
        <el-button type="primary" @click="sendGoods">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 自提弹框 -->
    <el-dialog title="自提"
              :visible.sync="isStoreBox"
              append-to-body width="600px"
              @close="closeReceiptBox"
              class="deliver_box">
      <el-table :data="orderDetails.orderInfo">
        <el-table-column label="图片" width="150">
          <template slot-scope="scope">
            <div>
              <img :src="scope.row.imgUrl" alt="" style="width: 60px">
            </div>
          </template>
        </el-table-column>
        <el-table-column property="name" label="商品"></el-table-column>
        <el-table-column property="price" label="单价"></el-table-column>
        <el-table-column property="num" label="数量"></el-table-column>
      </el-table>
      <div class="receipt_information">
        <ul>
          <li>
            <div class="receipt_before">取件人</div>
            <div>{{addressInfo.name}}</div>
          </li>
          <li>
            <div class="receipt_before">取件人电话</div>
            <div>{{addressInfo.phoneNumber}}</div>
          </li>
        </ul>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isStoreBox = false">取 消</el-button>
        <el-button type="primary" @click="sendGoods">确 定</el-button>
      </div>
    </el-dialog>
  </div>
  `,
  data() {
    return {
      orderDetails: {},
      //发货
      isdeliverBox: false,
      value: true,
      logisticCompanys: [], //所有物流公司
      companyName: "", //公司名称
      logisticsNum: "", //物流单号
      addressInfo: {}, //地址信息
      orderId: "",

      //自提
      isStoreBox: false,
    };
  },
  watch: {
    details: {
      handler(n, o) {
        this.orderDetails = n;

      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    // 打印
    bindPrint: function () {
      this.$emit("send-print");

    },

    // 关闭
    close_orderPage: function () {
      this.$emit("send-close-details", false);
    },
    bindModifyOrder: function () {
      var _self = this;
      var orderNo = _self.orderDetails.order_number;
      var vip_id = _self.orderDetails.vip_id;
      //window.location.href = "cashier_system.html?orderNO=" + orderNo + '&vip_id=' + vip_id;
      let href = "cashier_system.html?orderNO=" + orderNo + "&vip_id=" + vip_id;
      top.app.toPage(href);
    },
    bindCancelOrder: function () {
      var _self = this;
      this.$confirm("是否取消订单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _self.cancelOrder();
        })
        .catch(() => {});
    },
    cancelOrder: function () {
      var _self = this;
      var vip_id = _self.orderDetails.vip_id;
      $.ajax({
        url: _self.url + "/android/Orderlist/cancelOrder",
        type: "post",
        data: {
          id: _self.orderDetails.id,
          // merchantid:_self.loginInfo.merchantid,
          // storeid:_self.loginInfo.storeid,
          // vipId:vip_id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.isDetails = false;
            _self.currentPage = 1;
            _self.getInit();
          } else {
            _self.$message({
              type: "error",
              message: res.msg,
              duration: 1500,
            });
          }
        },
        error: function (res) {

        },
      });
    },
    //自提
    StoreGoods: function () {
      var _self = this;
      this.isStoreBox = true;
      _self.addressInfo = _self.orderDetails.address_info;
    },
    //获取所有物流公司
    deliverGoods: function () {
      var _self = this;
      this.isdeliverBox = true;
      _self.addressInfo = _self.orderDetails.address_info;
      $.ajax({
        url: _self.url + "/android/Orderlist/getLogistics",
        type: "post",
        success: function (res) {
          // var res = JSON.parse(res);
          _self.logisticCompanys = res.data;

        },
        error: function (e) {

        },
      });
    },
    chooseCompany: function (value) {
      var _self = this;
      _self.orderId = value;
    },
    //确认发货方法
    sendGoods: function () {
      var _self = this,
        delivery = 1;

      if (_self.value) {
        delivery = 1;
      } else {
        delivery = 2;
      }
      console.log(
        this.logisticsNum,
        this.orderId,
        this.orderDetails.dispatch_type,
        this.orderDetails.id,
        delivery
      );
      $.ajax({
        url: _self.url + "/android/Orderlist/deliverGoods",
        type: "post",
        data: {
          courier_num: _self.logisticsNum,
          delivery: delivery,
          dispatch_type: _self.orderDetails.dispatch_type,
          logistics: this.orderId,
          orderid: this.orderDetails.id,
        },
        success: function (res) {
          // var res = JSON.parse(res);

          _self.$message({
            type: "success",
            message: res.msg,
            duration: 1500,
          });
          _self.isdeliverBox = false;
          _self.isStoreBox = false;
          _self.isDetails = false;
          //window.location.href = "tongzhi.html"
          let href = "tongzhi.html";
          top.app.toPage(href);
        },
        error: function (e) {

        },
      });
    },
    //开关控制物流信息
    changeStatus: function () {
      var _self = this;
      if (_self.value == false) {
        //清空输入信息
        _self.logisticsNum = "";
        _self.companyName = "";
        _self.orderId = "";
      }
    },
    //发货弹框关闭执行方法
    closeReceiptBox: function () {
      //清空输入信息
      var _self = this;
      _self.logisticsNum = "";
      _self.companyName = "";
      _self.value = true;
      _self.orderId = "";
    },
  },
});
