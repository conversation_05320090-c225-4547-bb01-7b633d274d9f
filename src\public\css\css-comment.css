body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
textarea,
p,
blockquote,
th,
td,
input,
select,
textarea,
button {
  margin: 0;
  padding: 0;
}

/* 初始化标签在所有浏览器中的margin、padding值 */

fieldset,
img {
  border: 0 none;
}

/* 重置fieldset（表单分组）、图片的边框为0*/

dl,
ul,
ol,
menu,
li {
  list-style: none;
}

/* 重置类表前导符号为onne,menu在HTML5中有效 */

blockquote,
q {
  quotes: none;
}

/* 重置嵌套引用的引号类型 */

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

/* 重置嵌套引用*/

input,
select,
textarea,
button {
  vertical-align: middle;
}

/* 重置表单控件垂直居中*/

button {
  cursor: pointer;
  border: 0 none;
  background-color: transparent;
}

/* 重置表单button按钮效果 */

body {
  background: linear-gradient(180deg, #eaedf6 72%, #eeeaff 100%);
}

/* 重置body 页面背景为白色 */

body,
th,
td,
input,
select,
textarea,
button {
  color: #1c2024;
  font-size: 16px;
  line-height: 1;
  font-family: "Microsoft YaHei", "SimHei", "SimSun";
}

/* 重置页面文字属性 */

a {
  color: #1c2024;
  text-decoration: none;
  -webkit-tap-highlight-color: transparent;
}

/* 重置链接a标签 */

a:active,
a:hover {
  text-decoration: none;
}

/* 重置链接a标签的鼠标滑动效果 */

address,
caption,
cite,
code,
dfn,
em,
var {
  font-style: normal;
  font-weight: normal;
}

/* 重置样式标签的样式 */

caption {
  display: none;
}

/* 重置表格标题为隐藏 */

table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  table-layout: fixed;
}

/* 重置table属性 */

img {
  vertical-align: top;
}

/* 图片在当前行内的垂直位置 */

/* 页面设置 */

/* 取消a标签点击后的虚线框 */

a {
  outline: none;
}

a:active {
  star: expression(this.onFocus=this.blur());
}

/* 设置页面文字等在拖动鼠标选中情况下的背景色与文字颜色 */

/*
::selection {color: #fff;background-color: #4C6E78;}
::-moz-selection {color: #fff;background-color: #4C6E78;}
*/

/*清除浮动*/

.clear {
  clear: both;
}

.clear-float:after {
  display: block;
  clear: both;
  content: "";
}
.clear-float,
.clearfix {
  zoom: 1;
}
/*清除浮动--推荐使用*/

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
[v-cloak] {
  display: none;
}

/* 取消半透明灰色 */
html,
body {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
/* 修改chrome浏览器渲染黄色背景的时间 */
input:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s;
  color: #000 !important;
}

/*.el-loading-spinner .el-loading-text {*/
/*color: #F97E81;*/
/*}*/
/*.el-loading-spinner .path {*/
/*stroke: #F97E81;*/
/*}*/

/*.el-loading-spinner i {*/
/*color: #F97E81;*/
/*}*/
/*.el-loading-spinner .el-loading-text {*/
/*color: #F97E81;*/
/*margin: 3px 0;*/
/*font-size: 14px;*/
/*}*/

.drag {
  -webkit-app-region: drag;
}

.no-drag {
  -webkit-app-region: no-drag;
}
.my-scrollbar-y .el-scrollbar__wrap {
  overflow-x: hidden !important;
}
[v-cloak] {
  display: none;
}

.el-input--small {
  font-size: 14px !important;
}

.o-numberInput-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  width: 100%;
  height: 30px;
  overflow: hidden;
  color: #1c2024;
}

.o-numberInput-box:hover {
  border: 1px solid #3363ff;
}

.o-numberInput-box > i {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background: #f5f7fa;
  width: 30px;
  height: 100%;
  font-size: 13px;
}

.o-numberInput-box > .el-icon-minus {
  border-right: 1px solid #dcdfe6;
}
.o-numberInput-box > .el-icon-plus {
  border-left: 1px solid #dcdfe6;
}

.o-numberInput-box .el-input__inner {
  border: none;
}

.o-scrollbar::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.o-scrollbar::-webkit-scrollbar-thumb {
  transition: all 0.3s ease;
  border-radius: 5px;
  background: transparent;
  width: 8px;
}

.o-scrollbar:hover::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgb(210 215 229);
}

.o-scrollbar::-webkit-scrollbar-track {
  transition: all 0.3s ease;
  border-radius: 0;
  background: transparent;
}
.o-scrollbar:hover::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px #eee;
  background: #fff;
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
}

.o-dot {
  flex-shrink: 0;
  border-radius: 50%;
  background: #3363ff;
  width: 6px;
  height: 6px;
}

.o-1vh {
  height: calc(100% - 20px);
}

.o-bg-primary-linear-short {
  background: linear-gradient(92deg, #3363ff 59.62%, #5f60e9 100%);
}

.o-bg-primary-linear-long {
  background: linear-gradient(92deg, #3363ff 23.41%, #775fdd 100%);
}

.o-bg-primary-light-animation {
  animation: primaryLightAnimation 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)
    forwards;
  transition: none; /* 覆盖transition-all的影响 */
  will-change: background-size;
  background: linear-gradient(
    35deg,
    #ebeffc 36.68%,
    #f9faff 72.74%,
    #f9faff 94.1%
  );
  background-position: right top;
  background-size: 5000%;
}

.o-font-shadow {
  text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
}

.o-title-box-shadow {
  box-shadow: 0px 14px 31px -20px rgba(11, 49, 166, 0.15);
}

@keyframes primaryLightAnimation {
  0% {
    background-size: 5000%;
  }
  100% {
    background-size: 100%;
  }
}

.o-little-server-card-grid-box {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
}

.o-card-bg-primary-light {
  background: linear-gradient(84deg, #f8f8f8 0%, #fff 49.81%, #eceffb 99.63%);
}

.o-price-tag {
}

.o-tag-fuchsia {
  border: 1px solid #f5d0fe;
  background: #faf5ff;
  color: #d946ef;
}

.o-tag-pink {
  border: 1px solid #fbcfe8;
  background: #fdf2f8;
  color: #ec4899;
}

.o-tag-blue {
  border: 1px solid #bfdbfe;
  background: #eff6ff;
  color: #3b82f6;
}

.o-tag-indigo {
  border: 1px solid #c7d2fe;
  background: #eef2ff;
  color: #6366f1;
}

.o-tag-cyan {
  border: 1px solid #a5f3fc;
  background: #ecfeff;
  color: #06b6d4;
}

.o-tag-amber {
  border: 1px solid #fde68a;
  background: #fffbeb;
  color: #d97706;
}

.o-tag-lime {
  border: 1px solid #a3e635;
  background: #f7fee7;
  color: #4d7c0f;
}

.o-tag-gray {
  border: 1px solid #d1d5db;
  background: #f9fafb;
  color: #6b7280;
}

.o-tag-orange {
  border: 1px solid #fed7aa;
  background: #fff7ed;
  color: #f97316;
}

.o-tag-emerald {
  border: 1px solid #6ee7b7;
  background: #ecfdf5;
  color: #10b981;
}

.o-tag-rose {
  border: 1px solid #ffa1ad;
  background: #fff1f2;
  color: #ff2056;
}
