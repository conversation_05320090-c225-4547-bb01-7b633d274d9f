.printWrap, #prints {
    position: absolute;
    top: 0;
    left: 15px;
    box-sizing: border-box;
    box-shadow: 0 0 1px 2px #F6F6F6;
    max-height: 500px;
    overflow-y: auto;
    background: #fff;
    z-index: 3000;
    display: none ;
}

.storeTitle {
    text-align: center;
}

.header-wrap, .vipWrap {
    box-sizing: border-box;
    padding-bottom: 8px;
    margin-bottom: 8px;
    border-bottom: 2px dotted #2B282C;
}

.headerTitle {
    text-align: center;
    margin: 8px 0;
}

.header-item, .vip-item, .area-item, .footer-item {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    box-sizing: border-box;
    margin-bottom: 6px;
}

.header-item > span:first-child {
    display: inline-block;
    min-width: 55px;
}

.goods-main {
    width: 100%;
    zoom: 1;
}

.goods-main:after {
    display: block;
    content: '';
    clear: both;
}

.goodsIndex {
    float: left;
    width: 10%;
}

.goodsWrap {
    width: 90%;
    float: right;
}

.goodsItme {
    margin-bottom: 5px;
    zoom: 1;
}

.goodsItme:after {
    display: block;
    content: '';
    clear: both;
}

.goodsLeft {
    float: left;
}

.goodsRight {
    float: right;
}

.print-line {
    text-align: center;
    margin: 5px 0;
    margin-bottom: 15px;
}

.print-line2 {
    position: relative;
    text-align: center;
}

.print-line2 > span {
    position: relative;
    background: #fff;
    box-sizing: border-box;
    padding: 0 15px;
    z-index: 2;
}

.print-line2 > .line-item {
    position: absolute;
    top: 5px;
    left: 0;
    display: block;
    width: 100%;
    height: 3px;
    background: #8c939d;
}

.lineItme {
    width: 35%;
}

.lineItme > span {
    display: block;
    height: 2px;
    background: #2B282C;
}

.vipWrap {
    padding-top: 8px;
    border-top: 2px dotted #2B282C;
    border-bottom: 2px dotted #2B282C;
}

.infoWrap {
    margin: 15px 0;
}

.info-text {
    text-align: center;
    margin-bottom: 10px;
}

.pay-success {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.successIcon {
    color: #3363FF;
    font-size: 100px;
    margin-bottom: 30px
}

.pay-success > h2 {
    margin-bottom: 45px;
}

.success-btn {
    width: 180px;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #fff;
    border: 1px solid #dcdfe6;
    color: #606266;
    -webkit-appearance: none;
    text-align: center;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    outline: 0;
    margin: 0;
    -webkit-transition: .1s;
    transition: .1s;
    font-weight: 500;
    padding: 15px 20px;
    font-size: 14px;
    border-radius: 4px;
    margin-bottom: 10px;
}

.printBtn {
    color: #3363FF;
}

.newBilling {
    background: #3363FF;
    color: #fff;
    border: 1px solid #3363FF;
}

/*1px线条问题*/
.article {
    height: 1px;
    background: #dbdbdb;
    transform: scaleY(0.5);
}

/* 发货部分 */
.hy_model1_title_cz {
    height: 60px;
    background: #EEEEEE;
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    padding: 0px 30px !important;
    line-height: 60px;
}
.hy_model1_cz_top1 {
    font-size: 16px;
    font-weight: 400;
    color: #000000;
}
.deliver_box div:first-child{
    border-radius: 10px;
}
.receipt_information li{
    display: flex;
    height: 30px;
    line-height: 30px;
}
.nologisyic{
    justify-content: space-between;
    align-items: center;
}
.choose_logisyic>div{
    display: flex;
    height: 30px;
    line-height: 30px;
}
.logistic_input{
    width: 200px;
    height: 30px;
}
.logistic_input input{
    height: 30px;
}
.receipt_before{
    width: 100px;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 2px;
}
/* 覆盖取消按钮默认样式 */
.el-button--default:hover{
    color: #3363FF;
    border-color: #dcdfe6 !important;
    background-color: #fff !important;
}