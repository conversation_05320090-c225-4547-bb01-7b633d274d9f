var baseUrl = localStorage.getItem("fdb-domainName") + "/index.php?s=";

var app = new Vue({
  el: "#app",
  data: function () {
    return {
      shezhi_title2: "预约提示音",
      listIndex: 0,
      page: 1,
      cashier: "",
      limit: 10,
      show: false,
      datas: [],
      noticeDetails: {},
      isScroll: true,
      allCount: 0,
      tz_yuyue_message1: {},
      tz_yuyue_message_title: "",
      tz_yuyue_message_neirong: [],
      tz_now_time: "",

      //
      loading: false,
      url: baseUrl,
      loginInfo: {},

      //详情数据
      isNoticeDetails: false,
      noticeDetailsData: {},
      isCancelReservation: false, //取消预约
      isTechnician: false, //选择服务人员
      curReservation: {}, // 当前预约信息
      curIndex: 0,
      technicianInfo: [], // 服务人员

      // 订单
      isDetails: false,
      orderDetails: {},
      addressInfo: {},

      paperwidth: 0, //打印纸宽度
      printSet: [], // 设置打印
    };
  },
  created: function () {
    var _self = this;
    _self.sz_ys_money = _self.sz_hj_money;
    _self.getLoginInfo();
    _self.getNewsList();
  },

  mounted: function () {

    this.getReceiptSet();
  },

  methods: {
    isEmpty: function (obj) {
      for (var key in obj) {
        return false;
      }
      return true;
    },

    //加载更多
    loadMore: function () {
      var _self = this;
      _self.isScroll = true;
      if (_self.allCount == _self.datas.length) {

        return;
      } else {
        _self.page++;
        _self.getNewsList(1);
      }
    },

    //得到登陆信息
    getLoginInfo: function () {
      if (localStorage.getItem("loginInfo")) {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
      } else {
        this.$message({
          type: "error",
          message: "请登录",
          duration: 1500,
        });
      }
    },

    add_tz_yuyue_message1: function (data, index) {

      this.noticeDetails = data;
      this.listIndex = index;
      // 1 订单  2 预约
      var state = this.noticeDetails.state;
      var id = this.noticeDetails.pre_id;

      if (!state || state == null) {
        this.$message({
          type: "error",
          message: "订单类型异常",
          duration: 1500,
        });
        return;
      }
      switch (state) {
        case "2":

          this.getOrderDetails(id);
          break;
        case "1":

          this.reservationDetails(id);
          break;
      }
    },

    //获取通知列表
    getNewsList(flag) {
      var _self = this;
      _self.loading = true;
      $.ajax({
        url: _self.url + "/android/newsnotice/NewsList",
        type: "post",
        data: {
          storeid: _self.loginInfo.storeid,
          limit: _self.limit,
          page: _self.page,
          merchantid: _self.loginInfo.merchantid,
        },
        success: function (res) {
          // var res = JSON.parse(res);

          if (res.code == 0 && res.count > 0) {
            _self.allCount = res.count;
            if (flag == 1) {
              //多次加载数据
              _self.loading = false;
              _self.datas = _self.datas.concat(res.data);
              if (
                res.count == 0 &&
                _self.datas.length == res.data.length &&
                res.data.length == 0
              ) {
                _self.isScroll = true;
                return false;
              } else {
                _self.isScroll = false;
              }
              _self.loading = false;
            } else {
              //第一次加载
              //console.log('加载第一次')
              _self.datas = res.data;
              _self.isScroll = false;
              _self.add_tz_yuyue_message1(_self.datas[0]);
            }
          } else {
            _self.datas = [];
          }
        },
      });
      _self.loading = false;
    },

    /*
     *
     *  详情
     *
     * */

    // 查看详情
    seeNoticeDetails: function () {
      // 1 订单  2 预约
      var state = this.noticeDetails.state;
      var id = this.noticeDetails.pre_id;

      if (!state || state == null) {
        this.$message({
          type: "error",
          message: "订单类型异常",
          duration: 1500,
        });
        return;
      }
      switch (state) {
        case "2":

          this.getOrderDetails(id);
          this.isDetails = true;
          break;
        case "1":

          this.reservationDetails(id);
          this.isNoticeDetails = true;
          break;
      }
    },

    //预约详情
    reservationDetails: function (id) {
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Booker/getoneBooker",
        type: "POST",
        data: {
          id: id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.noticeDetailsData = res.data;
          }
        },
      });
    },

    // 修改预约---保存按钮
    modifyOrder: function (obj) {
      this.isNoticeDetails = obj;
    },

    // 预约详情--取消预约
    sendCancel: function (obj) {
      this.isCancelReservation = obj;
    },

    // 预约详情--关闭
    bindSendClose: function (obj) {
      this.isNoticeDetails = obj;
    },

    // 取消原因--取消按钮
    sendClose: function (obj) {
      this.isCancelReservation = obj;
    },

    // 取消原因--确认按钮
    sendConfirm: function (obj) {
      this.isCancelReservation = obj;
      this.isNoticeDetails = obj;
    },

    // 获取服务人员--信息
    bindchangeTechnician: function (obj) {


      this.isTechnician = obj.is;
      this.curIndex = obj.curIndex;
      this.curReservation = obj.detailsTable;
      if (this.isTechnician) {
        this.technicianInfo = obj.technicianInfo;
      }
    },

    // 点击服务人员 -- 修改
    bindModifyTechnician: function (obj) {
      var index = this.curIndex;
      this.isTechnician = obj.is;
      var nickname = obj.data.nickname;
      var id = obj.data.id;
      Vue.set(this.noticeDetailsData.servermess[index], "nickname", nickname);
      Vue.set(this.noticeDetailsData.servermess[index], "staffid", id);
    },

    /*
     *
     *   订单
     *
     * */

    getOrderDetails: function (id) {
      var _self = this;
      $.ajax({
        url: _self.url + "/android/Orderlist/OrderDetails",
        type: "POST",
        data: {
          id: id,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.orderDetails = res.data;
            _self.addressInfo = res.data.address_info;
          }
        },
      });
    },

    // 订单关闭
    closeDetails: function (obj) {
      this.isDetails = obj;
    },

    // 获取小票样式
    getReceiptSet: function () {
      var _self = this;

      $.ajax({
        url: _self.url + "/android/order/getReceiptSet",
        type: "post",
        data: {
          merchantid: _self.loginInfo.merchantid,
          storeid: _self.loginInfo.storeid,
        },
        success: function (res) {
          // var res = JSON.parse(res);
          if (res.code == 1) {
            _self.printSet = res.data;
            _self.paperwidth = res.data.width;
          }
        },
      });
    },

    // 订单打印
    bindPrint: function () {
      if (!LODOPbol) {
        this.noPrint();
        return;
      }
      var vm = this;
      if (this.printSet.length == 0) {

        Preview1();
      } else {

        // vm.printorderinfo = res.info;
        var str = $(vm.$refs.printorderstr).html();
        Preview2(str);
      }
    },

    // 没有安装打印机
    noPrint: function () {
      let self = this;
      self.$message({
        type: "error",
        message: "打印机未准备好,无法打印",
        duration: 1500,
        onClose: function () {
          LODOPbol = false;
        },
      });
    },
  },
});
