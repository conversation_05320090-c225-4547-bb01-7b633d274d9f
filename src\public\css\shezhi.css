div{
    -moz-user-select: none; /*mozilar*/
    -webkit-user-select: none; /*webkit*/
    -ms-user-select: none; /*IE*/
    user-select: none;
}
.menu {
    height: 60px;
    background-color: #2B282C;
    display: flex;
}

.menu_font {
    width: 80%;
    display: flex;
    margin-left: 100px;

}

.menu_font .menu_font_nav{
    width: 20%;
    min-width: 120px;
    height: 100%;
    color: #FFFFFF;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}
.addclass {
    background-color: #3363FF;
}

.cashier {
    background: #2B282C;
    opacity: 0.95;
    height: 60px;
    padding: 0% 10% 0% 10%;
    display: flex;
    justify-content: space-around;

}
.cashier .li1 {
    display: flex;
    justify-content: center;
    align-items: center;
}
.li1_img{
    width: 40px;
    height: 40px;
}

.cashier .li2 {
    color: white;
    font-size: 16px;
    text-align: center;
    line-height: 60px;
}

/*以上是导航条样式*/
.main {
    display: flex;
    height: calc(100vh - 60px);
}
.left {
    width: 100px;
    height: 100%;
}

.left_menu {
    width: 100px;
    height: 96%;
    margin-top: 2vh;
    background: rgba(43, 40, 44, 1);
    border-radius: 0px 20px 20px 0px;
    padding-top: 8vh;
    box-sizing: border-box;

}

.left_menu .left_menu_nav{
    color: white;
    font-size: 16px;
    height: 7vh;
    text-align: center;
    line-height: 7vh;
    cursor: pointer;
}

/* 主体--右侧 */
.main-right {
    width: 100%;
    display: flex;
}
.server {
    min-width: 500px;
    height: calc(100vh - 60px);
    border-right: 6px solid #ebeef7;
}
.open_details_title {
    background: #E5E5E5;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    line-height: 60px;
    border-bottom: 2px solid #E9E9E9;
}
.open_details {
    flex: 1;
    height: calc(100vh - 60px);
}
.open_details_border {
    height: 60px;
    border-bottom: 2px solid rgba(238, 238, 238, 1);
}
.addclass {
    background-color: #3363FF;
}
.sz_title1_info1{
    padding: 30px 40px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #E5E5E5;
    cursor: pointer;
}
.sz_title1_info1_font1{
    font-size: 14px;
    font-weight: 400;
}
.sz_title1_info1_font2{
    width: 50px;
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    color: #999999;
}
.sz_title1_info2{
    font-size: 14px;
    font-weight: 400;
    padding: 30px 40px;
    color: #333333;
    border-bottom: 1px solid #E5E5E5;
    cursor: pointer;
}
.sz_title1_info3{
    padding: 30px 40px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #E5E5E5;
    cursor: pointer;
}
.sz_title1_info3_font1{
    font-size: 14px;
    font-weight: 400;
}
.sz_title1_info3_font2{
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    color: #999999;
}
.title1_add_class{
    background-color: #F7EEFA!important;
    color: #3363FF!important;
}
.sz_title2_info1{
    display: flex;
    justify-content: space-between;
    padding: 20px 30px;
}
.sz_title2_info1_font1{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
}
.sz_title2_info2{
    padding: 20px 30px;
    background: #F6F6F6;
    color: #999999;
    font-size: 14px;
    font-weight: 400;
}
.sz_title2_info3{
    display: flex;
    justify-content: space-between;
    padding: 20px 30px;
}

.sz_title2_info3_font1{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
}
.sz_title2_info4{
    height: calc(100vh - 316px);
    padding: 20px 0px 0px 30px;
    background: #F6F6F6;
    color: #999999;
    font-size: 14px;
    font-weight: 400;
}
.sz_title2_info5{
    display: flex;
    justify-content: space-between;
    padding: 0px 0px 0px 30px;
}
.sz_title2_info5_font1{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 74px;
}
.sz_title2_info5_font2{
    display: flex;
    justify-content: space-between;
}
.sz_title2_info5_font2_1{
    padding: 30px 20px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    background: #EEEEEE;
}
.sz_title2_info5_font2_2{
    padding: 30px 30px;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    background: #DDDDDD;
}
.dayin1_duan_add{
    background:#3363FF!important;
}
.dayin2_duan_add{
    background:#3363FF!important;
}
.sz_title2_info6{
    height: calc(100vh - 290px);
    padding: 20px 0px 0px 30px;
    background: #F6F6F6;
    color: #999999;
    font-size: 14px;
    font-weight: 400;
}
.sz_title2_info7{
    display: flex;
    justify-content: space-between;
    padding: 20px 30px;
}
.sz_title2_info7_font1{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
}
.sz_title2_info7_font2{

}
.sz_title2_info8{
    padding: 20px 30px;
    background: #F6F6F6;
    color: #999999;
    font-size: 14px;
    font-weight: 400;
}
.sz_title2_info9{
    padding: 20px 30px;
    background: #F6F6F6;
    color: #999999;
    font-size: 14px;
    font-weight: 400;
}
.sz_title2_info10{
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
}
.sz_title2_info10_font1{
    font-size: 14px;
    font-weight: 400;
    color: #333333;

}
.sz_title2_info10_font2{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
}
