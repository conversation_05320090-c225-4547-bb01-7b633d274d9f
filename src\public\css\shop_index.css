[v-cloak] {
  display: none !important;
}

.shop-comtent {
  background: #f6f6f6;
  width: 100%;
  height: 100vh;
}

.height {
  height: 100%;
}

.shop_left {
  box-sizing: border-box;
  border-right: 3px solid #f6f6f6;
  background: #fff;
  width: 100%;
  height: 100%;
}

.shop-tab {
  box-sizing: border-box;
  border-bottom: 2px solid #eee;
  background: #fff;
  width: 100%;
  height: 60px;
  line-height: 56px;
  text-align: center;
}

.shop-body {
  box-sizing: border-box;
  padding: 0 15px;
  height: calc(100vh - 200px);
  overflow-y: auto;
  /*60+62+106+30+2*/
}

.shop-body::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.shop-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.shop-body::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.shop_public {
  box-sizing: border-box;
  padding: 15px;
}

.shop-body-search {
  margin-bottom: 15px;
}
.loadingtip {
  margin: 10px 0 5px;
  height: 20px;
  color: rgb(77, 76, 76);
  font-size: 14px;
  line-height: 20px;
  text-align: center;
}

.shop-body-tab {
  display: flex;
  box-sizing: border-box;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background: rgba(238, 238, 238, 1);
  padding: 15px 0;
}

.shop-body-tab > span {
  flex: 1;
  margin: 0 auto;
}

.shop-body-tab > span > .body-tab-li {
  width: 100%;
}

.body-tab-li {
  cursor: pointer;
  font-size: 16px;
  text-align: center;
}

.el-icon-caret-bottom {
  color: #666;
}

.server-shop-box {
  height: 100%;
  /* height: calc(100vh - 280px); */
  /*overflow: hidden;*/
  /*background: #f6f6f6;*/
}

.server-shop {
  display: flex;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-bottom: 1px solid #eee;
  padding: 10px;
}

.validity_time {
  font-size: 12px;
}

.aging {
  font-size: 14px;
}

.giveAway-faceValue {
  display: flex;
  justify-content: space-between;
}

.giveAway {
  display: inline-block;
  box-sizing: border-box;
  border: 1px solid #fff;
  -webkit-border-radius: 25px;
  -moz-border-radius: 25px;
  border-radius: 25px;
  padding: 5px 10px;
  font-size: 12px;
}

.giveAwayActive {
  border-color: #fff;
}

.shopActive {
  transition: all 0.5s ease-out;
  border: 1px solid #3363ff;
}

.shopImg {
  width: 80px;
  height: 80px;
}

.shopImg > img {
  display: inline-block;
  width: 80px;
  height: 80px;
}

.shop-list {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 10px;
  width: calc(100% - 80px);
}

.shop_name {
  overflow: hidden;
  font-weight: bold;
  font-size: 16px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.server-price,
.server-statu {
  float: right;
}

.fzc9 {
  color: #999;
  font-size: 14px;
}

.shop_right {
  position: relative;
}

.info-details {
  box-sizing: border-box;
  background: #fff;
  padding: 15px;
}

.shop-info {
  height: calc(100vh - 109px);
  overflow: auto;
}

.shop-info::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.shop-info::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.shop-info::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.shop-info-body {
  display: flex;
  margin-bottom: 15px;
}

.detail_ul {
  padding-right: 15px;
  width: calc(100% - 280px);
}

.detail_li {
  border-bottom: 1px solid #f6f6f6;
  padding: 12px;
  font-size: 16px;
}

.label-li {
  color: #999;
  font-size: 16px;
}

.label-name {
  float: right;
}

.shop-bigImg {
  position: relative;
  width: 280px;
  /*height: 200px;*/
  height: 100%;
}

.shop-bigImg img {
  display: block;
  width: 100%;
  height: 100%;
}

.shop-bigImg > .el-carousel__container {
  height: 100%;
}

.bigIndex {
  position: absolute;
  bottom: 0px;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  width: 100%;
  height: 25px;
  color: #fff;
  font-size: 12px;
  line-height: 25px;
  text-align: center;
}

.desc {
  box-sizing: border-box;
  padding: 10px 25px;
  line-height: 20px;
  font-family: "Microsoft YaHei", "SimHei", "SimSun";
}

.shopTable {
  box-sizing: border-box;
  margin: 10px 0;
}

.table-title {
  text-align: left;
}

.table-title > th {
  background: #eeeeee;
  font-size: 16px;
}

.table-title > th,
.table-body > td {
  box-sizing: border-box;
  padding: 12px 8px;
}

.table-body > td {
  border-bottom: 1px solid #e5e5e5;
}

.fixed-btn {
  border-top: 1px solid #fff;
  text-align: right;
}

.BindVip {
  display: inline-block;
  height: 48px;
  line-height: 48px;
}

.btn-button {
  cursor: pointer;
  padding: 16px 40px;
}

.obtained {
  border-color: #7a7a7a;
  background: #7a7a7a;
  color: #fff;
}

.obtained:focus,
.obtained:hover {
  border-color: #7a7a7a;
  background-color: #7a7a7a;
  color: #fff;
}

.promotion {
  border-color: #2b282c;
  background: #2b282c;
  color: #fff;
}

.promotion:focus,
.promotion:hover {
  border-color: #2b282c;
  background-color: #2b282c;
  color: #fff;
}

.el-button-group > .el-button:not(:last-child) {
  margin-right: 0px;
}

.editBtn {
  background: #3363ff;
  color: #fff;
}

/*弹出层*/
.edit_body {
  margin: 0 auto;
  width: 90%;
}

.cancel:focus,
.cancel:hover {
  border-color: #dcdfe6;
  background-color: #fff;
  color: #3363ff;
}

.edit-li {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.edit-li-label {
  display: inline-block;
  width: 70px;
  color: #333;
  font-size: 16px;
}

.edit-box {
  flex: 1;
}

.el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

.tip {
  padding-left: 69px;
  height: 40px;
  line-height: 40px;
}

.popover-wrap {
  position: relative;
  font-size: 16px;
}

.popover-title {
  margin-bottom: 15px;
  text-align: center;
}

.cancel-popover {
  position: absolute;
  top: 0;
  right: 15px;
  cursor: pointer;
  color: #3363ff;
}

.popover-li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  border-bottom: 1px solid #f6f6f6;
  padding: 10px 0;
}

.popover-li > .el-icon-circle-check {
  color: #3363ff;
  font-size: 20px;
}

.popover-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
}

/*order*/

.order-container {
  /* width: calc(100% - 100px); */
}

.table-wrap {
  box-sizing: border-box;
  padding: 15px;
  width: 100%;
}

.order-search {
  margin-bottom: 15px;
}

.orderTable {
  margin-bottom: 15px;
  width: 100%;
}

.orderTable .el-table__body-wrapper {
  /*120 48 55 32 15 30*/
  height: calc(100vh - 185px);
  overflow-y: auto;
}

.orderTable .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.orderTable .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.orderTable
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.refundCardData .el-table__body-wrapper {
  /*120 48 55 32 15 30*/
  height: calc(100vh - 510px);
  overflow-y: auto;
}

.refundCardData .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.refundCardData .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.refundCardData
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.refundheader {
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  width: 100%;
  max-height: 140px;
  overflow: hidden;
  overflow-y: auto;
}

.refundheader::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.refundheader::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.refundheader::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.refundGoods .el-table__body-wrapper {
  max-height: 160px;
  overflow: hidden;
  overflow-y: auto;
}

.refundGoods .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.refundGoods .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.refundGoods
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.refundForm {
  max-height: 420px;
  overflow-y: auto;
}

.refundForm::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.refundForm::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.refundForm::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.table_date {
  color: #999999;
  font-size: 14px;
}

.seeDetails {
  color: #3363ff;
  font-size: 14px;
}

/*预约*/
.navigation-tab {
  cursor: pointer;
  box-sizing: border-box;
  margin-top: 5px;
  -webkit-border-radius: 0 8px 8px 0;
  -moz-border-radius: 0 8px 8px 0;
  border-radius: 0 8px 8px 0;
  background: #2b282c;
  padding: 40px 0;
  width: 110px;
  height: calc(100vh - 10px);
  color: #fff;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

.tab-navigation,
.tab-navigation2 {
  box-sizing: border-box;
  padding: 15px 0;
  text-align: center;
}

.tab-navigation2 {
  font-size: 15px;
}

.tab-liActive {
  transition: all 0.3s ease-out;
  background: #3363ff;
}

.tabText {
  color: #3363ff;
}

.reservation-wrap {
  display: flex;
}

.reservation-container {
  background: #f6f6f6;
  width: calc(100vw - 110px);
  overflow: hidden;
}

.add_reservation_card {
  margin: 10px auto;
  width: 95%;
}

.padding {
  box-sizing: border-box;
  padding: 0 20px;
}

.form-wrap,
.form-wrap2 {
  box-sizing: border-box;
  background: #fff;
  padding: 0 30px;
}

.form-wrap2 {
  margin-top: 5px;
  margin-bottom: 5px;
}

.form_block,
.form_block2 {
  display: flex;
  align-items: center;
  width: 100%;
}

.form-label,
.form-label2 {
  width: 80px;
  font-size: 14px;
  /* text-align: right; */
}

.form-label2 {
  align-self: flex-start;
  padding-top: 15px;
}

.add-server {
  display: flex;
  align-items: center;
}

.axb > .add-server:last-child {
  margin-bottom: 20px;
}

.add-server > .el-icon-remove {
  display: inline-block;
  cursor: pointer;
  margin-right: 15px;
  color: #3363ff;
  font-size: 24px;
}

.server-list {
  flex: 1;
  width: 100%;
}

.form-inner,
.form-inner2 {
  flex: 1;
  box-sizing: border-box;
  /* padding: 15px 12px; */
  padding: 5px 5px;
}

.server-list > .form-inner2 {
  display: flex;
  flex: 1;
  justify-content: space-between;
}

.form-inner2 {
  padding: 15px 0;
}

.form-input {
  outline: none;
  border: none;
  width: 350px;
  height: 100%;
}

.border-bottom {
  border-bottom: 1px solid #e5e5e5;
}

.server-text {
  cursor: pointer;
  color: #3363ff;
  font-size: 15px;
}

.addTo {
  cursor: pointer;
  box-sizing: border-box;
  color: #3363ff;
  font-size: 15px;
}

.reservation-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  background: #fff;
  padding: 8px 0;
  text-align: center;
}

.arrdate {
  width: 150px !important;
}

.arrdate > .el-input__inner {
  cursor: pointer;
  padding: 0 5px;
  color: #3363ff;
}

.changeActive {
  cursor: pointer;
  color: #3363ff;
}
/* 去除商品button按钮默认样式 */
.el-button--default:hover {
  border-color: #dcdfe6 !important;
  background-color: #fff !important;
  color: #3363ff;
}

/* 服务- 规格 */
.srever_sku-title {
  position: relative;
  width: 100%;
  text-align: center;
}

.el-icon-back {
  position: absolute;
  top: 0;
  left: 20px;
  cursor: pointer;
}

.sku-list {
  height: 300px;
  overflow-y: auto;
}

.sku-list::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.sku-list::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.sku-list::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.sku-list > .sku-item {
  box-sizing: border-box;
  border-bottom: 1px solid #f6f6f6;
  padding: 15px 0;
}

.itemSku,
.itemPrice {
  margin-right: 15px;
}

.cancelPrint {
  text-decoration: line-through;
}

/*查看预约*/
.reservation-table {
  margin-bottom: 15px;
  width: 100%;
}

.reservation-table .el-table__body-wrapper {
  /* 48 55 60 15 32  20*/
  height: calc(100vh - 230px);
  overflow-y: auto;
}

.reservation-table .el-table__body-wrapper::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.reservation-table .el-table__body-wrapper::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.reservation-table
  .el-table__body-wrapper::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.reservation-table .has-gutter {
  /*display: none;*/
}

.details-server {
  height: 400px;
  overflow-y: auto;
}

.see-reservation {
  background: #fff;
  /*height: calc(100vh - 60px);*/
  /*overflow-y: auto;*/
}

.detailsTable-ul {
  display: flex;
  box-sizing: border-box;
  border-bottom: 1px solid #f6f6f6;
  padding: 10px 0;
}

.detailsTable-li {
  flex: 1;
  box-sizing: border-box;
  padding: 0 15px;
}

.shoperName {
  width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shoper-tip {
  display: inline-block;
  box-sizing: border-box;
  border-radius: 4px;
  background: #f6f6f6;
  padding: 4px 6px;
  font-size: 12px;
}

.margin-bottom {
  margin-bottom: 5px;
}

.reservation-search {
  box-sizing: border-box;
  padding: 0 20px 20px 20px;
}

.details-wrap {
  display: flex;
  position: relative;
  box-sizing: border-box;
  border-bottom: 1px solid #f6f6f6;
  padding: 15px 0;
}

.details-order-status {
  position: absolute;
  top: 15px;
  right: 0;
}

.details-server-wrap {
  flex: 1;
}

.details-label {
  width: 60px;
}

.details-server-info {
  margin-bottom: 15px;
  width: 100%;
}

.details-product {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.details-product-list {
  margin-bottom: 10px;
}

.details-product-list:last-child {
  margin-bottom: 0;
}

.details-technician {
  display: flex;
  justify-content: space-between;
}

/*.el-table thead {*/
/*display: none;*/
/*}*/

/*.details-mask.el-dialog {*/
/*left: 60%;*/
/*height: 100%;*/
/*margin: 0;*/
/*margin-top: 0;*/
/*position: relative;*/
/*}*/

.dialog-title {
  display: flex;
  justify-content: space-between;
}

.notice-mask > .el-dialog__header {
  padding: 0;
}

.dialog-title > .el-dialog__close,
.cancelReservation {
  display: inline-block;
  cursor: pointer;
  width: 80px;
}

.cancelReservation {
  color: #3363ff;
  font-size: 14px;
  text-align: center;
}

.details-mask .el-dialog__footer {
  position: absolute;
  bottom: 0;
  z-index: 10;
  padding: 0;
  width: 100%;
}

.details-mask .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 5px 0px 0px 15px;
  text-align: left;
}

.reservation-price {
  margin-bottom: 5px;
  color: #333;
}

.reservation-all {
  color: #999;
  font-size: 12px;
}

.reservation-group {
  display: flex;
}

.reservationBtn {
  cursor: pointer;
  padding: 15px 35px;
  color: #fff;
  font-size: 15px;
  text-align: center;
}

.save-btn {
  background: #dddddd;
}

.billing-btn {
  background: #3363ff;
}

/*****/

/*.cancelReservation .el-dialog__footer{*/

/*}*/

.reason-footer {
  box-sizing: border-box;
  padding: 30px 20px;
}

/* 到店预约收款-- 上门预约收款 */

/* 小票打印 */
.print-title {
  display: flex;
  box-sizing: border-box;
  background: #f6f6f6;
  padding: 15px;
  width: 100%;
}

.print-cancel,
.print-confirm {
  width: 50px;
  color: #3363ff;
}

.print-cancel {
  cursor: pointer;
  text-align: left;
}

.print-confirm {
  cursor: pointer;
  text-align: right;
}

.print-Text {
  flex: 1;
  font-size: 16px;
  text-align: center;
}

.print-main {
  box-sizing: border-box;
  padding: 0 15px;
  padding-bottom: 15px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}

.store-name {
  box-sizing: border-box;
  padding-top: 15px;
  font-weight: 600;
  font-size: 18px;
  text-align: center;
}

.print-info {
  max-height: 250px;
  overflow-y: auto;
}

/*.print-border {*/
/*border-bottom: 1px solid #F6F6F6;*/
/*margin-bottom: 15px;*/
/*}*/

.print-scanCode {
  width: 100%;
  text-align: center;
}

.rwm {
  display: inline-block;
  margin-bottom: 10px;
  width: 100px;
}

.print-date-li {
  display: flex;
}

.print-serialNumber {
  width: 30px;
  text-align: left;
}

.print-shop {
  flex: 1;
}

.print-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

/*发卡*/
.cardIssue-mask .el-dialog__header {
}

.cardIssueTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardIssueTitle > .el-icon-arrow-left {
  width: 128px;
}

.cardIssue-text {
  flex: 1;
  text-align: center;
}

.el-popup-parent--hidden {
  padding-right: 0 !important;
}

.serverPriceTable {
  margin-bottom: 30px;
}

.serverPriceTable .PriceTable-tr th,
.serverPriceTable .PriceTable-tr td {
  box-sizing: border-box;
  background: rgb(246, 246, 246);
  padding: 12px 10px;
  color: rgb(51, 51, 51);
  font-weight: 600;
  font-size: 15px;
}

.serverPriceTable .PriceTable-tr td {
  border-bottom: 1px solid #f6f6f6;
  background: #fff;
  font-weight: 400;
}

@media screen and (min-width: 1200px) {
  .reservation-choose-time {
    width: calc(100vw - 1180px);
    height: calc(100vh - 600px);
    overflow: hidden;
  }
}
@media screen and (max-width: 1200px) {
  .reservation-choose-time {
    width: calc(100vw - 450px);
    height: calc(100vh - 320px);
    overflow: hidden;
  }
}

.reservation-choose-time-ul {
  display: flex;
  flex-direction: row;
  cursor: default;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
}

.reservation-choose-time-ul-li {
  display: block;
  cursor: pointer;
  margin: 0 10px 2px 0;
  border-radius: 8px;
  padding: 10px;
  text-align: center;
}

.choose-time-active {
  border: 1px solid #c9c9c9;
  color: #3363ff;
}

.reservation-choose-time-ul::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  height: 8px;
  overflow: hidden;
}

.reservation-choose-time-ul::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  height: 8px;
}

.reservation-choose-time-ul::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.choose-time-body::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.choose-time-body::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  /*滚动条里面小方块*/
  width: 8px;
}

.choose-time-body::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.choose-time-body {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  height: 320px;
  overflow-x: hidden;
  overflow-y: auto;
  white-space: nowrap;
}

.choose-time-label {
  margin-bottom: 4px;
}

.choose-time-item {
  width: 20%;
  height: 80px;
  line-height: 80px;
  text-align: center;
}

.time-item-normal {
  cursor: pointer;
  font-weight: bold;
}

.time-item-active {
  color: #3363ff;
  font-weight: bold;
}

.time-item-expire {
  cursor: not-allowed;
  color: #cccccc;
}

.time-item-select {
  /*color: #cccccc;*/
  cursor: not-allowed;
  text-decoration: line-through;
}
/* 查看预约列表部分样式 */
.reservation_table_expand {
  font-size: 0;
}
.reservation_table_expand label {
  width: 90px;
  color: #99a9bf;
}
.reservation_table_expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}

.refundApply {
  margin: 20px -10px;
}

.elementImage img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
