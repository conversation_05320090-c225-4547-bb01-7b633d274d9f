.pay-wrap {
  will-change: transform, opacity;
  background: linear-gradient(180deg, #eaedf6 72%, #eeeaff 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 直接应用动画效果 */
.dialog-animation {
  animation: dialog-show 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

/* 关闭动画效果 */
.dialog-animation-close {
  animation: dialog-close 0.4s cubic-bezier(0.6, -0.28, 0.735, 0.045) forwards;
}

@keyframes dialog-show {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes dialog-close {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* 定义CSS自定义属性用于渐变过渡 */
@property --gradient-color-1 {
  syntax: "<color>";
  initial-value: #d6e0ff;
  inherits: false;
}

@property --gradient-color-2 {
  syntax: "<color>";
  initial-value: #e3dff8;
  inherits: false;
}

@property --gradient-angle {
  syntax: "<angle>";
  initial-value: 92deg;
  inherits: false;
}

@property --gradient-stop-1 {
  syntax: "<percentage>";
  initial-value: 23.41%;
  inherits: false;
}

@property --gradient-stop-2 {
  syntax: "<percentage>";
  initial-value: 100%;
  inherits: false;
}

/* 按钮边框渐变的CSS自定义属性 */
@property --btn-border-color-1 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --btn-border-color-2 {
  syntax: "<color>";
  initial-value: #c3cded;
  inherits: false;
}

@property --btn-border-color-3 {
  syntax: "<color>";
  initial-value: #cbbfff;
  inherits: false;
}

@property --btn-border-color-4 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

/* 内部按钮渐变的CSS自定义属性 */
@property --btn-inner-color-1 {
  syntax: "<color>";
  initial-value: #f8f8f8;
  inherits: false;
}

@property --btn-inner-color-2 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --btn-inner-color-3 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --btn-inner-stop-1 {
  syntax: "<percentage>";
  initial-value: 0%;
  inherits: false;
}

@property --btn-inner-stop-2 {
  syntax: "<percentage>";
  initial-value: 64%;
  inherits: false;
}

@property --btn-inner-stop-3 {
  syntax: "<percentage>";
  initial-value: 100%;
  inherits: false;
}

@property --paylist-border-color-1 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --paylist-border-color-2 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --paylist-border-color-3 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --paylist-border-color-4 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --paylist-inner-color-1 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --paylist-inner-color-2 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --paylist-inner-color-3 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --paylist-inner-stop-1 {
  syntax: "<percentage>";
  initial-value: 0%;
  inherits: false;
}

@property --paylist-inner-stop-2 {
  syntax: "<percentage>";
  initial-value: 51%;
  inherits: false;
}

@property --paylist-inner-stop-3 {
  syntax: "<percentage>";
  initial-value: 100%;
  inherits: false;
}

/* 列表项逐个出现的动画 */
.item-animation {
  transform: translateY(40px);
  opacity: 0;
  animation: item-fade-in 0.5s ease forwards;
}

@keyframes item-fade-in {
  0% {
    transform: translateY(40px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.f-pay-vl-box {
  width: 352px;
  height: calc(100% - 40px);
}

/* 添加平滑滚动效果 */
.f-pay-scroll-box {
  scroll-behavior: smooth;
  /* iOS平滑滚动 */
  -webkit-overflow-scrolling: touch;
}

.f-pay-money-box {
  background: linear-gradient(180deg, #f3f4f6 0%, #fff 80%);
}

.f-pay-title-1,
.f-pay-title-2,
.f-pay-title-3 {
  --fv-title-h: 60px;

  box-shadow:
    0px 14px 31px -20px rgba(11, 49, 166, 0.15),
    0px -14px 31px -20px rgba(11, 49, 166, 0.15);
}

.f-pay-title-1,
.f-pay-title-2 {
  position: sticky;
  height: var(--fv-title-h);
}

.f-pay-title-3 {
}

.f-pay-title-text {
  height: var(--fv-title-h);
}

.f-pay-title-2.is-sticky {
  box-shadow: 0px 14px 31px -20px rgba(11, 49, 166, 0.15);
}

.f-pay-title-1::before {
  position: absolute;
  bottom: 0px;
  left: 0;
  box-shadow:
    -50px 0px 40px -30px #fff inset,
    50px 0px 40px -30px #fff inset,
    0px 20px 30px -20px #ebeef7 inset;
  width: 100%;
  height: 2px;
  overflow: hidden;
  pointer-events: none;
  content: "";
}

.f-pay-title-2 {
  top: var(--fv-title-h);
  bottom: 0;
}

.f-pay-vl-box-item-animation {
  transform: translateX(100px);
  opacity: 0;
  animation: vl-box-item-fade-in 0.5s ease forwards;
}

.f-pay-vl-box-item-animation-lakala {
  transform: translateX(100px);
  opacity: 0;
  animation: vl-box-item-fade-in 0.3s ease forwards;
}

@keyframes vl-box-item-fade-in {
  0% {
    transform: translateX(100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.f-pay-vl-box-item-animation-lakala.f-pay-lakala-out {
  animation: vl-box-item-fade-out 0.2s ease-out forwards;
}

@keyframes vl-box-item-fade-out {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100px);
    opacity: 0;
  }
}

.f-pay-btn-box {
  --fv-border: 2px;
  --fv-radius: 12px;
  /* 添加平滑过渡效果 */
  transition:
    --btn-border-color-1 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-border-color-2 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-border-color-3 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-border-color-4 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.3s ease,
    transform 0.2s ease;

  cursor: pointer;
  box-shadow:
    0px -6px 10px -10px rgba(0, 0, 0, 0.25),
    0px 14px 15px -10px #d8def0;
  border-radius: var(--fv-radius);
  background: linear-gradient(
    180deg,
    var(--btn-border-color-1) 0%,
    var(--btn-border-color-2) 30%,
    var(--btn-border-color-3) 70%,
    var(--btn-border-color-4) 100%
  );
  padding: var(--fv-border);
}

.f-pay-btn-box:not(.is-disable):hover {
  /* 使用CSS自定义属性实现平滑渐变过渡 */
  --btn-border-color-1: #fff;
  --btn-border-color-2: #8ca0dc;
  --btn-border-color-3: #947ef4;
  --btn-border-color-4: #fff;

  box-shadow:
    0px -6px 10px -10px #385cffff,
    0px 14px 30px -15px #7b9cff;
}

.f-pay-btn-box.is-active:hover {
  box-shadow:
    0px -6px 10px -10px #2049ffff,
    0px 14px 30px -15px #4c79ffff;
}

.f-pay-btn-box:not(.is-disable):active,
.f-pay-btn-box.is-active:active {
  transform: translateY(2px);
  box-shadow:
    0px -3px 8px -10px #385cffff,
    0px 10px 20px -15px #7b9cff;
}

.f-pay-btn-box.is-active {
  box-shadow:
    0px -6px 10px -10px #385cffff,
    0px 14px 30px -15px #7b9cff;
}

.f-pay-btn-box .f-pay-btn-inner-box {
  --fv-height: 51px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* 添加平滑过渡效果 */
  transition:
    --btn-inner-color-1 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-inner-color-2 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-inner-color-3 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-inner-stop-1 0.3s ease,
    --btn-inner-stop-2 0.3s ease,
    --btn-inner-stop-3 0.3s ease,
    transform 0.2s ease;
  border-radius: calc(var(--fv-radius) - var(--fv-border));
  background: linear-gradient(
    90deg,
    var(--btn-inner-color-1) var(--btn-inner-stop-1),
    var(--btn-inner-color-2) var(--btn-inner-stop-2),
    var(--btn-inner-color-3) var(--btn-inner-stop-3)
  );
  height: var(--fv-height);

  overflow: hidden;
}

.f-pay-btn-box:not(.is-disable):hover .f-pay-btn-inner-box {
  /* 使用CSS自定义属性实现平滑渐变过渡 */
  --btn-inner-color-1: #e9edfa;
  --btn-inner-color-2: #ebebfa;
  --btn-inner-stop-1: 0%;
  --btn-inner-stop-2: 70%;
}

.f-pay-btn-box:not(.is-disable):hover .f-pay-btn-inner-box-text {
  text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
}

.f-pay-btn-box .f-pay-btn-inner-box .f-pay-btn-color-box {
  --fv-width: 128px;
  width: var(--fv-width);
  height: var(--fv-height);
}

.f-pay-btn-color-box-text {
  transition: all 0.4s ease-out;
  height: var(--fv-height);
  text-shadow:
    -1px -1px 2px #fff,
    1px 1px 2px rgba(12, 0, 105, 0.1);
}

.f-pay-btn-text-switch {
  transition: transform 0.5s cubic-bezier(0.82, 0.04, 0.36, 1.75);
}

.f-pay-btn-text-switch,
.f-pay-btn-text-switch-item {
  height: var(--fv-height);
}

.f-pay-btn-box:not(.is-disable):hover .f-pay-btn-color-box-text {
  color: #fff;
  text-shadow: 1px 1px 2px rgba(51, 99, 255, 0.66);
}

.f-pay-btn-color-box .f-pay-btn-color-inner {
  top: 0px;
  right: 0px;
  z-index: -1;
  filter: blur(15px);
  /* 添加平滑过渡效果 */
  transition:
    --gradient-color-1 0.6s cubic-bezier(0.4, 0, 0.2, 1),
    --gradient-color-2 0.6s cubic-bezier(0.4, 0, 0.2, 1),
    --gradient-angle 0.4s ease-out,
    --gradient-stop-1 0.5s ease-in-out,
    --gradient-stop-2 0.5s ease-in-out,
    filter 0.3s ease,
    transform 0.3s ease,
    width 0.3s ease,
    right 0.3s ease,
    height 0.3s ease,
    top 0.3s ease,
    left 0.3s ease,
    bottom 0.3s ease,
    opacity 0.3s ease,
    background 0.3s ease,
    border-radius 0.3s ease,
    box-shadow 0.3s ease,
    z-index 0.3s ease;
  border-radius: 100px;
  background: linear-gradient(
    var(--gradient-angle),
    var(--gradient-color-1) var(--gradient-stop-1),
    var(--gradient-color-2) var(--gradient-stop-2)
  );
  width: var(--fv-width);
  height: var(--fv-height);
}

.f-pay-btn-box:not(.is-disable):hover
  .f-pay-btn-color-box
  .f-pay-btn-color-inner {
  --fv-top: 3px;

  /* 使用CSS自定义属性实现平滑渐变过渡 */
  --gradient-color-1: #3363ff;
  --gradient-color-2: #775fdd;
  --gradient-angle: 95deg;
  --gradient-stop-1: 0%;
  --gradient-stop-2: 100%;
  top: -var(--fv-top);

  height: calc(var(--fv-height) + 2 * var(--fv-top));
}

.f-pay-btn-box:active .f-pay-btn-color-box .f-pay-btn-color-inner {
  right: -2px;
  width: calc(var(--fv-width) - 10px);
}

.f-pay-btn-box.is-active .f-pay-btn-color-box .f-pay-btn-color-inner {
  --gradient-color-1: #3363ff;
  --gradient-color-2: #775fdd;
  --gradient-stop-1: 0%;
  --gradient-stop-2: 73%;
  right: 0px;
  width: var(--fv-width);
}

.f-pay-btn-box.is-active .f-pay-btn-color-box-text {
  color: #fff;
  text-shadow: 1px 1px 2px rgba(51, 99, 255, 0.66);
}

.f-pay-btn-box.is-active .f-pay-btn-inner-box {
  /* 使用CSS自定义属性实现平滑渐变过渡 */
  --btn-inner-color-1: #e9edfa;
  --btn-inner-color-2: #ebebfa;
  --btn-inner-stop-1: 0%;
  --btn-inner-stop-2: 70%;
}

.f-pay-btn-box.is-disable {
  --btn-border-color-2: #cacaca;
  --btn-border-color-3: #cacaca;

  cursor: auto;
  box-shadow:
    0px -6px 10px -10px rgba(0, 0, 0, 0.25),
    0px 8px 15px -10px #d8def0;
}

.f-pay-btn-box.is-disable .f-pay-btn-inner-box {
  --btn-inner-color-1: #f8f8f8;
  --btn-inner-color-2: #ffffff;
  --btn-inner-color-3: #f4f4f4;
  --btn-inner-stop-2: 52%;
}

.f-pay-btn-box.is-disable .f-pay-btn-color-box .f-pay-btn-color-inner {
  --gradient-color-1: transparent;
  --gradient-color-2: transparent;
  --gradient-stop-1: 0%;
  --gradient-stop-2: 100%;
}

.f-pay-btn-box.is-disable .f-pay-btn-inner-box-text,
.f-pay-btn-box.is-disable .f-pay-btn-color-box-text {
  opacity: 0.4;
}

.pay-wrap-inner {
  transition: all 0.2s ease;
  width: 1160px;
}

.f-pay-pagination {
  box-shadow: 0px -14px 31px -20px rgba(11, 49, 166, 0.15);
}

.f-pay-paylist-item-box {
  --fv-paylist-border: 1px;
  --fv-paylist-radius: 12px;
  /* 添加平滑过渡效果 */
  transition:
    --btn-border-color-1 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-border-color-2 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-border-color-3 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-border-color-4 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.3s ease,
    transform 0.2s ease;

  cursor: pointer;
  border-radius: var(--fv-paylist-radius);
  background: linear-gradient(
    180deg,
    var(--paylist-border-color-1) 0%,
    var(--paylist-border-color-2) 30%,
    var(--paylist-border-color-3) 70%,
    var(--paylist-border-color-4) 100%
  );
  padding: var(--fv-paylist-border);
}

.f-pay-paylist-item-box-inner {
  --fv-paylist-height: 51px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  /* 添加平滑过渡效果 */
  transition:
    --paylist-inner-color-1 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --paylist-inner-color-2 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --paylist-inner-color-3 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --paylist-inner-stop-1 0.3s ease,
    --paylist-inner-stop-2 0.3s ease,
    --paylist-inner-stop-3 0.3s ease,
    box-shadow 0.3s ease,
    transform 0.2s ease;
  box-shadow: 0px 1px 2px -1px #fff;
  border-radius: calc(var(--fv-paylist-radius) - var(--fv-paylist-border));
  background: linear-gradient(
    90deg,
    var(--paylist-inner-color-1) var(--paylist-inner-stop-1),
    var(--paylist-inner-color-2) var(--paylist-inner-stop-2),
    var(--paylist-inner-color-3) var(--paylist-inner-stop-3)
  );
  height: var(--fv-paylist-height);

  overflow: hidden;
}

.f-pay-paylist-item-box:hover {
  --paylist-border-color-1: #fff;
  --paylist-border-color-2: #8ca0dc;
  --paylist-border-color-3: #947ef4;
  --paylist-border-color-4: #fff;

  box-shadow: 0px 1px 2px -1px #947ef4;
}

.f-pay-paylist-item-box:hover .f-pay-paylist-item-box-inner {
  --paylist-inner-color-1: #fafafa;
  --paylist-inner-color-2: #fff;
  --paylist-inner-color-3: #f8f8f8;
}

.f-pay-paylist-item-box.is-active {
  --paylist-border-color-1: #fff;
  --paylist-border-color-2: #8ca0dc;
  --paylist-border-color-3: #947ef4;
  --paylist-border-color-4: #fff;

  box-shadow:
    0px 1px 2px -1px #947ef4,
    0px 10px 8px -10px rgba(0, 0, 0, 0.1);
}

.f-pay-paylist-item-box.is-active .f-pay-paylist-item-box-inner {
  --paylist-inner-color-1: #f8f8f8;
  --paylist-inner-color-2: #fff;
  --paylist-inner-color-3: #eaecfa;
}

.f-pay-paying-mask {
  backdrop-filter: blur(3.9000000953674316px);
  will-change: transform, opacity;
  background: rgba(255, 255, 255, 0.5);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  animation: dialog-show 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.f-pay-paying-text {
  box-shadow: 0px 4px 30px 24px #fff;
  border-radius: 30px;
  background: #fff;
}

.f-success-box {
  --size: 50px;
  display: block;
  position: relative;
  transform: scale(0);
  z-index: 1;
  animation: success-box-show 0.2s ease-out forwards;
  animation-delay: 0.2s;
  border-radius: 50%;
  background-image: linear-gradient(92deg, #3363ff 23.41%, #775fdd 100%);
  width: var(--size);
  height: var(--size);
  overflow: hidden;
}

@keyframes success-box-show {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.tick_mark {
  position: absolute;
  top: -1px;
  right: 0;
  left: calc(var(--size) * -0.05);
  transform: rotateZ(-40deg);
  margin: 0 auto;
  margin-left: calc(var(--size) * 0.14);
  width: calc(var(--size) * 0.6);
  height: calc(var(--size) * 0.6);
}

.tick_mark:before,
.tick_mark:after {
  position: absolute;
  border-radius: 2px;
  background-color: #fff;
  content: "";
}

.tick_mark:before {
  bottom: 0;
  left: 0;
  transform: translateY(calc(var(--size) * -0.68));
  animation: tick-mark-show-before 0.2s ease-in-out forwards;
  animation-delay: 0.4s;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.23);
  width: calc(var(--size) * 0.1);
  height: calc(var(--size) * 0.3);
}

.tick_mark:after {
  bottom: 0;
  left: 0;
  transform: translateX(calc(var(--size) * 0.78));
  animation: tick-mark-show-after 0.2s ease-in-out forwards;
  animation-delay: 0.4s;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.23);
  width: 100%;
  height: calc(var(--size) * 0.1);
}

@keyframes tick-mark-show-before {
  0% {
    transform: translateY(calc(var(--size) * -0.68));
  }
  100% {
    transform: translate(0);
  }
}
@keyframes tick-mark-show-after {
  0% {
    transform: translateX(calc(var(--size) * 0.78));
  }
  100% {
    transform: translate(0);
  }
}
