div {
    -moz-user-select: none;
    /*mozilar*/
    -webkit-user-select: none;
    /*webkit*/
    -ms-user-select: none;
    /*IE*/
    user-select: none;
}

.menu {
    height: 60px;
    background-color: #2B282C;
    display: flex;
}

.menu_font {
    width: 80%;
    display: flex;
    margin-left: 100px;
}

.menu_font .menu_font_nav {
    width: 20%;
    min-width: 120px;
    height: 100%;
    color: #FFFFFF;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.addclass {
    background-color: #3363FF;
}

.cashier {
    background: #2B282C;
    opacity: 0.95;
    height: 60px;
    padding: 0% 10% 0% 10%;
    display: flex;
    justify-content: space-around;
}

.cashier .li1 {
    display: flex;
    justify-content: center;
    align-items: center;
}

.li1_img {
    width: 40px;
    height: 40px;
}

.cashier .li2 {
    color: white;
    font-size: 16px;
    text-align: center;
    line-height: 60px;
}


/*以上是导航条样式*/

.main {
    display: flex;
}

.tz_left {
    box-sizing: border-box;
    padding: 0 15px;
    width: 45%;
    height:100vh;
    overflow-y: auto;
    border-right: 3px solid #F6F6F6;
}

.tz_left::-webkit-scrollbar {
    width: 8px;
    overflow: hidden;
}

.tz_left::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    width: 8px;
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
    background: rgba(0, 0, 0, 0.1);
}

.tz_left::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px #eee;
    border-radius: 0;
    background: #fff;
}

.tz_left1 {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 20px 10px 5px 20px;
    overflow: auto;
    border-bottom: 1px solid #E5E5E5;
    cursor: pointer;
}

.listActive{
    border: 1px solid #3363FF!important;
}

.tz_left1_line1 {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
}

.tz_left1_line1_font1 {
    font-size: 16px;
    font-weight: 400;
    color: #333333;
}

.tz_left1_line1_font2 {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
}

.tz_left1_line2 {
    font-size: 12px;
    font-weight: 400;
    color: #666666;
    display: flex;
    flex-direction: column;
}

.tz_left1_line2_font1,
.tz_left1_line2_font2,
.tz_left1_line2_font3 {
    flex-basis: auto;
    font-size: 14px;
}
.tz_left1_line2_font3{
    white-space: nowrap;
    overflow: hidden;
    text-overflow:ellipsis;
}
.tz_left1_line2_font4 {
    line-height: 20px;
}

.tz_right_neirong_font2 {
    padding: 20px  0px;
    font-size: 16px;
    color: #333;
}

.tz_right_neirong_font2>div:first-child{
    border-top: 1px solid #cccccc;
}

.tz_right_neirong_font22 {
    padding-left: 53px;
    line-height: 40px;
    /* border-bottom: 1px solid #cccccc; */
}

.tz_right {
    width: 100%;
    background: #f6f6f6;
}

.tz_right_title {
    padding: 20px 10px;
    display: flex;
    background: white;
}

.tz_right_neirong {
    padding: 20px 10px;
    background: white;
}

.tz_right_title_font1 {
    font-weight: 400;
    font-size: 16px;
    color: #999999;
}

.tz_right_title_font2 {
    margin-left: 20px;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
}

.tz_right_neirong_font1 {
    font-weight: 400;
    font-size: 16px;
    color: #999999;
}

.getMoreBtn {
    display: block;
    margin: 50px auto;
}

.loadMoreBtn {
    font-size: 14px;
    color: #333;
    margin: 20px 0;
    margin-left: 36%;
}


/* 滚动条样式 */

.notice-body {
    height: calc(100vh - 60px);
}
.Notice-details {
    height: 60px;
    background: #fff;
    line-height: 60px;
    text-align: center;
    color: #666;
    cursor: pointer;
}